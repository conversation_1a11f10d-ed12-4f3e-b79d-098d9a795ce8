{"name": "rex-construction-cms", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint-fix": "eslint --fix \"src/**/*.{ts,tsx}\"", "pretty": "prettier --write \"src/**/*.{ts,tsx}\"", "prepare": "husky install", "precommit": "npm run lint-fix && npm run pretty", "prepush": "npm run lint", "preview": "vite preview", "format": "prettier --write .", "lint": "eslint . --fix", "lint:check": "eslint .", "prettier:check": "prettier --check ."}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@popperjs/core": "^2.11.8", "@reduxjs/toolkit": "^2.5.0", "@tailwindcss/postcss": "^4.0.9", "@tailwindcss/vite": "^4.0.7", "@tanstack/react-table": "^8.21.2", "axios": "^1.7.9", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "i18next": "^24.2.0", "i18next-browser-languagedetector": "^8.0.2", "i18next-http-backend": "^3.0.1", "moment": "^2.30.1", "motion": "^12.4.10", "path": "^0.12.7", "postcss": "^8.5.3", "react": "^18.3.1", "react-datepicker": "^8.2.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-i18next": "^15.2.0", "react-markdown": "^10.1.0", "react-query": "^3.39.3", "react-quill": "^2.0.0", "react-redux": "^9.2.0", "react-router": "^7.1.0", "react-select": "^5.10.0", "react-switch": "^7.1.0", "react-tailwindcss-datepicker": "^2.0.0", "recharts": "^2.15.3", "redux-persist": "^6.0.0", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "styled-components": "^6.1.13", "tailwindcss": "^4.0.9", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/crypto-js": "^4.2.2", "@types/node": "^22.10.2", "@types/react": "^18.3.17", "@types/react-datepicker": "^7.0.0", "@types/react-dom": "^18.3.5", "@typescript-eslint/eslint-plugin": "^8.26.0", "@typescript-eslint/parser": "^8.26.0", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^9.21.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.13.0", "husky": "^8.0.3", "prettier": "^3.5.2", "prettier-plugin-tailwindcss": "^0.6.11", "typescript": "~5.6.2", "typescript-eslint": "^8.18.1", "vite": "^6.0.3"}}