{"root": ["./src/main.tsx", "./src/vite-env.d.ts", "./src/Components/index.ts", "./src/Components/Common/index.ts", "./src/Components/Common/ActionButtons/index.tsx", "./src/Components/Common/FormError/index.tsx", "./src/Components/Common/HeaderButton/index.tsx", "./src/Components/Common/MenuItem/index.tsx", "./src/Components/Common/RightSidebar/index.tsx", "./src/Components/Common/Sidebar/index.tsx", "./src/Components/Layout/ActionBarLayout.tsx", "./src/Components/Layout/AuthLayout.tsx", "./src/Components/Layout/ContentLayout.tsx", "./src/Components/Layout/MainLayout.tsx", "./src/Components/Layout/MainLoader.tsx", "./src/Components/Layout/ProfileLayout.tsx", "./src/Components/UI/index.ts", "./src/Components/UI/AccessibleImage/index.tsx", "./src/Components/UI/Breadcrumb/index.tsx", "./src/Components/UI/Button/index.tsx", "./src/Components/UI/Checkbox/index.tsx", "./src/Components/UI/CustomRadioButton/index.tsx", "./src/Components/UI/DatePicker/index.tsx", "./src/Components/UI/Divider/index.tsx", "./src/Components/UI/Error/index.tsx", "./src/Components/UI/InputField/TextAreaField.tsx", "./src/Components/UI/InputField/index.tsx", "./src/Components/UI/Loader/index.tsx", "./src/Components/UI/Modal/index.tsx", "./src/Components/UI/Pagination/index.tsx", "./src/Components/UI/Popover/index.tsx", "./src/Components/UI/ProfileImageUploader/index.tsx", "./src/Components/UI/Progressbar/index.tsx", "./src/Components/UI/SelectPicker/index.tsx", "./src/Components/UI/Skeleton/index.tsx", "./src/Components/UI/Switch/index.tsx", "./src/Components/UI/Table/FilterPopover.tsx", "./src/Components/UI/Table/SelectFilter.tsx", "./src/Components/UI/Table/index.tsx", "./src/Components/UI/Toast/ToastProvider.tsx", "./src/Components/UI/Toast/index.tsx", "./src/Components/UI/Tooltip/index.tsx", "./src/Components/UI/VIewMoreText/index.tsx", "./src/Components/UI/VideoUploader/index.tsx", "./src/Config/LargeFileUpload.config.ts", "./src/Config/MenuItems.config.ts", "./src/Config/Path.Config.ts", "./src/Config/Routes.Config.ts", "./src/Config/UploadEvents.config.ts", "./src/Config/uploadState.ts", "./src/Helpers/Api.ts", "./src/Helpers/CacheKeys.ts", "./src/Helpers/Constants.ts", "./src/Helpers/QueryClient.ts", "./src/Helpers/Roleguard.tsx", "./src/Helpers/StatusCodes.ts", "./src/Helpers/Utils.ts", "./src/Hooks/useAppDispatch.ts", "./src/Hooks/useDebounce.ts", "./src/Hooks/useHasAccess.ts", "./src/Hooks/useLocalStorage.ts", "./src/Hooks/usePathnameAndQuery.ts", "./src/Hooks/useShortcutKeys.ts", "./src/Icons/index.ts", "./src/Icons/AddIcon/index.tsx", "./src/Icons/AdminIcon/index.tsx", "./src/Icons/ArrowLeftIcon/index.tsx", "./src/Icons/AscSortIcon/index.tsx", "./src/Icons/BackIcon/index.tsx", "./src/Icons/BellIcon/index.tsx", "./src/Icons/CalenderIcon/index.tsx", "./src/Icons/CaretRightIcon/index.tsx", "./src/Icons/CategoryIcon/index.tsx", "./src/Icons/ChartLineUpIcon/index.tsx", "./src/Icons/CheckIcon/index.tsx", "./src/Icons/CircleTickIcon/index.tsx", "./src/Icons/ClockCounterwiseIcon/index.tsx", "./src/Icons/ClockIcon/index.tsx", "./src/Icons/CloseEyeIcon/index.tsx", "./src/Icons/CloseIcon/index.tsx", "./src/Icons/CloudUploadIcon/index.tsx", "./src/Icons/CopyIcon/index.tsx", "./src/Icons/DatabaseIcon/index.tsx", "./src/Icons/DeleteAccIcon/index.tsx", "./src/Icons/DeleteIcon/index.tsx", "./src/Icons/DollarIcon/index.tsx", "./src/Icons/DownloadIcon/index.tsx", "./src/Icons/DscSortIcon/index.tsx", "./src/Icons/EditIcon/index.tsx", "./src/Icons/EmailIcon/index.tsx", "./src/Icons/ErrorIcon/index.tsx", "./src/Icons/ExportIcon/index.tsx", "./src/Icons/EyeIcon/index.tsx", "./src/Icons/FileIcon/index.tsx", "./src/Icons/FilterIcon/index.tsx", "./src/Icons/FormsAndDocumentsIcon/index.tsx", "./src/Icons/HouseIcon/index.tsx", "./src/Icons/IgnoreIcon/index.tsx", "./src/Icons/InfoIcon/index.tsx", "./src/Icons/KeyIcon/index.tsx", "./src/Icons/LicenceApproveIcon/index.tsx", "./src/Icons/Loader/index.tsx", "./src/Icons/LocationIcon/Location2.tsx", "./src/Icons/LocationIcon/index.tsx", "./src/Icons/LogoutIcon/index.tsx", "./src/Icons/MagnifyGlassIcon/index.tsx", "./src/Icons/MicIcon/index.tsx", "./src/Icons/MinusIcon/index.tsx", "./src/Icons/NoSortIcon/index.tsx", "./src/Icons/OrgIcon/index.tsx", "./src/Icons/PersonIcon/index.tsx", "./src/Icons/PhoneIcon/index.tsx", "./src/Icons/PlayIcon/index.tsx", "./src/Icons/QuestionsIcon/index.tsx", "./src/Icons/QuizIcon/index.tsx", "./src/Icons/ReportIcon/index.tsx", "./src/Icons/RequestIcon/index.tsx", "./src/Icons/RoboIcon/index.tsx", "./src/Icons/SearchIcon/index.tsx", "./src/Icons/SettingIcon/index.tsx", "./src/Icons/SimpleTickIcon/index.tsx", "./src/Icons/SubscriptionIcon/index.tsx", "./src/Icons/TemplatesIcon/index.tsx", "./src/Icons/ThreeDotIcon/index.tsx", "./src/Icons/ThumbnailIcon/index.tsx", "./src/Icons/UploadIcon/index.tsx", "./src/Icons/UserCircleIcon/index.tsx", "./src/Icons/UsersIcon/index.tsx", "./src/Icons/VideoFileIcon/index.tsx", "./src/Icons/VideoIcon/index.tsx", "./src/Icons/WarningIcon/index.tsx", "./src/LanguageProvider/i18n.ts", "./src/Pages/404/index.tsx", "./src/Pages/AdminManagement/index.tsx", "./src/Pages/AdminManagement/AddSubadmin/index.tsx", "./src/Pages/AdminManagement/EditSubAdmin/index.tsx", "./src/Pages/Auth/ForgetPassword.tsx", "./src/Pages/Auth/Login.tsx", "./src/Pages/Auth/ResetPassword.tsx", "./src/Pages/ContactRequests/index.tsx", "./src/Pages/Dashboard/ChatList.tsx", "./src/Pages/Dashboard/ChatView.tsx", "./src/Pages/Dashboard/IssueList.tsx", "./src/Pages/Dashboard/RecChatList.tsx", "./src/Pages/Dashboard/index.tsx", "./src/Pages/InternetConnectionMonitor/index.tsx", "./src/Pages/Knowledgebase/index.tsx", "./src/Pages/LicenceApproval/index.tsx", "./src/Pages/Notifications/EmailTemplates.tsx", "./src/Pages/Notifications/NotificationPreview.tsx", "./src/Pages/Notifications/NotificationTemplates.tsx", "./src/Pages/Profile/index.tsx", "./src/Pages/RoleManagement/index.tsx", "./src/Pages/Settings/ChangePassword.tsx", "./src/Pages/StaticPage/index.tsx", "./src/Pages/Subscription/index.tsx", "./src/Pages/Subscription/SubscriptionModel/index.tsx", "./src/Pages/TradieManagement/AddCategory.tsx", "./src/Pages/TradieManagement/index.tsx", "./src/Pages/TradiesHub/PostReport.tsx", "./src/Pages/TradiesHub/index.tsx", "./src/Pages/TradiesHub/Category/AddCategory.tsx", "./src/Pages/TradiesHub/Category/index.tsx", "./src/Pages/TradiesHub/Comments/CommentReports.tsx", "./src/Pages/TradiesHub/Comments/index.tsx", "./src/Pages/TradiesHub/Reply/ReplyReports.tsx", "./src/Pages/TradiesHub/Reply/index.tsx", "./src/Pages/TradiesHub/Reports/index.tsx", "./src/Pages/TransactionHistory/index.tsx", "./src/Pages/Users/<USER>", "./src/Pages/Users/<USER>/index.tsx", "./src/Pages/Users/<USER>/index.tsx", "./src/Query/Hooks/useAdmin.ts", "./src/Query/Hooks/useAnalytics.ts", "./src/Query/Hooks/useAuth.ts", "./src/Query/Hooks/useContactUs.ts", "./src/Query/Hooks/useKnowledgebase.ts", "./src/Query/Hooks/useLicenceApproval.ts", "./src/Query/Hooks/useNotification.ts", "./src/Query/Hooks/useRolePermission.ts", "./src/Query/Hooks/useStaticPage.ts", "./src/Query/Hooks/useSubscription.ts", "./src/Query/Hooks/useTradiesHub.ts", "./src/Query/Hooks/useUser.ts", "./src/Query/Hooks/userTrade.ts", "./src/Query/Services/admin.service.ts", "./src/Query/Services/analytics.service.ts", "./src/Query/Services/auth.service.ts", "./src/Query/Services/contactUs.service.ts", "./src/Query/Services/knowledgebase.service.ts", "./src/Query/Services/licenceApproval.service.ts", "./src/Query/Services/notification.service.ts", "./src/Query/Services/rolePermission.service.ts", "./src/Query/Services/staticPage.service.ts", "./src/Query/Services/subscription.service.ts", "./src/Query/Services/trade.service.ts", "./src/Query/Services/tradiesHub.service.ts", "./src/Query/Services/user.service.ts", "./src/Redux/Reducers.ts", "./src/Redux/store.ts", "./src/Redux/General/Toast.ts", "./src/Redux/SystemControl/PermissionControl.ts", "./src/Redux/SystemControl/PostControl.ts", "./src/Redux/SystemControl/index.ts", "./src/Routes/index.tsx"], "version": "5.6.3"}