# Rex-Tredie <PERSON>MS
# Project created with Vite + React + TypeScript

## Getting Started

development/staging live url paste here

### Prerequisites

1. Node:  v18.20.4
2. React: v18.0.0
3. Visual Studio Code

### Clone 
https://gitlab.openxcell.dev/rextradie/rex-tradie-cms.git

Cloning and Running the Application in local

Clone the project into local

Install all the npm packages. Go into the project folder and type the following command to install all npm packages

npm install

In order to run the application Type the following command

npm start

for Local run

npm run dev

for build 

npm run build

The Application Runs on localhost:5173

## HTTP client

axios library is used to make HTTP Calls

## Resources

vite react app : The following commands that can be used for create vite app
npm create vite@latest
https://vite.dev/guide/

ReactJS : Refer to https://reactjs.org/ to understand the concepts of ReactJS

Tailwind: https://tailwindcss.com/docs/installation/using-vite

Redux Toolkit: https://redux-toolkit.js.org/

React query: https://tanstack.com/query/latest/docs/framework/react/overview

# Folder Structure 
    | src
    |-- Assets
    |-- Components
    |-- Helpers
    |   | Api.ts          #Global Api Handler 
    |   | CacheKeys.ts    #cachekeys for react query    
    |   | Constants.ts    #constants values like api paths
    |   | Paths.ts        #globally all pages path 
    |   | QueryClient.ts  #query client config for 
    |   | StatusCodes.ts  #status code for apis
    |   | Utils.ts        #miscellaneous functions    
    |-- Hooks
    |-- LanguageProvider #globally language changer for system  
    |-- Pages #web pages for system
    |-- Query #react query setup (if you wanna use react query)
    |-- Redux #redux setup (if you wanna use redux)
    |-- Routes #routes setup for cms

# Note: 
  When you create new folder in src, after the folder creation need to register that folder via vite.config.ts -> resolve -> alias & tsconfig.json

