import { InputField } from '@Components/UI';
import { t } from 'i18next';
import {
  Control,
  Controller,
  FieldErrors,
  UseFormHandleSubmit,
  UseFormReset,
} from 'react-hook-form';

interface UserEditFormValues {
  first_name: string;
  last_name: string;
  email: string;
}

interface EditSubAdminProps {
  control: Control<UserEditFormValues>;
  reset: UseFormReset<UserEditFormValues>;
  handleSubmit: UseFormHandleSubmit<UserEditFormValues>;
  errors: FieldErrors<UserEditFormValues>;
  onSubmit: (data: UserEditFormValues) => void;
}

function EditUserManagement({
  control,
  reset,
  handleSubmit,
  errors,
  onSubmit,
}: EditSubAdminProps) {
  return (
    <div className="flex flex-col gap-4 max-w-lg">
      <form onSubmit={handleSubmit(onSubmit)} onReset={() => reset()}>
        <Controller
          control={control}
          name="first_name"
          render={({ field }) => (
            <InputField
              label={t('profile.fname')}
              placeholder={t('profile.fFname')}
              field={field}
              type="text"
              errorMessage={errors?.first_name?.message}
            />
          )}
        />
        <Controller
          control={control}
          name="last_name"
          render={({ field }) => (
            <InputField
              label={t('profile.lname')}
              placeholder={t('profile.fLname')}
              field={field}
              type="text"
              errorMessage={errors?.last_name?.message}
            />
          )}
        />
        <Controller
          control={control}
          name="email"
          render={({ field }) => (
            <InputField
              label={t('profile.email')}
              placeholder={t('profile.fEmail')}
              disabled={true}
              field={field}
              type="text"
              errorMessage={errors?.email?.message}
            />
          )}
        />
      </form>
    </div>
  );
}

export default EditUserManagement;
