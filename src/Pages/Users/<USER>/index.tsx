import { useEffect, useMemo } from 'react';
import { <PERSON>ton, InputField, InputSelect } from '@Components/UI';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { COUNTRY_CODES } from '@Helpers/Utils';
import clsx from 'clsx';

import { useToast } from '@Components/UI/Toast/ToastProvider';
import { useCreateUser, useStateList, useTradeList } from '@Query/Hooks/useUser';
import PATHS from '@Config/Path.Config';
import { t } from 'i18next';
import { useNavigate } from 'react-router';

// Default values for country and country code
const DEFAULT_COUNTRY = { value: '1', label: 'Australia' };
const DEFAULT_COUNTRY_CODE = {
  label: '+61',
  value: '+61',
  flag: 'https://flagcdn.com/w320/au.png',
};

// Interface for select options
interface SelectOption {
  label: string;
  value: string;
  [key: string]: unknown;
}

interface StateData {
  id: number;
  name: string;
}

// Interface for user data
interface UserData {
  id?: number;
  first_name: string;
  last_name: string;
  email: string;
  mobile_no: string;
  state?: SelectOption;
  trade?: SelectOption;
  statesList?: StateData[];
  tradesList?: StateData[];
  country_code?: string;
}

// Component props interface
interface AddUsersProps {
  isEdit?: boolean;
  userData?: UserData;
  handleSuccess?: () => void;
}

// Form Validation Schema
const schema = yup.object().shape({
  firstName: yup
    .string()
    .trim('Cannot include leading and trailing spaces')
    .strict(true)
    .required('First name is required')
    .min(3, 'Name must have at least 3 characters')
    .max(50, 'Name must have at most 50 characters'),
  lastName: yup
    .string()
    .trim('Cannot include leading and trailing spaces')
    .strict(true)
    .required('Last name is required')
    .min(1, 'Name must have at least 1 character')
    .max(50, 'Name must have at most 50 characters'),
  email: yup
    .string()
    .required('Email is required')
    .matches(
      /^(?!.*\.\.)[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      'Please enter a valid email'
    )
    .max(150, t('authentication.validation.emailMaxLength')),
  phoneNumber: yup
    .string()
    .matches(/^\d{8,12}$/, 'Enter a valid mobile number')
    .required('Mobile number is required'),
  // FIX: Explicitly define the shape for SelectOption fields
  countryCode: yup
    .object()
    .shape({
      label: yup.string().required(),
      value: yup.string().required(),
    })
    .required('Required')
    .typeError('Country code is required'), // Added a typeError for better user feedback
  country: yup
    .object()
    .shape({
      label: yup.string().required(),
      value: yup.string().required(),
    })
    .required('Country is required')
    .typeError('Country is required'), // Added a typeError
  state: yup
    .object()
    .shape({
      label: yup.string().required(),
      value: yup.string().required(),
    })
    .required('State is required')
    .typeError('State is required'), // Added a typeError
  trade: yup
    .object()
    .shape({
      label: yup.string().required(),
      value: yup.string().required(),
    })
    .required('Trade is required')
    .typeError('Trade is required'), // Added a typeError
});

// Interface for form values
interface RegisterFormValues {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  countryCode: SelectOption;
  country: SelectOption;
  state: SelectOption;
  trade: SelectOption;
}

// Interface for API request data
interface RegisterRequestData {
  id?: number;
  first_name: string;
  last_name: string;
  email: string;
  mobile: string;
  country_code: string; // Changed to string as you are sending formData.countryCode.value
  country: number;
  state: number;
  trade: number;
  [key: string]: unknown;
}

const defaultUserData: UserData = {
  first_name: '',
  last_name: '',
  email: '',
  mobile_no: '',
};

const AddUsers = ({
  isEdit = false,
  userData = defaultUserData,
  handleSuccess = () => { },
}: AddUsersProps): JSX.Element => {
  const { addToast } = useToast();
  const navigate = useNavigate();

  const {
    mutate: createItem,
    isError,
    error,
    isLoading,
    isSuccess,
    data,
  } = useCreateUser(isEdit);
  const { data: stateListData, isLoading: stateListLoading } = useStateList(!isEdit);
  const { data: tradeListData, isLoading: tradeListLoading } = useTradeList(!isEdit);

  const statesOptions = useMemo(() => {
    return isEdit && userData?.statesList
      ? userData?.statesList.map((state: StateData) => ({
        label: state.name,
        value: state.id.toString(),
      }))
      : stateListData?.data?.data?.map((state: StateData) => ({
        label: state.name,
        value: state.id.toString(),
      })) || [];
  }, [isEdit, userData, stateListData]);

  const tradeOptions = useMemo(() => {
    return isEdit && userData?.tradesList
      ? userData?.tradesList.map((trade: StateData) => ({
        label: trade.name,
        value: trade.id.toString(),
      }))
      : tradeListData?.data?.data?.map((trade: StateData) => ({
        label: trade.name,
        value: trade.id.toString(),
      })) || [];
  }, [isEdit, userData, tradeListData]);

  // Handle successful API call
  useEffect(() => {
    if (isError) {
      addToast('error', error as string);
    }
    if (isSuccess) {
      addToast('success', data?.data?.message);
      handleSuccess();
      // If not editing, navigate to users page
      if (!isEdit) {
        navigate(PATHS.USERS);
      }
    }
  }, [isSuccess, isError, data, addToast, error, isEdit, navigate]);

  // Form setup
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<RegisterFormValues>({
    resolver: yupResolver(schema),
    defaultValues: {
      firstName: userData?.first_name || '',
      lastName: userData?.last_name || '',
      email: userData?.email || '',
      phoneNumber: userData?.mobile_no || '',
      countryCode: userData?.country_code
        ? {
          label: userData?.country_code,
          value: userData?.country_code,
        }
        : DEFAULT_COUNTRY_CODE,
      country: DEFAULT_COUNTRY,
      state: userData?.state || { label: '', value: '' }, // Initialize with empty object or valid default
      trade: userData?.trade || { label: '', value: '' }, // Initialize with empty object or valid default
    },
    mode: 'onChange',
  });

  // Form submission handler
  const onSubmit = async (formData: RegisterFormValues): Promise<void> => {
    const requestData: RegisterRequestData = {
      first_name: formData.firstName,
      last_name: formData.lastName,
      email: formData.email,
      mobile: formData.phoneNumber,
      country_code: formData.countryCode.value, // Accessing the value property
      country: parseInt(formData.country.value, 10),
      state: parseInt(formData.state.value, 10),
      trade: parseInt(formData.trade.value, 10),
    };

    if (isEdit) {
      requestData.id = userData?.id;
    }

    createItem(requestData);
  };

  return (
    <form
      className={clsx('grid grid-cols-6 gap-4 pt-5')}
      onSubmit={handleSubmit(onSubmit)}
      autoComplete="off"
    >
      <div className="col-span-2">
        <Controller
          name="firstName"
          control={control}
          render={({ field }) => (
            <InputField
              label="First Name"
              field={field}
              placeholder="First Name"
              errorMessage={errors.firstName?.message}
              autoFocus
            />
          )}
        />
      </div>
      <div className="col-span-2">
        <Controller
          name="lastName"
          control={control}
          render={({ field }) => (
            <InputField
              label="Last Name"
              field={field}
              placeholder="Last Name"
              errorMessage={errors.lastName?.message}
            />
          )}
        />
      </div>
      <div className="col-span-2">
        <Controller
          name="email"
          control={control}
          render={({ field }) => (
            <InputField
              label="Email"
              field={field}
              placeholder="Enter your email"
              errorMessage={errors.email?.message}
            />
          )}
        />
      </div>
      <div className="relative mt-6 col-span-3">
        <div className="flex gap-x-2 items-end">
          <label
            className={clsx(
              'text-sm font-medium text-left absolute -top-5 text-gray-700'
            )}
          >
            Mobile Number
          </label>
          <div className="w-[108px]">
            <Controller
              name="countryCode"
              control={control}
              render={({ field }) => (
                <InputSelect
                  label={true}
                  field={field}
                  placeholder="+61"
                  containerClassName="pt-1 !gap-1"
                  errorMessage={
                    errors.countryCode?.message
                      ? String(errors.countryCode.message)
                      : undefined
                  }
                  options={COUNTRY_CODES}
                />
              )}
            />
          </div>

          <div className="w-full">
            <Controller
              name="phoneNumber"
              control={control}
              render={({ field }) => (
                <InputField
                  label={true}
                  containerClassName="pt-0 !gap-1.5"
                  field={field}
                  placeholder="Enter your mobile number"
                  errorMessage={errors.phoneNumber?.message}
                />
              )}
            />
          </div>
        </div>
      </div>
      <div className="col-span-3">
        <Controller
          name="country"
          control={control}
          render={({ field }) => (
            <InputSelect
              label={'Country'}
              field={field}
              placeholder="Select country"
              paddingBlock="6px"
              disabled={true} // As per your default country setting
              containerClassName="pt-1 !gap-1.5"
              errorMessage={
                errors.country?.message
                  ? String(errors.country.message)
                  : undefined
              }
              options={[{ value: '1', label: 'Australia' }]} // Only Australia as default
            />
          )}
        />
      </div>
      <div className="col-span-3">
        <Controller
          name="state"
          control={control}
          render={({ field }) => (
            <InputSelect
              label={'State'}
              field={field}
              containerClassName="pt-0"
              placeholder="Select your state"
              errorMessage={
                errors.state?.message ? String(errors.state.message) : undefined
              }
              options={statesOptions}
              isLoading={!isEdit && stateListLoading}
            />
          )}
        />
      </div>

      <div className="col-span-3">
        <Controller
          name="trade"
          control={control}
          render={({ field }) => (
            <InputSelect
              label={'Trade'}
              field={field}
              containerClassName="pt-0"
              placeholder="Select your trade"
              errorMessage={
                errors.trade?.message ? String(errors.trade.message) : undefined
              }
              options={tradeOptions}
              isLoading={!isEdit && tradeListLoading}
            />
          )}
        />
      </div>

      <div
        className={clsx(
          'col-span-6 flex',
          isEdit ? 'justify-end' : 'justify-between'
        )}
      >
        {!isEdit && (
          <Button
            text="Cancel"
            type="reset"
            className="mt-2"
            onClick={() => handleSuccess()}
            variant="outline"
            disabled={isLoading}
            width="w-[182px]"
          />
        )}

        <Button
          text={`${isEdit ? 'Save' : 'Create'}`}
          className="mt-2"
          width="w-[182px]"
          type="submit"
          loading={isLoading}
        />
      </div>
    </form>
  );
};

export default AddUsers;