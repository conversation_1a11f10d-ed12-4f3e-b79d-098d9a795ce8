import { Button, DataTable, InputField, Modal } from '@Components/UI';
import {
  AddIcon,
  CloseIcon,
  ExportIcon,
  SearchIcon,
  DownloadIcon,
} from '@Icons';
import React, { useEffect, useRef, useState } from 'react';

import { useToast } from '@Components/UI/Toast/ToastProvider';
import { ColumnDef, SortingState } from '@tanstack/react-table';
import { useNavigate } from 'react-router';
import PATHS from '@Config/Path.Config';
import ContentLayout from '@Components/Layout/ContentLayout';
import ActionButtons from '@Components/Common/ActionButtons';

import Switch from 'react-switch';

import {
  useDeleteUser,
  useExportCsv,
  useGetPurchaseHistory,
  useGetUserList,
  useImportCsv,
  useSampleCsv,
  useStateList,
  useToggleUserStatus,
  useTradeList
} from '@Query/Hooks/useUser';
import AddUsers from './AddUser';
import { useDebounce } from '@Hooks/useDebounce';
import queryClient from '@Helpers/QueryClient';
import useHasAccess from '@Hooks/useHasAccess';
import { MODULE_KEY } from '@Helpers/Constants';
import ActionBarLayout from '@Components/Layout/ActionBarLayout';

interface LicenseFormat {
  prefix: string | null;
  postfix: string | null;
  min_number: number;
  max_number: number;
}

interface StateData {
  id: number;
  name: string;
  license_formats: LicenseFormat[];
}

interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  number_of_uploads: number;
  used_uploads: number;
  is_active: boolean;
  mobile_no: string;
  country_code: string;
  license: string;
  license_format?: LicenseFormat;
  state?: {
    label: string;
    value: string;
    license_formats?: LicenseFormat[];
  };
  trade?: {
    label: string;
    value: string;
  };
  statesList?: StateData[];
  tradesList?: StateData[];
}

interface History {
  id: number;
  subscription_name: string;
  platform_name: string;
  payment_status: string;
  amount: string;
  final_amount: string;
  payment_date: string;
}

interface ImportErrorData {
  message?: string;
  data?: {
    errors: {
      row: number;
      errors: string[];
    }[];
  };
}

type SearchableColumnDef<T> = ColumnDef<T> & {
  search?: boolean;
  vKey?: string;
};

function UserManagement() {
  const [openDeleteAccountModal, setOpenDeleteAccountModal] =
    useState<boolean>(false);
  const importFileInputRef = useRef<HTMLInputElement>(null);
  const [searchText, setSearchText] = useState<string>('');
  const [sorting, setSorting] = useState<SortingState>([
    { id: 'id', desc: true },
  ]);
  const [openEditAccountModal, setOpenEditAccountModal] =
    useState<boolean>(false);
  const [openAddAccountModal, setOpenAddAccountModal] =
    useState<boolean>(false);

  const [selectedRow, setSelectedRow] = useState<User | null>();

  const [currentPage, setCurrentPage] = useState(1);

  const [pageSize, setPageSize] = useState(10);
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);
  const [historyData, setHistoryData] = useState<History[]>([]);
  const [userViewData, setUserViewData] = useState<User | null>(null);
  const [isImportError, setIsImportError] = useState(false);
  const [importErrorData, setImportErrorData] =
    useState<ImportErrorData | null>(null);

  const module_access = useHasAccess(MODULE_KEY.USERS, [
    'read',
    'write',
    'update',
    'delete',
  ]);

  const navigate = useNavigate();

  const { addToast } = useToast();

  const {
    data: userListData,
    isError: userListError,
    isLoading: userListLoading,
    refetch: refetchUserList,
    error: userListErrorMessage,
  } = useGetUserList({
    ordering:
      sorting.length > 0
        ? `${sorting[0]?.desc === true ? '-' + sorting[0]?.id : sorting[0]?.id}`
        : '-id',
    page: currentPage,
    limit: pageSize,
    search: searchText,
    isSearchable: searchText.length > 0,
  });
  const {
    isLoading: stateListLoading,
    isFetching: stateListFetching,
    refetch: stateRefetch,
  } = useStateList(false);

  const {
    isLoading: tradeListLoading,
    isFetching: tradeListFetching,
    refetch: tradeRefetch,
  } = useTradeList(false);

  const {
    mutate: getHistory,
    isError: historyListError,
    isLoading: historyListLoading,
    error: historyListErrorMessage,
  } = useGetPurchaseHistory((data) => {
    setHistoryData(data?.data?.data?.purchase_history);
    setIsHistoryOpen(true);
  });

  const { mutate: importItem, isLoading: importItemIsLoading } = useImportCsv({
    onSuccess: (data) => {
      setIsImportError(false);
      setImportErrorData(data?.data);

      addToast(
        data?.data?.status === 1 ? 'success' : 'error',
        data?.data?.message
      );
      queryClient.invalidateQueries('userList');
    },
    onError: (error) => {
      addToast('error', error);
    },
  });

  const {
    mutate: exportCsv,
    isLoading: exportLoading,
    isError: exportIsError,
    error: exportErrorMessage,
    isSuccess: exportSuccess,
  } = useExportCsv();

  const {
    mutate: sampleCsv,
    isLoading: sampleCsvLoading,
    isError: sampleCsvIsError,
    error: sampleCsvErrorMessage,
    isSuccess: sampleCsvSuccess,
  } = useSampleCsv();

  const {
    mutate: deleteUser,
    error: deleteUserError,
    isError: isErrorDeleteUser,
    isSuccess: isSuccessDeleteUser,
    data: deleteUserData,
    isLoading: isLoadingDeleteUser,
  } = useDeleteUser({
    userId: selectedRow ? selectedRow?.id : 0,
  });
  const {
    mutate: toggleUserStatus,
    error: toggleError,
    isError: isErrortoggle,
    isSuccess: isSuccesstoggle,
    isLoading: isLoadingtoggle,
  } = useToggleUserStatus();

  const columns: SearchableColumnDef<User>[] = [
    {
      header: 'Name',
      accessorKey: 'full_name',
      cell: (info) => info.getValue(),
    },
    {
      header: 'Email',
      accessorKey: 'email',
      cell: (info) => info.getValue(),
    },
    {
      header: 'Phone number',
      accessorKey: 'mobile_no',
      enableSorting: false,
      cell: (info) => {
        const { country_code } = info.row.original;
        return `${country_code || ''} ${info.getValue()}`;
      },
    },
    {
      header: 'State',
      accessorKey: 'state',
      cell: (info) => info.getValue(),
      enableSorting: false,
    },
    {
      header: 'Trade',
      accessorKey: 'trade',
      enableSorting: false,
      cell: (info) => info.getValue(),
    },
    {
      header: 'Status',
      accessorKey: 'status',
      enableSorting: false,
      cell: (info) => {
        const { is_active } = info.row.original;

        return (
          <div className="flex gap-4">
            <Switch
              checked={is_active}
              onChange={() =>
                toggleUserStatus({ userId: info.row.original.id })
              }
              disabled={!module_access?.update || isLoadingtoggle}
              onColor="#E0E3FF"
              onHandleColor="#FF8800"
              offColor="#E8ECF3"
              offHandleColor="#D0D5DD"
              handleDiameter={24}
              uncheckedIcon={false}
              checkedIcon={false}
              boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
              activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
              height={16}
              width={40}
            />
          </div>
        );
      },
    },
    {
      header: 'Actions',
      accessorKey: 'actions',
      enableSorting: false,
      cell: (info) => {
        return (
          <ActionButtons
            // isPurchaseHistory={module_access?.read}
            deleteIcon
            editIcon
            deleteIconDisabled={!module_access?.delete}
            editIconDisabled={!module_access?.update}
            onDelete={() => {
              setSelectedRow(info.row.original);
              setOpenDeleteAccountModal(true);
            }}
            onEdit={async () => {
              setSelectedRow(info.row.original);
              const response = await stateRefetch();
              const responseTrade = await tradeRefetch();
              const states = response?.data?.data?.data as StateData[];
              const trades = responseTrade?.data?.data?.data as StateData[];
              const selectedState = states.find(
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                (elem: any) => elem.name === info.row.original.state
              );
              const selectedTrade = trades.find(
                (elem: any) => elem.name === info.row.original.trade
              );
              if (response.isSuccess) {
                setUserViewData({
                  ...info.row.original,
                  state: {
                    label: selectedState?.name || '',
                    value: selectedState ? selectedState.id.toString() : '',
                    license_formats: selectedState?.license_formats,
                  },
                  trade: {
                    label: selectedTrade?.name || '',
                    value: selectedTrade ? selectedTrade.id.toString() : '',
                  },
                  statesList: states,
                  tradesList: trades,
                });
                setOpenEditAccountModal(true);
              }
            }}
            loadingEdit={stateListLoading || stateListFetching}
            loadingPurchaseHistory={historyListLoading}
          // onPurchaseHistoryView={() => {
          //   setSelectedRow(info.row.original);
          //   getHistory(info.row.original.id);
          // }}
          />
        );
      },
    },
  ];

  const historyColumns: SearchableColumnDef<History>[] = [
    {
      header: 'Subscription',
      accessorKey: 'subscription_name',
      cell: (info) => info.getValue(),
      enableSorting: false,
    },
    {
      header: 'Platform',
      accessorKey: 'platform_name',
      cell: (info) => info.getValue(),
      enableSorting: false,
    },
    {
      header: 'Payment Status',
      accessorKey: 'payment_status',
      cell: (info) => info.getValue(),
      enableSorting: false,
    },
    {
      header: 'Amount',
      accessorKey: 'amount',
      cell: (info) => info.getValue(),
      enableSorting: false,
    },
    {
      header: 'Final Amount',
      accessorKey: 'final_amount',
      cell: (info) => info.getValue(),
      enableSorting: false,
    },
    {
      header: 'Payment Date',
      accessorKey: 'payment_date',
      cell: (info) => info.getValue(),
      enableSorting: false,
    },
  ];

  const onPageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const onPageSizeChange = (newSize: number) => {
    setPageSize(newSize);
    setCurrentPage(1); // Reset to first page when page size changes
  };

  const currentData = userListData?.data?.data?.list;

  const debouncedSearchText = useDebounce(searchText, 500);

  const handleExportCsv = () => {
    exportCsv(undefined, {
      onSuccess: (res: { data: string }) => {
        const blob = new Blob([res.data], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'users.csv';
        a.click();
        window.URL.revokeObjectURL(url);
      },
    });
  };

  const handleSampleCsv = () => {
    sampleCsv(undefined, {
      onSuccess: (res: { data: string }) => {
        const blob = new Blob([res.data], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'sampleUsers.csv';
        a.click();
        window.URL.revokeObjectURL(url);
      },
    });
  };

  const importHandler = () => {
    if (importFileInputRef.current) {
      importFileInputRef.current.value = '';
      importFileInputRef.current.click();
    }
  };

  const handleImportErrorModalClose = () => {
    setIsImportError(false);
  };

  const handleSuccess = () => {
    setOpenEditAccountModal(false);
    setOpenAddAccountModal(false);
    setSelectedRow(null);
  };

  const handleImportCsv = (file: File) => {
    const formData = new FormData();
    formData.append('csv_file', file);

    importItem(formData);
  };

  useEffect(() => {
    if (userListError) {
      addToast('error', userListErrorMessage as string);
    }
    if (isErrorDeleteUser) {
      addToast('error', deleteUserError as string);
    }
  }, [userListError, userListErrorMessage, isErrorDeleteUser, deleteUserError]);

  useEffect(() => {
    refetchUserList();
  }, [debouncedSearchText]);

  useEffect(() => {
    if (isSuccessDeleteUser) {
      setOpenDeleteAccountModal(false);
      setSelectedRow(null);
      addToast('success', deleteUserData?.data?.message);
      refetchUserList();
    }
  }, [isSuccessDeleteUser]);

  useEffect(() => {
    if (searchText.length > 0) {
      setCurrentPage(1);
    }
  }, [searchText]);

  useEffect(() => {
    if (historyListError) {
      addToast('error', historyListErrorMessage as string);
    }
  }, [historyListError, historyListErrorMessage]);

  useEffect(() => {
    if (exportIsError) {
      addToast('error', exportErrorMessage as string);
    }
    if (exportSuccess) {
      addToast('success', 'CSV exported successfully');
    }
  }, [exportIsError, exportErrorMessage, exportSuccess]);

  useEffect(() => {
    if (sampleCsvIsError) {
      addToast('error', sampleCsvErrorMessage as string);
    }
    if (sampleCsvSuccess) {
      addToast('success', 'Sample CSV generated successfully');
    }
  }, [sampleCsvIsError, sampleCsvErrorMessage, sampleCsvSuccess]);

  useEffect(() => {
    if (isErrortoggle) {
      addToast('error', toggleError as string);
    }
    if (isSuccesstoggle) {
      addToast('success', 'User status updated successfully');
    }
  }, [isErrortoggle, toggleError, isSuccesstoggle]);

  useEffect(() => {
    refetchUserList();
  }, [sorting, pageSize, currentPage]);

  return (
    <ContentLayout>
      <ActionBarLayout
        actionbarChildren={
          <React.Fragment>
            <div className="w-auto">
              <InputField
                placeholder="Search...."
                inputContainerClassName="max-h-[46px] w-max "
                className="p-0"
                containerClassName="p-0"
                onChange={(i) => setSearchText(i.target.value)}
                rightIcon={<SearchIcon height={18} width={18} />}
              />
            </div>
            <div className="flex gap-3">
              <div>
                <Button
                  text={
                    <div className="flex gap-2 px-2 items-center justify-center text-white w-max">
                      <DownloadIcon height={18} width={18} fill="#fff" />
                      Sample CSV
                    </div>
                  }
                  onClick={() => handleSampleCsv()}
                  disabled={sampleCsvLoading}
                />
              </div>
              <div>
                <Button
                  text={
                    <div className="flex gap-2 px-2 items-center justify-center text-white w-max">
                      <DownloadIcon height={18} width={18} fill="#fff" />
                      Export
                    </div>
                  }
                  onClick={() => handleExportCsv()}
                  disabled={exportLoading}
                />
              </div>
              <div>
                <Button
                  text={
                    <div className="flex gap-2 px-2 items-center justify-center text-white w-max">
                      <ExportIcon height={18} width={18} fill="#fff" />
                      Import
                    </div>
                  }
                  onClick={() => importHandler()}
                  disabled={!module_access?.write || importItemIsLoading}
                />
                <input
                  type="file"
                  accept=".csv"
                  ref={importFileInputRef}
                  className="hidden"
                  onChange={(e) => {
                    if (e.target.files && e.target.files[0]) {
                      handleImportCsv(e.target.files[0]);
                      e.target.value = '';
                    }
                  }}
                />
              </div>
              <div>
                <Button
                  text={
                    <div className="flex gap-2 px-2 items-center justify-center text-white w-max">
                      <AddIcon height={18} width={18} fill="#fff" /> Add User
                    </div>
                  }
                  disabled={!module_access?.write}
                  onClick={() => setOpenAddAccountModal(true)}
                />
              </div>
            </div>
          </React.Fragment>
        }
      >
        <DataTable
          data={currentData}
          columns={columns}
          sorting={sorting}
          setSorting={setSorting}
          totalPages={userListData?.data?.data?.total_pages}
          currentPage={currentPage}
          onPageChange={onPageChange}
          pageSize={pageSize}
          setPageSize={setPageSize}
          pageSizeOptions={[10, 20, 30, 50]}
          onPageSizeChange={onPageSizeChange}
          isPagination={true}
          loading={userListLoading}
        />

        <Modal
          isOpen={isHistoryOpen}
          size="none"
          className="max-w-6xl"
          header={
            <div className="flex flex-col gap-1.5">
              <h3 className="text-xl text-black font-bold">Purchase History</h3>
            </div>
          }
          onClose={() => {
            setIsHistoryOpen(false);
            setSelectedRow(null);
          }}
          children={<DataTable data={historyData} columns={historyColumns} />}
        />
        {/*edit */}
        <Modal
          isOpen={openEditAccountModal}
          size="none"
          className="max-w-7xl"
          header={
            <div className="flex flex-col gap-1.5">
              <h3 className="text-xl text-black font-bold">Edit User</h3>
            </div>
          }
          onClose={() => {
            setOpenEditAccountModal(false);
            setSelectedRow(null);
          }}
          children={
            <AddUsers
              isEdit={true}
              userData={userViewData || ({} as User)}
              handleSuccess={handleSuccess}
            />
          }
        />

        {/*Add */}
        <Modal
          isOpen={openAddAccountModal}
          size="none"
          className="max-w-7xl"
          header={
            <div className="flex flex-col gap-1.5">
              <h3 className="text-xl text-black font-bold">Add User</h3>
            </div>
          }
          onClose={() => {
            setOpenAddAccountModal(false);
          }}
          children={<AddUsers isEdit={false} handleSuccess={handleSuccess} />}
        />
        <Modal
          isOpen={openDeleteAccountModal}
          hideCloseIcon
          onClose={() => {
            setOpenDeleteAccountModal(false);
            setSelectedRow(null);
          }}
          children={
            <div className="flex flex-col items-center gap-[36px] ">
              <div className="bg-[#FFD8E0] h-14 w-14 flex justify-center items-center rounded-full">
                <CloseIcon width={18} height={18} fill="#FF3B30" />
              </div>
              <div className="text-black font-bold text-center w-full">
                Are you sure you want to delete this user? This action cannot be
                undone.
              </div>
              <div className=" w-full flex justify-center gap-6">
                <Button
                  onClick={deleteUser}
                  text="Yes"
                  variant="other"
                  className="border border-error-0
              text-error-0 bg-transparent hover:border-error-0"
                  loading={isLoadingDeleteUser}
                />
                <Button
                  text="No"
                  variant="outline"
                  disabled={isLoadingDeleteUser}
                  onClick={() => {
                    setOpenDeleteAccountModal(false);
                    setSelectedRow(null);
                  }}
                />
              </div>
            </div>
          }
        />
        <Modal
          isOpen={isImportError}
          size="xl"
          onClose={() => {
            handleImportErrorModalClose();
          }}
          header={
            <div className="flex flex-col gap-1.5">
              <h3 className="text-xl text-black font-bold">
                User Import Status
              </h3>
              <p className="mt-2 text-sm text-gray-600">
                {importErrorData?.message}
              </p>
            </div>
          }
          children={
            <div className="overflow-auto">
              <div className="space-y-4">
                <div className="text-sm font-medium">
                  The following users could not be imported:
                </div>
                <div className="border">
                  <table className="w-full">
                    <thead className="bg-gray-200">
                      <tr>
                        <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">
                          Row
                        </th>
                        <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">
                          Reason
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y">
                      {importErrorData?.data?.errors?.map((error, index) => {
                        return (
                          <tr key={index} className="bg-white">
                            <td className="px-4 py-3 text-sm text-gray-900">
                              {error.row}
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-900">
                              <div className="space-y-1">
                                {Object.entries(error.errors || {}).map(
                                  ([errorKey, errorMessages]) => (
                                    <div key={errorKey}>
                                      <span className="font-medium">
                                        {errorKey}:{' '}
                                      </span>
                                      {Array.isArray(errorMessages) ? (
                                        <ul className="list-disc pl-5">
                                          {errorMessages.map(
                                            (message, msgIndex) => (
                                              <li key={msgIndex}>{message}</li>
                                            )
                                          )}
                                        </ul>
                                      ) : (
                                        <span>{String(errorMessages)}</span>
                                      )}
                                    </div>
                                  )
                                )}
                              </div>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          }
        />
      </ActionBarLayout>
    </ContentLayout>
  );
}

export default UserManagement;
