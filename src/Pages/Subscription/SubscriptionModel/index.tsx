import { useEffect, useState, useMemo } from 'react';
import { Button, CustomCheckbox, DataTable, InputField } from '@Components/UI';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import clsx from 'clsx';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { useGetAppRolePermission } from '@Query/Hooks/useRolePermission';
import { useEditSubscription } from '@Query/Hooks/useSubscription';

interface UserData {
  id: number;
  subscription_name: string;
  subscription_price: number;
  subscription_duration: number;
  feature_ids: number[];
}

// Component props interface
interface AddUsersProps {
  isEdit?: boolean;
  userData: UserData;
  handleSuccess?: () => void;
  handleClose?: () => void;
}

// Define schema with proper license format validation

const schema = {
  subscription_price: yup.number().required('Price is required'),
  subscription_duration: yup
    .number()
    .required('Subscription start date is required'),
  subscription_name: yup.string().required('Subscription end date is required'),
};

interface RegisterFormValues {
  subscription_name: string;
  subscription_price: number;
  subscription_duration: number;
  feature_ids: number[];
}

interface FeatureAction {
  feature_id: number;
  action: boolean;
}

const defaultUserData: UserData = {
  id: 0,
  subscription_duration: 0,
  subscription_name: '',
  feature_ids: [],
  subscription_price: 0,
};

const createSchema = () =>
  yup.object().shape({
    ...schema,
  });

const Subscription = ({
  isEdit = false,
  userData = defaultUserData,
  handleSuccess = () => {},
}: AddUsersProps): JSX.Element => {
  const { addToast } = useToast();

  type Features = number[];

  const [rolePermissions, setRolePermissions] = useState<Features>(
    userData?.feature_ids
  );

  const columnsForRolePermission = useMemo(
    () => [
      {
        header: 'Module name',
        accessorKey: 'display_name',
        enableSorting: false,
      },
      {
        header: 'Access',
        accessorKey: 'access.read',
        enableSorting: false,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        cell: (info: any) => (
          <CustomCheckbox
            onChange={(e) => {
              setRolePermissions((prevPermissions) => {
                const roleId = info.row.original.id as number;

                if (e.target.checked) {
                  // Add roleId if not already in the list
                  return prevPermissions.includes(roleId)
                    ? prevPermissions
                    : [...prevPermissions, roleId];
                } else {
                  // Remove roleId when unchecked
                  return prevPermissions.filter((id) => id !== roleId);
                }
              });
            }}
            checked={rolePermissions.includes(info.row.original.id)}
          />
        ),
      },
    ],
    [rolePermissions]
  );

  // Get state list data

  const {
    data: approlePermissionListData,
    // refetch: refetchRolePermissionList,
    isError: approlePermissionListIsError,
    error: approlePermissionListError,
    isLoading: approlePermissionListLoading,
  } = useGetAppRolePermission();

  const {
    mutate: updateItem,
    data: updateItemData,
    isError: updateItemIsError,
    error: updateItemError,
    isSuccess: updateItemIsSuccess,
    isLoading: updateItemIsLoading,
  } = useEditSubscription();

  useEffect(() => {
    if (updateItemIsError) {
      addToast('error', updateItemError as string);
    }

    if (updateItemIsSuccess) {
      addToast('success', updateItemData?.data?.message);
      handleSuccess();
    }
  }, [updateItemIsError, updateItemError, updateItemIsSuccess, updateItemData]);

  useEffect(() => {
    if (approlePermissionListIsError) {
      addToast('error', approlePermissionListError as string);
    }
  }, [approlePermissionListIsError, approlePermissionListError]);

  // Form setup
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<RegisterFormValues>({
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    resolver: yupResolver(createSchema()) as any,
    defaultValues: {
      feature_ids: userData?.feature_ids,
      subscription_duration: userData.subscription_duration,
      subscription_name: userData.subscription_name,
      subscription_price: userData.subscription_price,
    },
    mode: 'onChange',
  });

  // Form submission handler
  const onSubmit = async (formData: RegisterFormValues): Promise<void> => {
    const _features: FeatureAction[] =
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      approlePermissionListData?.data?.data?.map((elem: any) => {
        return {
          feature_id: elem.id,
          action: !!rolePermissions.find((el) => el === elem.id),
        };
      });

    updateItem({
      subscription_id: userData.id,
      features: _features,
    });
  };

  return (
    <form
      className={clsx('grid grid-cols-6 gap-4 pt-2', isEdit ? '' : '')}
      onSubmit={handleSubmit(onSubmit)}
      autoComplete="off"
    >
      <div className="col-span-3">
        <Controller
          name="subscription_name"
          control={control}
          render={({ field }) => (
            <InputField
              label="Subscription Name"
              disabled={true}
              field={field}
              placeholder="Subscription Name"
              errorMessage={errors.subscription_name?.message}
            />
          )}
        />
      </div>
      <div className="col-span-3">
        {' '}
        <Controller
          name="subscription_duration"
          control={control}
          render={({ field }) => (
            <InputField
              label="Subscription Duration (Days)"
              disabled={true}
              field={field}
              placeholder="Subscription Duration"
              errorMessage={errors.subscription_duration?.message}
            />
          )}
        />
      </div>
      <div className="col-span-3">
        {' '}
        <Controller
          name="subscription_price"
          control={control}
          render={({ field }) => (
            <InputField
              label="Subscription Price ($)"
              disabled={true}
              field={field}
              errorMessage={errors.subscription_price?.message}
            />
          )}
        />
      </div>
      <div className="col-span-6">
        <DataTable
          data={approlePermissionListData?.data?.data}
          columns={columnsForRolePermission}
          totalPages={1}
          currentPage={1}
          pageSize={10000}
          isPagination={false}
          loading={approlePermissionListLoading}
        />
      </div>

      <div className={clsx('col-span-6 flex', 'justify-end')}>
        <Button
          text={'Save'}
          className="mt-2"
          width="w-[182px]"
          type="submit"
          loading={updateItemIsLoading}
        />
      </div>
    </form>
  );
};

export default Subscription;
