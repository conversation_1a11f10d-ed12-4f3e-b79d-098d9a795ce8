import { DataTable, Modal } from '@Components/UI';
import { useEffect, useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import ContentLayout from '@Components/Layout/ContentLayout';
import ActionButtons from '@Components/Common/ActionButtons';
import useHasAccess from '@Hooks/useHasAccess';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { MODULE_KEY } from '@Helpers/Constants';
import { useGetSubscriptionList } from '@Query/Hooks/useSubscription';
import Subscription from './SubscriptionModel';
import ActionBarLayout from '@Components/Layout/ActionBarLayout';

interface Subscription {
  id: number;
  subscription_name: string;
  subscription_price: number;
  subscription_duration: number;
  no_of_uploads: number;
  features: {
    id: number;
    name: string;
    display_name: string;
  }[];
}

type SearchableColumnDef<T> = ColumnDef<T> & {
  search?: boolean;
  vKey?: string;
};

function Subscriptions() {
  const module_access = useHasAccess(MODULE_KEY.SUBSCRIPTION, [
    'read',
    'write',
    'update',
    'delete',
  ]);

  const [openViewModal, setOpenViewModal] = useState<boolean>(false);
  const [selectedRow, setSelectedRow] = useState<Subscription | null>(null);
  const { addToast } = useToast();

  const {
    data: subscriptiondatalist,
    isError: subscriptionListError,
    isLoading: subscriptionListIsLoading,
    refetch: refetchSubscriptionList,
    isFetching: subscriptionListIsFetching,
    error: subscriptionListErrorMessage,
  } = useGetSubscriptionList();

  const columns: SearchableColumnDef<Subscription>[] = [
    {
      header: 'Subscription Name',
      accessorKey: 'subscription_name',
      enableSorting: false,
      cell: (info) => info.getValue(),
    },
    {
      header: 'Subscription Price',
      accessorKey: 'subscription_price',
      enableSorting: false,
      cell: (info) => `$${info.getValue()}`,
    },
    {
      header: 'Subscription Duration',
      accessorKey: 'subscription_duration',
      enableSorting: false,
      cell: (info) => `${info.getValue()} Months`,
    },
    {
      header: 'Action',
      accessorKey: 'action',
      enableSorting: false,
      cell: (info) => (
        <ActionButtons
          editIcon
          onEdit={() => {
            setSelectedRow(info?.row?.original);
            setOpenViewModal(true);
          }}
          editIconDisabled={!module_access?.update}
        />
      ),
    },
  ];

  const currentData = subscriptiondatalist?.data?.data || [];

  useEffect(() => {
    if (subscriptionListError) {
      addToast('error', subscriptionListErrorMessage as string);
    }
  }, [subscriptionListError, subscriptionListErrorMessage]);

  return (
    <ContentLayout>
      <ActionBarLayout>
        <DataTable
          data={currentData}
          columns={columns}
          isPagination={false}
          loading={subscriptionListIsLoading || subscriptionListIsFetching} // set to true if loading data from an API
        />

        {openViewModal && selectedRow && (
          <Modal
            isOpen={openViewModal}
            header={'Edit Subscription'}
            size="dxl"
            onClose={() => {
              setOpenViewModal(false);
            }}
            children={
              <>
                <div>
                  <Subscription
                    userData={{
                      id: selectedRow.id,
                      subscription_name: selectedRow.subscription_name,
                      subscription_price: selectedRow.subscription_price,
                      subscription_duration: selectedRow.subscription_duration,
                      feature_ids: selectedRow
                        ? selectedRow?.features?.map((feats) => feats.id)
                        : [],
                    }}
                    handleSuccess={() => {
                      refetchSubscriptionList();
                      setOpenViewModal(false);
                    }}
                  />
                </div>
              </>
            }
          />
        )}
      </ActionBarLayout>
    </ContentLayout>
  );
}

export default Subscriptions;
