import { But<PERSON>, InputField } from '@Components/UI';
import { useCallback, useEffect, useState } from 'react';
import { t } from 'i18next';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { CloseEyeIcon, EyeIcon } from '@Icons';
import ContentLayout from '@Components/Layout/ContentLayout';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import {
  useGetAdminProfile,
  useUpdateAdminProfile,
} from '@Query/Hooks/useAdmin';
import ProfileLayout from '@Components/Layout/ProfileLayout';

interface ChangePasswordFormValues {
  old_password: string;
  new_password: string;
  confirm_password: string;
}

function ChangePassword() {
  const { addToast } = useToast();

  const [toggleEyeIconForCurrentPassword, setToggleEyeIconCurrentPassword] =
    useState<boolean>(false);

  const [toggleEyeIcon, setToggleEyeIcon] = useState<boolean>(false);

  const [toggleEyeIconForConfirmPassword, setToggleEyeIconConfirmPassword] =
    useState<boolean>(false);

  const {
    data: viewProfileData,
    isError: isErrorInViewProfile,
    error: errorOfViewProfile,
    isSuccess: isSuccessViewProfile,
    // isLoading: viewProfileIsLoading,
  } = useGetAdminProfile();

  const {
    mutate: updatePassword,
    data: updateProfileData,
    isError: isUpdateProfileError,
    error: updateProfileError,
    isSuccess: updateProfileSuccess,
    isLoading: isUpdateProfileLoading,
  } = useUpdateAdminProfile();

  const changePasswordFormSchema: yup.ObjectSchema<ChangePasswordFormValues> =
    yup.object({
      old_password: yup
        .string()
        .required(t('authentication.validation.currentPasswordRequired')),
      new_password: yup
        .string()
        .required(t('authentication.validation.new_PasswordRequired'))
        .max(16, t('authentication.validation.passwordTooLong'))
        .matches(
          // /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
          // /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#^])[A-Za-z\d@$!%*?&#^]{8,}$/,
          /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*()_+\-=\\[\]{};':`~"\\|,.<>\\/?])(?=.{8,}).*$/,
          t('authentication.validation.passwordValidation')
        ),
      confirm_password: yup
        .string()
        .required(t('authentication.validation.confirmPasswordRequired'))
        .max(16, t('authentication.validation.passwordTooLong'))
        .matches(
          // /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
          // /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#^])[A-Za-z\d@$!%*?&#^]{8,}$/,
          /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*()_+\-=\\[\]{};':`~"\\|,.<>\\/?])(?=.{8,}).*$/,
          t('authentication.validation.passwordValidation')
        )
        .oneOf(
          [yup.ref('new_password'), ''],
          t('authentication.validation.passwordsDoNotMatch')
        ),
    });

  const {
    handleSubmit,
    control,
    reset,
    formState: { errors },
  } = useForm<ChangePasswordFormValues>({
    defaultValues: {
      old_password: '',
      new_password: '',
      confirm_password: '',
    },
    resolver: yupResolver(changePasswordFormSchema),
    mode: 'onChange',
  });

  const onSubmit = useCallback(
    (data: ChangePasswordFormValues) => {
      updatePassword({ ...data });
    },
    [updatePassword]
  );

  const togglePasswordHandler = (): void => setToggleEyeIcon(!toggleEyeIcon);
  const toggleConfirmPasswordHandler = (): void =>
    setToggleEyeIconConfirmPassword(!toggleEyeIconForConfirmPassword);
  const toggleCurrentPasswordHandler = (): void =>
    setToggleEyeIconCurrentPassword(!toggleEyeIconForCurrentPassword);

  useEffect(() => {
    if (isErrorInViewProfile) {
      addToast('error', errorOfViewProfile as string);
    }
  }, [
    viewProfileData,
    isSuccessViewProfile,
    isErrorInViewProfile,
    errorOfViewProfile,
  ]);

  useEffect(() => {
    if (updateProfileSuccess) {
      reset();
      addToast('success', updateProfileData?.data?.message);
    }
    if (isUpdateProfileError) {
      addToast('error', updateProfileError as string);
    }
  }, [
    isUpdateProfileError,
    updateProfileData,
    updateProfileError,
    updateProfileSuccess,
  ]);

  return (
    <ProfileLayout>
      <ContentLayout
        title={
          <div className="flex gap-2">
            {t('changePassword.title')}{' '}
            {/* {viewProfileIsLoading ? (
              <Loader height={20} width={20} fill="#fff" />
            ) : (
              <text className="text-secondary font-medium">
                {viewProfileData?.data?.data?.email}
              </text>
            )} */}
          </div>
        }
        // subtitle={t('changePassword.subtitle')}
      >
        <form
          className=" max-w-lg flex flex-col gap-4"
          onSubmit={handleSubmit(onSubmit)}
          onReset={() => reset()}
        >
          <Controller
            control={control}
            name="old_password"
            render={({ field }) => (
              <InputField
                label={t('authentication.currentPassword')}
                placeholder={t('authentication.inputOldPassword')}
                disabled={false}
                field={field}
                errorMessage={errors?.old_password?.message}
                type={toggleEyeIconForCurrentPassword ? 'text' : 'password'}
                rightIcon={
                  toggleEyeIconForCurrentPassword ? (
                    <EyeIcon
                      style={{ cursor: 'pointer' }}
                      height={20}
                      width={20}
                      onClick={toggleCurrentPasswordHandler}
                    />
                  ) : (
                    <CloseEyeIcon
                      style={{ cursor: 'pointer' }}
                      height={20}
                      width={20}
                      onClick={toggleCurrentPasswordHandler}
                    />
                  )
                }
              />
            )}
          />
          <Controller
            control={control}
            name="new_password"
            render={({ field }) => (
              <InputField
                label={t('authentication.newPassword')}
                placeholder={t('authentication.inputNewPassword')}
                disabled={false}
                field={field}
                errorMessage={errors?.new_password?.message}
                type={toggleEyeIcon ? 'text' : 'password'}
                rightIcon={
                  toggleEyeIcon ? (
                    <EyeIcon
                      style={{ cursor: 'pointer' }}
                      height={20}
                      width={20}
                      onClick={togglePasswordHandler}
                    />
                  ) : (
                    <CloseEyeIcon
                      style={{ cursor: 'pointer' }}
                      height={20}
                      width={20}
                      onClick={togglePasswordHandler}
                    />
                  )
                }
              />
            )}
          />
          <Controller
            control={control}
            name="confirm_password"
            render={({ field }) => (
              <InputField
                label={t('authentication.confirmPassword')}
                placeholder={t('authentication.inputConfirmPassword')}
                disabled={false}
                field={field}
                errorMessage={errors?.confirm_password?.message}
                type={toggleEyeIconForConfirmPassword ? 'text' : 'password'}
                rightIcon={
                  toggleEyeIconForConfirmPassword ? (
                    <EyeIcon
                      style={{ cursor: 'pointer' }}
                      height={20}
                      width={20}
                      onClick={toggleConfirmPasswordHandler}
                    />
                  ) : (
                    <CloseEyeIcon
                      style={{ cursor: 'pointer' }}
                      height={20}
                      width={20}
                      onClick={toggleConfirmPasswordHandler}
                    />
                  )
                }
              />
            )}
          />

          <div className="mt-4 flex flex-col gap-5">
            <Button
              text={t('save')}
              type="submit"
              loading={isUpdateProfileLoading}
              disabled={isUpdateProfileLoading}
            />
          </div>
        </form>
      </ContentLayout>
    </ProfileLayout>
  );
}

export default ChangePassword;
