import ActionButtons from '@Components/Common/ActionButtons';
import ContentLayout from '@Components/Layout/ContentLayout';
import { Button, DataTable, InputField, Modal, ViewMore } from '@Components/UI';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { MODULE_KEY } from '@Helpers/Constants';
import useHasAccess from '@Hooks/useHasAccess';
import { CloseIcon, AddIcon, SearchIcon } from '@Icons';
import {
  useTradeList,
  useTradeStatus,
} from '@Query/Hooks/userTrade';
import { ColumnDef, SortingState } from '@tanstack/react-table';
import React, { useEffect, useState } from 'react';
import { useDebounce } from '@Hooks/useDebounce';
import AddCategory from './AddCategory';
import Switch from 'react-switch';
import ActionBarLayout from '@Components/Layout/ActionBarLayout';

type SearchableColumnDef<T> = ColumnDef<T> & {
  search?: boolean;
  vKey?: string;
};

interface TradiesHub {
  id: number;
  name: string;
  is_active: boolean;
}

function TradiesHub() {
  const { addToast } = useToast();

  const module_access = useHasAccess(MODULE_KEY.TRADIES_HUB, [
    'read',
    'write',
    'update',
    'delete',
  ]);

  const [selectedRow, setSelectedRow] = useState<TradiesHub | null>();
  const [openAddFormModal, setOpenAddFormModal] = useState<boolean>(false);
  const [openEditFormModal, setOpenEditFormModal] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>('');

  const [sorting, setSorting] = useState<SortingState>([
    { id: 'id', desc: true },
  ]);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  const columns: SearchableColumnDef<TradiesHub>[] = [
    {
      header: 'Name',
      accessorKey: 'name',
      cell: (info) => {
        const row = info.row.original;
        return (
          <div className="flex flex-col gap-1">
            <ViewMore text={row.name} maxLength={60} />
          </div>
        );
      },
    },
    {
      header: 'Status',
      accessorKey: 'status',
      enableSorting: false,
      cell: (info) => {
        const { is_active } = info.row.original;
        return (
          <div className="flex gap-4">
            <Switch
              checked={is_active}
              onChange={() =>
                toggleTradiesHubCategoryStatus({
                  tradeId: info.row.original.id,
                })
              }
              disabled={!module_access?.update || isLoadingtoggle}
              onColor="#E0E3FF"
              onHandleColor="#FF8800"
              offColor="#E8ECF3"
              offHandleColor="#D0D5DD"
              handleDiameter={24}
              uncheckedIcon={false}
              checkedIcon={false}
              boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
              activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
              height={16}
              width={40}
            />
          </div>
        );
      },
    },
    // {
    //   header: 'Action',
    //   accessorKey: 'action',
    //   enableSorting: false,
    //   cell: (info) => (
    //     <ActionButtons
    //       deleteIcon
    //       editIcon
    //       deleteIconDisabled={!module_access.delete}
    //       editIconDisabled={!module_access?.update}
    //       onEdit={() => {
    //         setSelectedRow({ ...info.row.original });
    //         setOpenEditFormModal(true);
    //       }}
    //     />
    //   ),
    // },
  ];

  const {
    data: categoryDataList,
    isError: categoryDataListError,
    isLoading: categoryDataListIsLoading,
    refetch: refetchCategoryDataList,
    isFetching: categoryDataListIsFetching,
    error: categoryDataListErrorMessage,
  } = useTradeList({
    ordering:
      sorting.length > 0
        ? `${sorting[0]?.desc === true ? '-' + sorting[0]?.id : sorting[0]?.id}`
        : 'id',
    page: currentPage,
    limit: pageSize,
    search: searchText,
    isSearchable: searchText.length > 0,
  });


  const {
    mutate: toggleTradiesHubCategoryStatus,
    error: toggleError,
    isError: isErrortoggle,
    data: toggleData,
    isSuccess: isSuccesstoggle,
    isLoading: isLoadingtoggle,
  } = useTradeStatus();

  const currentData = categoryDataList?.data?.data?.list;

  const onPageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const onPageSizeChange = (newSize: number) => {
    setPageSize(newSize);
    setCurrentPage(1);
  };

  useEffect(() => {
    if (searchText.length > 0) {
      setCurrentPage(1);
    }
  }, [searchText]);

  const debouncedSearchText = useDebounce(searchText, 500); // 500ms debounce

  useEffect(() => {
    refetchCategoryDataList();
  }, [debouncedSearchText]);

  useEffect(() => {
    if (categoryDataListError) {
      addToast('error', categoryDataListErrorMessage as string);
    }
  }, [
    categoryDataListError,
    categoryDataListErrorMessage,
  ]);

  useEffect(() => {
    if (isErrortoggle) {
      addToast('error', toggleError as string);
    }
    if (isSuccesstoggle) {
      refetchCategoryDataList();
      addToast(
        'success',
        toggleData?.data?.message || 'File status updated successfully'
      );
    }
  }, [isErrortoggle, toggleError, isSuccesstoggle]);

  useEffect(() => {
    refetchCategoryDataList();
  }, [sorting, pageSize, currentPage]);


  return (
    <ContentLayout>
      <ActionBarLayout
        actionbarChildren={
          <React.Fragment>
            <div className="w-auto">
              <InputField
                placeholder="Search name"
                inputContainerClassName="max-h-[46px] w-max "
                className="p-0"
                containerClassName="p-0"
                onChange={(i) => setSearchText(i.target.value)}
                rightIcon={<SearchIcon height={18} width={18} />}
              />
            </div>
            <div>
              <Button
                text={
                  <div className="flex gap-2 px-2 items-center justify-center text-white w-max">
                    <AddIcon height={18} width={18} fill="#fff" /> Add Trade
                  </div>
                }
                disabled={!module_access?.write}
                onClick={() => setOpenAddFormModal(true)}
              />
            </div>
          </React.Fragment>
        }
      >
        <DataTable
          data={currentData}
          columns={columns}
          sorting={sorting}
          setSorting={setSorting}
          totalPages={categoryDataList?.data?.data?.total_pages}
          currentPage={currentPage}
          onPageChange={onPageChange}
          pageSize={pageSize}
          setPageSize={(num) => {
            setPageSize(num);
            setCurrentPage(1);
          }}
          pageSizeOptions={[10, 20, 30, 50]}
          onPageSizeChange={onPageSizeChange}
          isPagination={true}
          loading={categoryDataListIsLoading || categoryDataListIsFetching}
        />

        <Modal
          isOpen={openAddFormModal}
          size="xl"
          className="max-w-5xl"
          header={
            <div className="flex flex-col gap-1.5">
              <h3 className="text-xl text-black font-bold">Add Trade</h3>
            </div>
          }
          onClose={() => {
            setOpenAddFormModal(false);
            setSelectedRow(null);
          }}
          children={
            <AddCategory
              handleSuccess={() => {
                setOpenAddFormModal(false);
                refetchCategoryDataList();
              }}
            />
          }
        />

        <Modal
          isOpen={openEditFormModal}
          size="xl"
          className="max-w-5xl"
          header={
            <div className="flex flex-col gap-1.5">
              <h3 className="text-xl text-black font-bold">Edit Trade</h3>
            </div>
          }
          onClose={() => {
            setOpenEditFormModal(false);
            setSelectedRow(null);
          }}
          children={
            <AddCategory
              isEdit
              handleSuccess={() => {
                setOpenEditFormModal(false);
                refetchCategoryDataList();
              }}
              formData={{
                id: selectedRow?.id ?? 0,
                name: selectedRow?.name ?? '',
              }}
            />
          }
        />
      </ActionBarLayout>
    </ContentLayout>
  );
}

export default TradiesHub;
