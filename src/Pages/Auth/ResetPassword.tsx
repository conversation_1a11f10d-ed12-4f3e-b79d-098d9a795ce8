import Button from '@Components/UI/Button';
import InputField from '@Components/UI/InputField';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import PATHS from '@Config/Path.Config';
import { yupResolver } from '@hookform/resolvers/yup';
import usePathnameAndQuery from '@Hooks/usePathnameAndQuery';
import { CloseEyeIcon, EyeIcon } from '@Icons';
import { useResetPassword } from '@Query/Hooks/useAuth';
import { t } from 'i18next';
import { useCallback, useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router';
import * as yup from 'yup';

interface ResetPasswordFormValues {
  new_password: string;
  confirm_password: string;
}

const ChangePassword = () => {
  const {
    data,
    error,
    isError,
    isLoading,
    isSuccess,
    mutate: resetPasswordSubmit,
  } = useResetPassword();
  const navigate = useNavigate();
  const { query } = usePathnameAndQuery();
  const { addToast } = useToast();

  const [toggleEyeIcon, setToggleEyeIcon] = useState<boolean>(false);
  const [toggleEyeIconForConfirmPassword, setToggleEyeIconConfirmPassword] =
    useState<boolean>(false);

  const togglePasswordHandler = (): void => setToggleEyeIcon(!toggleEyeIcon);
  const toggleConfirmPasswordHandler = (): void =>
    setToggleEyeIconConfirmPassword(!toggleEyeIconForConfirmPassword);

  const onSubmit = useCallback(
    async (formData: ResetPasswordFormValues): Promise<void> => {
      resetPasswordSubmit(
        { ...formData, encrypted_token: query.token } // Avoid re-creating the object
      );
    },
    [resetPasswordSubmit] // Dependencies to prevent unnecessary re-creation
  );

  const changePasswordSchema: yup.ObjectSchema<ResetPasswordFormValues> =
    yup.object({
      new_password: yup
        .string()
        .required(t('authentication.validation.newPasswordRequired'))
        .max(16, t('authentication.validation.passwordTooLong'))
        .matches(
          // /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
          // /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#^])[A-Za-z\d@$!%*?&#^]{8,}$/,
          /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*()_+\-=\\[\]{};':`~"\\|,.<>\\/?])(?=.{8,}).*$/,
          t('authentication.validation.passwordValidation')
        ),
      confirm_password: yup
        .string()
        .required(t('authentication.validation.confirmPasswordRequired'))
        .max(16, t('authentication.validation.passwordTooLong'))
        .oneOf(
          [yup.ref('new_password'), ''],
          t('authentication.validation.passwordsDoNotMatch')
        ),
    });

  const {
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<ResetPasswordFormValues>({
    defaultValues: {
      new_password: '',
      confirm_password: '',
    },
    resolver: yupResolver(changePasswordSchema),
    mode: 'onChange',
  });

  useEffect(() => {
    if (!query.token) {
      navigate(PATHS.FORGOTPASSWORD, { replace: true });
    }
  }, [query]);

  useEffect(() => {
    if (isError) {
      addToast('error', error as string);
    }

    if (isSuccess) {
      addToast('success', data?.data?.message);
      navigate(PATHS.LOGIN, { replace: true });
    }
  }, [isError, isSuccess]);

  return (
    <div>
      <form
        className="flex flex-col gap-3"
        onSubmit={handleSubmit(onSubmit)}
        autoComplete="off"
      >
        <Controller
          control={control}
          name="new_password"
          render={({ field }) => (
            <InputField
              label={t('authentication.newPassword')}
              placeholder={t('authentication.inputNewPassword')}
              disabled={false}
              field={field}
              errorMessage={errors?.new_password?.message}
              type={toggleEyeIcon ? 'text' : 'password'}
              rightIcon={
                toggleEyeIcon ? (
                  <EyeIcon
                    style={{ cursor: 'pointer' }}
                    height={20}
                    width={20}
                    onClick={togglePasswordHandler}
                  />
                ) : (
                  <CloseEyeIcon
                    style={{ cursor: 'pointer' }}
                    height={20}
                    width={20}
                    onClick={togglePasswordHandler}
                  />
                )
              }
            />
          )}
        />
        <Controller
          control={control}
          name="confirm_password"
          render={({ field }) => (
            <InputField
              label={t('authentication.confirmPassword')}
              placeholder={t('authentication.inputConfirmPassword')}
              disabled={false}
              field={field}
              errorMessage={errors?.confirm_password?.message}
              type={toggleEyeIconForConfirmPassword ? 'text' : 'password'}
              rightIcon={
                toggleEyeIconForConfirmPassword ? (
                  <EyeIcon
                    style={{ cursor: 'pointer' }}
                    height={20}
                    width={20}
                    onClick={toggleConfirmPasswordHandler}
                  />
                ) : (
                  <CloseEyeIcon
                    style={{ cursor: 'pointer' }}
                    height={20}
                    width={20}
                    onClick={toggleConfirmPasswordHandler}
                  />
                )
              }
            />
          )}
        />
        <div className="mt-7">
          <Button
            type="submit"
            text={t('submit')}
            loading={isLoading}
            disabled={isLoading}
          />
        </div>
      </form>
    </div>
  );
};

export default ChangePassword;
