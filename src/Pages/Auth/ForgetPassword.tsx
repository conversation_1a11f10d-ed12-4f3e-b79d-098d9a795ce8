import Button from '@Components/UI/Button';
import InputField from '@Components/UI/InputField';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import PATHS from '@Config/Path.Config';
import { EMAIL_REGX } from '@Helpers/Utils';
import { yupResolver } from '@hookform/resolvers/yup';
import { useSendForgotPasswordMail } from '@Query/Hooks/useAuth';
import { t } from 'i18next';
import { useCallback, useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router';
import * as yup from 'yup';

interface ForgetPasswordFormValues {
  email: string;
}

const ForgetPassword = () => {
  const navigate = useNavigate();

  const {
    mutate: sendForgotPasswordMail,
    isLoading,
    isError,
    error,
    isSuccess,
    data,
  } = useSendForgotPasswordMail();

  const { addToast } = useToast();

  const forgotPasswordSchema: yup.ObjectSchema<ForgetPasswordFormValues> =
    yup.object({
      email: yup
        .string()
        .required(t('authentication.validation.emailRequired'))
        .matches(EMAIL_REGX, t('authentication.validation.validEmail'))
        .max(150, t('authentication.validation.emailMaxLength')),
    });

  const {
    handleSubmit,
    reset,
    control,
    formState: { errors },
  } = useForm<ForgetPasswordFormValues>({
    defaultValues: {
      email: '',
    },
    resolver: yupResolver(forgotPasswordSchema),
    mode: 'onChange',
  });

  const onSubmit = useCallback(
    async (formData: ForgetPasswordFormValues): Promise<void> => {
      sendForgotPasswordMail({
        ...formData,
      });
    },
    [sendForgotPasswordMail]
  );

  useEffect(() => {
    if (isError) {
      addToast('error', error as string);
    }

    if (isSuccess) {
      reset();
      addToast('success', data?.data?.message);
    }
  }, [isError, isSuccess]);

  return (
    <div>
      {!isSuccess ? (
        <form
          className="flex flex-col"
          onSubmit={handleSubmit(onSubmit)}
          autoComplete="off"
        >
          <Controller
            control={control}
            name="email"
            render={({ field }) => (
              <InputField
                label={t('authentication.email')}
                placeholder={t('authentication.inputEmailPlaceHolder')}
                disabled={false}
                field={field}
                type="text"
                errorMessage={errors?.email?.message}
              />
            )}
          />
          <div className="mt-7">
            <Button
              type="submit"
              text={t('submit')}
              disabled={isLoading}
              loading={isLoading}
            />
          </div>
        </form>
      ) : (
        <div className="px-7 pt-10  flex justify-center text-[17px] text-secondary font-medium  items-center flex-col">
          <p className="text-center">
            Reset Password Link has been shared to you registered email address
          </p>
          <span className="pt-4">
            <span
              onClick={() => navigate(PATHS.LOGIN)}
              className="text-primary-100 font-medium hover:underline underline-offset-2 cursor-pointer"
            >
              Click Here
            </span>{' '}
            to redirect to Login page.
          </span>
        </div>
      )}
    </div>
  );
};

export default ForgetPassword;
