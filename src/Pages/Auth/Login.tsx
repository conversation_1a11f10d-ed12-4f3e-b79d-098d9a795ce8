import Button from '@Components/UI/Button';
import InputField from '@Components/UI/InputField';
import { CloseEyeIcon, EyeIcon } from '@Icons';
import { useState, useCallback, useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { t } from 'i18next';
import { yupResolver } from '@hookform/resolvers/yup';
import { encrypt } from '@Helpers/Utils';
import { useLogin } from '@Query/Hooks/useAuth';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { Link, useNavigate } from 'react-router';
import PATHS from '@Config/Path.Config';

interface LoginFormValues {
  email: string;
  password: string;
}

const Login = () => {
  const navigate = useNavigate();
  const {
    mutate: login,
    isLoading,
    isError,
    error,
    isSuccess,
    data,
  } = useLogin();
  const { addToast } = useToast();

  const [toggleEyeIcon, setToggleEyeIcon] = useState<boolean>(false);

  useEffect(() => {
    if (isError) {
      addToast('error', error as string);
    }

    if (isSuccess) {
      addToast('success', data?.data?.message);
      localStorage.setItem('authaccess', data?.data?.data?.access_token);
      navigate(PATHS.DASHBOARD, { replace: true });
    }
  }, [isError, isSuccess]);

  const togglePasswordHandler = useCallback((): void => {
    setToggleEyeIcon((prev) => !prev);
  }, []);

  const loginSchema: yup.ObjectSchema<LoginFormValues> = yup.object({
    email: yup
      .string()
      .required(t('authentication.validation.emailRequired'))
      .matches(
        /^(?!.*\.\.)[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
        t('authentication.validation.validEmail')
      )
      .max(150, t('authentication.validation.emailMaxLength')),
    password: yup
      .string()
      .required(t('authentication.validation.passwordRequired')),
    // .max(16, t('authentication.validation.passwordTooLong'))
    // .matches(
    //   /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
    //   t('authentication.validation.passwordValidation')
    // ),
  });

  const {
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<LoginFormValues>({
    defaultValues: {
      email: '',
      password: '',
    },
    resolver: yupResolver(loginSchema),
    mode: 'onChange',
  });

  const onSubmit = useCallback(
    async (formData: LoginFormValues): Promise<void> => {
      const encryptPasswordKey = import.meta.env.VITE_ENC_PASSWORD;
      const encryptedPassword = encrypt(formData.password, encryptPasswordKey);

      login(
        { email: formData.email, password: encryptedPassword } // Avoid re-creating the object
      );
    },
    [login] // Dependencies to prevent unnecessary re-creation
  );

  return (
    <div>
      <form
        className="flex flex-col gap-3"
        onSubmit={handleSubmit(onSubmit)}
        autoComplete="off"
      >
        <Controller
          control={control}
          name="email"
          render={({ field }) => (
            <InputField
              label={t('authentication.email')}
              placeholder={t('authentication.inputEmailPlaceHolder')}
              disabled={false}
              field={field}
              type="text"
              errorMessage={errors?.email?.message}
            />
          )}
        />

        <Controller
          control={control}
          name="password"
          render={({ field }) => (
            <InputField
              label={t('authentication.password')}
              placeholder={t('authentication.inputPasswordPlaceHolder')}
              disabled={false}
              field={field}
              errorMessage={errors?.password?.message}
              type={toggleEyeIcon ? 'text' : 'password'}
              rightIcon={
                toggleEyeIcon ? (
                  <EyeIcon
                    style={{ cursor: 'pointer' }}
                    height={20}
                    width={20}
                    onClick={togglePasswordHandler}
                  />
                ) : (
                  <CloseEyeIcon
                    style={{ cursor: 'pointer' }}
                    height={20}
                    width={20}
                    onClick={togglePasswordHandler}
                  />
                )
              }
            />
          )}
        />
        {/* <Divider /> */}
        <div className="mt-7">
          <Button
            type="submit"
            text={t('authentication.login')}
            loading={isLoading}
          />
        </div>
      </form>
      <div className="pt-5 text-right">
        <Link to="/forgot-password">{t('authentication.forgetPassword')}</Link>
      </div>
    </div>
  );
};

export default Login;
