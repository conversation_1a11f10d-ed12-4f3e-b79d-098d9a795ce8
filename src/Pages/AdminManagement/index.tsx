import { Button, DataTable, InputField, Modal } from '@Components/UI';
import { AddIcon, CloseIcon, SearchIcon } from '@Icons';
import React, { useEffect, useState } from 'react';
import * as yup from 'yup';
import {
  useDeleteSubAdminUserAccount,
  useGetSubAdminList,
  useUpdateSubAdminUserProfile,
} from '@Query/Hooks/useAdmin';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { ColumnDef, SortingState } from '@tanstack/react-table';
import { useNavigate } from 'react-router';
import PATHS from '@Config/Path.Config';
import ContentLayout from '@Components/Layout/ContentLayout';
import ActionButtons from '@Components/Common/ActionButtons';
import EditSubAdminManagement from './EditSubAdmin';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { EMAIL_REGX, NO_SPEC_CHARACTER } from '@Helpers/Utils';
import { t } from 'i18next';
import { useDebounce } from '@Hooks/useDebounce';
import useHasAccess from '@Hooks/useHasAccess';
import { MODULE_KEY } from '@Helpers/Constants';
import ActionBarLayout from '@Components/Layout/ActionBarLayout';
import AddSubAdminManagement from './AddSubadmin';

interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
  role_id: number;
}
interface SelectOption {
  label: string;
  value: string;
}

type SearchableColumnDef<T> = ColumnDef<T> & {
  search?: boolean;
  vKey?: string;
};

interface SubAdminAddFormValues {
  first_name: string;
  last_name: string;
  email: string;
  role_id: SelectOption;
}

function AdminManagement() {
  const module_access = useHasAccess(MODULE_KEY.ADMINS, [
    'read',
    'write',
    'update',
    'delete',
  ]);

  const [openDeleteAccountModal, setOpenDeleteAccountModal] =
    useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>('');
  const [openEditAccountModal, setOpenEditAccountModal] =
    useState<boolean>(false);
  const [selectedRow, setSelectedRow] = useState<User | null>();
  const [openAddAccountModal, setOpenAddAccountModal] =
    useState<boolean>(false);

  const [sorting, setSorting] = useState<SortingState>([
    { id: 'id', desc: true },
  ]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const navigate = useNavigate();

  const { addToast } = useToast();

  const {
    data: subadmindatalist,
    isError: subadminlistError,
    isLoading: subadminlistIsLoading,
    refetch: refetchSubadminList,
    isFetching: subadminlistIsFetching,
    error: subadminListErrorMessage,
  } = useGetSubAdminList({
    ordering:
      sorting.length > 0
        ? `${sorting[0]?.desc === true ? '-' + sorting[0]?.id : sorting[0]?.id}`
        : '-id',
    page: currentPage,
    limit: pageSize,
    search: searchText,
    isSearchable: searchText.length > 0,
  });

  const {
    mutate: updateItem,
    isLoading: updateItemIsLoading,
    isError: updateItemIsError,
    error: updateItemError,
    isSuccess: updateItemSuccess,
    data: updateItemData,
  } = useUpdateSubAdminUserProfile();

  const {
    mutate: deleteUser,
    error: deleteUserError,
    isError: isErrorDeleteUser,
    isSuccess: isSuccessDeleteUser,
    data: deleteUserData,
    isLoading: isLoadingDeleteUser,
  } = useDeleteSubAdminUserAccount({
    userId: selectedRow ? selectedRow?.id : 0,
  });

  const columns: SearchableColumnDef<User>[] = [
    // {
    //   header: 'ID',
    //   accessorKey: 'id',
    //   cell: (info) => info.getValue(), // Custom cell renderer
    // },
    {
      header: 'First name',
      accessorKey: 'first_name',
      vKey: 'first_name',
      search: true,
      cell: (info) => info.getValue(),
    },
    {
      header: 'Last name',
      accessorKey: 'last_name',
      vKey: 'last_name',
      search: true,
      cell: (info) => info.getValue(),
    },
    {
      header: 'Email',
      accessorKey: 'email',
      vKey: 'email',
      search: true,
      cell: (info) => info.getValue(),
    },
    {
      header: 'Action',
      accessorKey: 'action',
      enableSorting: false,
      cell: (info) => (
        <ActionButtons
          deleteIcon
          editIcon
          deleteIconDisabled={!module_access?.delete}
          editIconDisabled={!module_access?.update}
          onDelete={() => {
            setSelectedRow(info.row.original);
            setOpenDeleteAccountModal(true);
          }}
          onEdit={() => {
            setSelectedRow(info.row.original);
            setOpenEditAccountModal(true);
          }}
        />
      ),
    },
  ];

  // const searchInputOption: Array<{ label: string; value: string }> = columns
  //   .filter((clm) => clm.search)
  //   .map((d) => ({
  //     label: typeof d.header === 'string' ? d.header : '', // Ensure label is a string
  //     // eslint-disable-next-line @typescript-eslint/no-explicit-any
  //     value: (d as any).accessorKey ?? '', // Use accessorKey safely
  //   }));

  const onPageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const onPageSizeChange = (newSize: number) => {
    setPageSize(newSize);
    setCurrentPage(1); // Reset to first page when page size changes
  };

  const currentData = subadmindatalist?.data?.data?.list;

  const debouncedSearchText = useDebounce(searchText, 500); // 500ms debounce

  const schema = {
    first_name: yup
      .string()
      .strict()
      .trim(t('nospace'))
      .required(t('profile.validation.firstnameRequired'))
      .matches(NO_SPEC_CHARACTER, t('onlyAlphabates'))
      .min(3, t('profile.validation.validFirstname'))
      .max(50, t('profile.validation.firstnameGreaterthan')),

    last_name: yup
      .string()
      .strict()
      .trim(t('nospace'))
      .required(t('profile.validation.lastnameRequired'))
      .matches(NO_SPEC_CHARACTER, t('onlyAlphabates'))
      .min(1, t('profile.validation.validLastname'))
      .max(50, t('profile.validation.lastnameGreaterthan')),
    email: yup
      .string()
      .required(t('authentication.validation.emailRequired'))
      .matches(EMAIL_REGX, t('authentication.validation.validEmail'))
      .max(150, t('authentication.validation.emailMaxLength')),
    role_id: yup.mixed().required('Role is required'),
  };

  const createSchema = () =>
    yup.object().shape({
      ...schema,
    });

  const {
    handleSubmit,
    control,
    reset,
    setValue,
    formState: { errors },
  } = useForm<SubAdminAddFormValues>({
    defaultValues: {
      first_name: '',
      last_name: '',
      email: '',
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    resolver: yupResolver(createSchema()) as any,
    mode: 'onChange',
  });

  const onEditSubmit = async (
    formData: SubAdminAddFormValues
  ): Promise<void> => {
    updateItem({
      ...formData,
      role: Number(formData?.role_id?.value),
      id: selectedRow?.id,
    });
  };

  useEffect(() => {
    if (subadminlistError) {
      addToast('error', subadminListErrorMessage as string);
    }
    if (isErrorDeleteUser) {
      addToast('error', deleteUserError as string);
    }
  }, [
    subadminlistError,
    subadminListErrorMessage,
    isErrorDeleteUser,
    deleteUserError,
  ]);

  useEffect(() => {
    if (searchText.length > 0) {
      setCurrentPage(1);
    }
  }, [searchText]);

  useEffect(() => {
    refetchSubadminList();
  }, [debouncedSearchText]);

  useEffect(() => {
    if (updateItemIsError) {
      addToast('error', updateItemError as string);
    }

    if (updateItemSuccess) {
      reset();
      refetchSubadminList();
      setOpenEditAccountModal(false);
      addToast('success', updateItemData?.data?.message);
    }
  }, [updateItemIsError, updateItemError, updateItemSuccess]);

  useEffect(() => {
    if (isSuccessDeleteUser) {
      setSelectedRow(null);
      addToast('success', deleteUserData?.data?.message);
      if (currentData?.length === 1 && currentPage > 1) {
        setCurrentPage((prev) => prev - 1);
      } else {
        refetchSubadminList();
      }
      setOpenDeleteAccountModal(false);
    }
  }, [isSuccessDeleteUser]);

  useEffect(() => {
    refetchSubadminList();
  }, [sorting, pageSize, currentPage]);

  useEffect(() => {
    if (openEditAccountModal && selectedRow) {
      setValue('first_name', selectedRow?.first_name);
      setValue('last_name', selectedRow?.last_name);
      setValue('email', selectedRow?.email);
      if (selectedRow.role && selectedRow.role_id) {
        setValue('role_id', {
          label: selectedRow.role,
          value: String(selectedRow.role_id),
        });
      }
    }
  }, [openEditAccountModal, selectedRow]);

  return (
    <ContentLayout>
      <ActionBarLayout
        actionbarChildren={
          <React.Fragment>
            <div className="w-[300px]">
              <InputField
                placeholder="Search by name, email"
                className="p-0"
                containerClassName="p-0"
                onChange={(i) => setSearchText(i.target.value)}
                rightIcon={<SearchIcon height={14} width={14} />}
              />
            </div>
            <div>
              <Button
                disabled={!module_access?.write}
                text={
                  <div className="flex gap-2 items-center justify-center text-white">
                    <AddIcon height={14} width={14} fill="#fff" />{' '}
                    {t('pages.admins.addSubAdmin')}
                  </div>
                }
                onClick={() => setOpenAddAccountModal(true)}
              />
            </div>
          </React.Fragment>
        }
      >
        <DataTable
          data={currentData}
          columns={columns}
          sorting={sorting}
          setSorting={setSorting}
          totalPages={subadmindatalist?.data?.data?.total_pages}
          currentPage={currentPage}
          onPageChange={onPageChange}
          pageSize={pageSize}
          setPageSize={(num) => {
            setPageSize(num);
            setCurrentPage(1);
          }}
          pageSizeOptions={[10, 20, 30, 50]}
          onPageSizeChange={onPageSizeChange}
          isPagination={true}
          loading={subadminlistIsLoading || subadminlistIsFetching} // set to true if loading data from an API
        />

        {/* Delete user */}
        <Modal
          isOpen={openDeleteAccountModal}
          hideCloseIcon
          onClose={() => {
            setOpenDeleteAccountModal(false);
            setSelectedRow(null);
          }}
          children={
            <div className="flex flex-col items-center gap-[36px] ">
              <div className="bg-[#FFD8E0] h-14 w-14 flex justify-center items-center rounded-full">
                <CloseIcon width={18} height={18} fill="#FF3B30" />
              </div>
              <div className="text-black font-bold text-center w-full">
                {t('pages.admins.deleteAdmin')}
              </div>
              <div className=" w-full flex justify-center gap-6">
                <Button
                  onClick={deleteUser}
                  text="Yes"
                  variant="other"
                  loading={isLoadingDeleteUser}
                  className=" border border-error-0
              text-error-0 bg-transparent hover:border-error-0"
                />
                <Button
                  text="Cancel"
                  variant="outline"
                  disabled={isLoadingDeleteUser}
                  onClick={() => {
                    setOpenDeleteAccountModal(false);
                    setSelectedRow(null);
                  }}
                />
              </div>
            </div>
          }
        />

        {/* Edit user  */}
        <Modal
          isOpen={openEditAccountModal}
          header={
            <div className="flex flex-col gap-1.5">
              <h3 className="text-xl text-black font-bold">
                {t('pages.admins.editSubadmin')}
              </h3>
            </div>
          }
          onClose={() => {
            setOpenEditAccountModal(false);
            setSelectedRow(null);
            reset();
          }}
          children={
            <EditSubAdminManagement
              control={control}
              errors={errors}
              handleSubmit={handleSubmit}
              onSubmit={onEditSubmit}
              reset={reset}
            />
          }
          footerButton
          onSave={handleSubmit(onEditSubmit)}
          onCancel={() => {
            setOpenEditAccountModal(false);
            setSelectedRow(null);
            reset();
          }}
          saveLoading={updateItemIsLoading}
          cancelLoading={updateItemIsLoading}
        />

        {/* Add user  */}
        <Modal
          isOpen={openAddAccountModal}
          header={
            <div className="flex flex-col gap-1.5">
              <h3 className="text-xl text-black font-bold">
                {t('pages.admins.addSubAdmin')}
              </h3>
            </div>
          }
          onClose={() => {
            setOpenAddAccountModal(false);
          }}
          children={
            <AddSubAdminManagement
              onCancel={() => {
                setOpenAddAccountModal(false);
              }}
            />
          }
        />
      </ActionBarLayout>
    </ContentLayout>
  );
}

export default AdminManagement;
