import { InputField, InputSelect } from '@Components/UI';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { useGetRoleList } from '@Query/Hooks/useAdmin';
import { t } from 'i18next';
import { useEffect } from 'react';
import {
  Control,
  Controller,
  FieldErrors,
  UseFormHandleSubmit,
  UseFormReset,
} from 'react-hook-form';

interface SubAdminAddFormValues {
  first_name: string;
  last_name: string;
  email: string;
  role_id: { label: string; value: string };
}

interface EditSubAdminProps {
  control: Control<SubAdminAddFormValues>;
  reset: UseFormReset<SubAdminAddFormValues>;
  handleSubmit: UseFormHandleSubmit<SubAdminAddFormValues>;
  errors: FieldErrors<SubAdminAddFormValues>;
  onSubmit: (data: SubAdminAddFormValues) => void;
}

function EditSubAdminManagement({
  control,
  reset,
  handleSubmit,
  errors,
  onSubmit,
}: EditSubAdminProps) {
  const { addToast } = useToast();

  const {
    data: roleListData,
    isError: roleListIsError,
    error: roleListError,
    isLoading: roleListLoading,
  } = useGetRoleList();

  useEffect(() => {
    if (roleListIsError) {
      addToast('error', roleListError as string);
    }
  }, [roleListIsError]);

  return (
    <div className="flex flex-col gap-4 max-w-lg">
      <form
        onSubmit={handleSubmit(onSubmit)}
        onReset={() => reset()}
        className="flex flex-col gap-4"
      >
        <Controller
          control={control}
          name="first_name"
          render={({ field }) => (
            <InputField
              label={t('profile.fname')}
              placeholder={t('profile.fFname')}
              field={field}
              type="text"
              errorMessage={errors?.first_name?.message}
            />
          )}
        />
        <Controller
          control={control}
          name="last_name"
          render={({ field }) => (
            <InputField
              label={t('profile.lname')}
              placeholder={t('profile.fLname')}
              field={field}
              type="text"
              errorMessage={errors?.last_name?.message}
            />
          )}
        />
        <Controller
          control={control}
          name="email"
          render={({ field }) => (
            <InputField
              label={t('profile.email')}
              placeholder={t('profile.fEmail')}
              disabled={true}
              field={field}
              type="text"
              errorMessage={errors?.email?.message}
            />
          )}
        />
        <Controller
          control={control}
          name="role_id"
          render={({ field }) => (
            <InputSelect
              label={'Select role'}
              field={field}
              options={roleListData?.data?.data?.list?.map(
                (preDatas: { id: number; name: string }) => ({
                  label: preDatas.name,
                  value: preDatas.id.toString(),
                })
              )}
              isLoading={roleListLoading}
              errorMessage={errors?.role_id?.message}
            />
          )}
        />
      </form>
    </div>
  );
}

export default EditSubAdminManagement;
