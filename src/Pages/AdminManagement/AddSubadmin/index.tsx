import { But<PERSON>, InputField, InputSelect } from '@Components/UI';
import { useCallback, useEffect } from 'react';
import { t } from 'i18next';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { EMAIL_REGX, NO_SPEC_CHARACTER } from '@Helpers/Utils';
import { useAddSubAdmin, useGetRoleList } from '@Query/Hooks/useAdmin';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import ContentLayout from '@Components/Layout/ContentLayout';
import PATHS from '@Config/Path.Config';
import { useNavigate } from 'react-router';
import { MODULE_KEY } from '@Helpers/Constants';
import useHasAccess from '@Hooks/useHasAccess';

interface SelectOption {
  label: string;
  value: string;
}
interface SubAdminAddFormValues {
  first_name: string;
  last_name: string;
  email: string;
  role_id: SelectOption;
}

function AddSubAdminManagement({ onCancel }: any) {
  const module_access = useHasAccess(MODULE_KEY.ADMINS, [
    'read',
    'write',
    'update',
    'delete',
  ]);
  const { addToast } = useToast();
  const navigate = useNavigate();
  const {
    data: roleListData,
    isError: roleListIsError,
    error: roleListError,
    isLoading: roleListLoading,
  } = useGetRoleList();

  const {
    mutate: addSubAdminMutation,
    isLoading,
    isError,
    error,
    isSuccess,
    data,
  } = useAddSubAdmin();

  const schema = {
    first_name: yup
      .string()
      .strict()
      .trim(t('nospace'))
      .required(t('profile.validation.firstnameRequired'))
      .matches(NO_SPEC_CHARACTER, t('onlyAlphabates'))
      .min(3, t('profile.validation.validFirstname'))
      .max(50, t('profile.validation.firstnameGreaterthan')),

    last_name: yup
      .string()
      .strict()
      .trim(t('nospace'))
      .required(t('profile.validation.lastnameRequired'))
      .matches(NO_SPEC_CHARACTER, t('onlyAlphabates'))
      .min(1, t('profile.validation.validLastname'))
      .max(50, t('profile.validation.lastnameGreaterthan')),
    email: yup
      .string()
      .required(t('authentication.validation.emailRequired'))
      .matches(EMAIL_REGX, t('authentication.validation.validEmail'))
      .max(150, t('authentication.validation.emailMaxLength')),
    role_id: yup.mixed().required('Role is required'),
  };

  const createSchema = () =>
    yup.object().shape({
      ...schema,
    });

  const {
    handleSubmit,
    control,
    reset,
    formState: { errors },
  } = useForm<SubAdminAddFormValues>({
    defaultValues: {
      first_name: '',
      last_name: '',
      email: '',
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    resolver: yupResolver(createSchema()) as any,
    mode: 'onChange',
  });

  const onSubmit = useCallback(
    async (formData: SubAdminAddFormValues): Promise<void> => {
      addSubAdminMutation({
        ...formData,
        role_id: Number(formData?.role_id?.value),
      });
    },
    [addSubAdminMutation]
  );

  useEffect(() => {
    if (isError) {
      addToast('error', error as string);
    }

    if (isSuccess) {
      reset();
      addToast('success', data?.data?.message);
      navigate(PATHS.ADMINMANAGEMENT);
      onCancel();
    }
  }, [isError, isSuccess]);

  useEffect(() => {
    if (roleListIsError) {
      addToast('error', roleListError as string);
    }
  }, [roleListIsError]);

  return (
    // <ContentLayout
    //   title={'Create a new sub admin'}
    //   subtitle="Complete the form and submit the details to add a new sub admin."
    // >

    <form
      className="max-w-full w-full"
      onSubmit={handleSubmit(onSubmit)}
      onReset={() => reset()}
    >
      <div className="grid grid-cols-1 gap-4 ">
        <Controller
          control={control}
          name="first_name"
          render={({ field }) => (
            <InputField
              label={t('profile.fname')}
              placeholder={t('profile.fFname')}
              disabled={false}
              field={field}
              type="text"
              errorMessage={errors?.first_name?.message}
            />
          )}
        />
        <Controller
          control={control}
          name="last_name"
          render={({ field }) => (
            <InputField
              label={t('profile.lname')}
              placeholder={t('profile.fLname')}
              disabled={false}
              field={field}
              type="text"
              errorMessage={errors?.last_name?.message}
            />
          )}
        />
        <Controller
          control={control}
          name="email"
          render={({ field }) => (
            <InputField
              label={t('profile.email')}
              placeholder={t('profile.fEmail')}
              disabled={false}
              field={field}
              type="text"
              errorMessage={errors?.email?.message}
            />
          )}
        />
        <Controller
          control={control}
          name="role_id"
          render={({ field }) => (
            <InputSelect
              label={'Select role'}
              field={field}
              options={roleListData?.data?.data?.list?.map(
                (preDatas: { id: number; name: string }) => ({
                  label: preDatas.name,
                  value: preDatas.id.toString(),
                })
              )}
              isLoading={roleListLoading}
              errorMessage={
                errors.role_id?.message
                  ? String(errors.role_id.message)
                  : undefined
              }
            />
          )}
        />
      </div>
      {/* {module_access?.write && ( */}
      <div className="pt-8 flex items-center justify-between w-full">
        <Button
          text={t('cancel')}
          type="reset"
          variant="outline"
          disabled={!module_access?.write || isLoading}
          width="w-[182px]"
          onClick={() => onCancel()}
        />
        <Button
          text={t('submit')}
          type="submit"
          disabled={isLoading}
          loading={isLoading}
          width="w-[182px]"
        />
      </div>
      {/* )} */}
    </form>
    // </ContentLayout>
  );
}

export default AddSubAdminManagement;
