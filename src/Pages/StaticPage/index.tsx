import ActionButtons from '@Components/Common/ActionButtons';
import ContentLayout from '@Components/Layout/ContentLayout';
import { Button, DataTable, InputField, Modal } from '@Components/UI';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { DateConvertor } from '@Helpers/Utils';
import { CloseIcon } from '@Icons';
import * as yup from 'yup';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

import {
  useDeleteStaticPage,
  useGetStaticPages,
  useUpdateStaticPage,
} from '@Query/Hooks/useStaticPage';
import { ColumnDef, SortingState } from '@tanstack/react-table';
import { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import clsx from 'clsx';
import useHasAccess from '@Hooks/useHasAccess';
import { MODULE_KEY } from '@Helpers/Constants';
import ActionBarLayout from '@Components/Layout/ActionBarLayout';

type SearchableColumnDef<T> = ColumnDef<T> & {
  search?: boolean;
  vKey?: string;
};

interface StaticPages {
  id: number;
  title: string;
  slug: string;
  content: string;
  status: string;
  updated_at: string;
}

interface PagesFormValues {
  title: string;
  content: string;
  slug: string;
}

function StaticPages() {
  const { addToast } = useToast();

  const module_access = useHasAccess(MODULE_KEY.STATIC_PAGES, [
    'read',
    'write',
    'update',
    'delete',
  ]);

  const [openDeleteListItemModal, setOpenDeleteListItemModal] =
    useState<boolean>(false);
  const [openViewModal, setOpenViewModal] = useState<boolean>(false);
  const [openEditModal, setOpenEditModal] = useState<boolean>(false);

  const [selectedRow, setSelectedRow] = useState<StaticPages | null>();

  const [sorting, setSorting] = useState<SortingState>([
    { id: 'id', desc: true },
  ]);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(100);

  const columns: SearchableColumnDef<StaticPages>[] = [
    // {
    //   header: 'ID',
    //   accessorKey: 'id',
    //   enableSorting: false,
    //   cell: (info) => info.getValue(), // Custom cell renderer
    // },
    {
      header: 'Page title',
      accessorKey: 'title',
      enableSorting: false,
      cell: (info) => info.getValue(),
    },
    {
      header: 'Last updated',
      accessorKey: 'updated_at',
      enableSorting: false,
      cell: (info) => {
        const row = info.row.original;
        return row.updated_at !== null ? DateConvertor(row.updated_at) : '';
      },
    },
    // {
    //   header: 'Publish/Unpublish',
    //   accessorKey: 'publishUnpublish',
    //   enableSorting: false,
    //   cell: (info) => {
    //     const row = info.row.original;
    //     return (
    //       <Button
    //         variant="primary"
    //         style={{ height: 30, width: 100, padding: 0 }}
    //         text={row?.status === 'active' ? 'Unpublish' : 'Publish'}
    //         onClick={() =>
    //           updateListItem({
    //             status: row?.status === 'active' ? 'inactive' : 'active',
    //             listId: row?.id,
    //           })
    //         }
    //       />
    //     );
    //   },
    // },
    // {
    //   header: 'Status',
    //   accessorKey: 'status',
    //   enableSorting: false,
    //   cell: (info) => {
    //     const row = info.row.original;
    //     return (
    //       <text
    //         className={clsx(
    //           row.status === 'active' ? 'text-primary-100' : 'text-amber-400',
    //           'font-bold uppercase'
    //         )}
    //       >
    //         {row.status === 'active' ? 'Published' : 'Draft'}
    //       </text>
    //     );
    //   },
    // },
    {
      header: 'Action',
      accessorKey: 'action',
      enableSorting: false,
      cell: (info) => (
        <ActionButtons
          editIcon
          viewIcon
          editIconDisabled={!module_access?.update}
          onView={() => {
            setSelectedRow(info.row.original);
            setOpenViewModal(true);
          }}
          // onDelete={() => {
          //   setSelectedRow(info.row.original);
          //   setOpenDeleteListItemModal(true);
          // }}
          onEdit={() => {
            setSelectedRow(info.row.original);
            setOpenEditModal(true);
          }}
        />
      ),
    },
  ];

  const {
    data: staticPagesList,
    isError: staticPagesListError,
    isLoading: staticPagesListIsLoading,
    refetch: refetchStaticPagesDataList,
    isFetching: staticPagesDataListIsFetching,
    error: staticPageListErrorMessage,
  } = useGetStaticPages({
    ordering:
      sorting.length > 0
        ? `${sorting[0]?.desc === true ? '-' + sorting[0]?.id : sorting[0]?.id}`
        : 'first_name',
    page: currentPage,
    limit: pageSize,
  });

  const {
    mutate: deleteListItem,
    error: deleteListItemError,
    isError: isErrorDeleteListItem,
    isSuccess: isSuccessDeleteListItem,
    data: deleteListItemData,
    isLoading: isLoadingDeleteListItem,
  } = useDeleteStaticPage({
    listItemId: selectedRow ? selectedRow?.id : 0,
  });

  const {
    mutate: updateListItem,
    error: updateListItemError,
    isError: isErrorUpdateListItem,
    isSuccess: isSuccessUpdateListItem,
    data: updateListItemData,
    isLoading: isLoadingUpdateListItem,
  } = useUpdateStaticPage();

  const pagesAddSchema: yup.ObjectSchema<PagesFormValues> = yup.object({
    title: yup
      .string()
      .trim()
      .required('Page title cannot be empty')
      .min(3, 'Title must be at least 3 characters long')
      .max(100, 'Page title cannot exceed 100 characters'),
    content: yup
      .string()
      .trim()
      .required('Page body cannot be empty')
      .min(10, 'Page body must be at least 10 characters long'),
    slug: yup
      .string()
      .trim()
      .matches(
        /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
        'Slug must be lowercase and can contain hyphens'
      )
      .max(50, 'Slug cannot exceed 50 characters')
      .required('Slug is required'),
  });

  const currentData = staticPagesList?.data?.data?.list;

  const onPageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const onPageSizeChange = (newSize: number) => {
    setPageSize(newSize);
    setCurrentPage(1); // Reset to first page when page size changes
  };

  const {
    handleSubmit,
    control,
    setValue,
    clearErrors,
    formState: { errors },
  } = useForm<PagesFormValues>({
    defaultValues: {
      content: '',
      title: '',
      slug: '',
    },
    resolver: yupResolver(pagesAddSchema),
    mode: 'onChange',
  });

  useEffect(() => {
    if (staticPagesListError) {
      addToast('error', staticPageListErrorMessage as string);
    }

    if (isErrorDeleteListItem) {
      addToast('error', deleteListItemError as string);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    staticPagesListError,
    staticPageListErrorMessage,
    deleteListItemError,
    isErrorDeleteListItem,
  ]);

  useEffect(() => {
    refetchStaticPagesDataList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sorting, pageSize, currentPage]);

  useEffect(() => {
    if (isSuccessDeleteListItem) {
      setOpenDeleteListItemModal(false);
      setSelectedRow(null);
      addToast('success', deleteListItemData?.data?.message);
      refetchStaticPagesDataList();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSuccessDeleteListItem]);

  useEffect(() => {
    if (isSuccessUpdateListItem) {
      addToast('success', updateListItemData?.data?.message);
      refetchStaticPagesDataList();
      setOpenEditModal(false);
    }
    if (isErrorUpdateListItem) {
      addToast('error', updateListItemError as string);
    }
  }, [isSuccessUpdateListItem]);

  useEffect(() => {
    if (openEditModal && selectedRow) {
      setValue('title', selectedRow?.title);
      setValue('content', selectedRow?.content);
      setValue('slug', selectedRow?.slug);
    }
  }, [openEditModal, selectedRow]);

  const sanitizeHtml = (value: string) => {
    const strippedValue = value
      .replace(/<[^/>]+><br\s*\/?><\/[^>]+>/g, '')
      .trim();
    return strippedValue.length === 0 ? '' : value;
  };

  const onSubmit = async (formData: PagesFormValues): Promise<void> => {
    const data = { ...formData, listId: selectedRow?.id ? selectedRow?.id : 0 };
    updateListItem({
      ...data,
    });
  };

  return (
    <ContentLayout>
      <ActionBarLayout>
        <DataTable
          data={currentData}
          columns={columns}
          sorting={sorting}
          setSorting={setSorting}
          totalPages={staticPagesList?.data?.data?.total_pages}
          currentPage={currentPage}
          onPageChange={onPageChange}
          pageSize={pageSize}
          setPageSize={setPageSize}
          pageSizeOptions={[10, 20, 30, 50]}
          onPageSizeChange={onPageSizeChange}
          isPagination={false}
          loading={staticPagesListIsLoading || staticPagesDataListIsFetching} // set to true if loading data from an API
        />
        <Modal
          isOpen={openDeleteListItemModal}
          hideCloseIcon
          onClose={() => {
            setOpenDeleteListItemModal(false);
            setSelectedRow(null);
          }}
          children={
            <div className="flex flex-col items-center gap-[36px] ">
              <div className="bg-[#FFD8E0] h-14 w-14 flex justify-center items-center rounded-full">
                <CloseIcon width={18} height={18} fill="#FF3B30" />
              </div>
              <div className="text-black font-bold text-center w-full">
                Are you sure want to delete{' '}
                <b className="text-error-0">{selectedRow?.title}</b> page?
              </div>
              <div className=" w-full flex justify-center gap-6">
                <Button
                  onClick={deleteListItem}
                  text="Yes"
                  variant="other"
                  className="bg-error-0 text-white"
                  loading={isLoadingDeleteListItem}
                />
                <Button
                  text="No"
                  variant="outline"
                  disabled={isLoadingDeleteListItem}
                  onClick={() => {
                    setOpenDeleteListItemModal(false);
                    setSelectedRow(null);
                  }}
                />
              </div>
            </div>
          }
        />

        <Modal
          size="xl"
          header={selectedRow?.title}
          isOpen={openViewModal}
          onClose={() => {
            setOpenViewModal(false);
            setSelectedRow(null);
          }}
        >
          <div
            className="reset-styles"
            dangerouslySetInnerHTML={{ __html: selectedRow?.content as string }}
          />
        </Modal>

        <Modal
          size="dxl"
          header={'Edit ' + selectedRow?.title + ' page'}
          isOpen={openEditModal}
          footerButton
          onCancel={() => {
            setOpenEditModal(false);
            setSelectedRow(null);
            clearErrors();
          }}
          saveLoading={isLoadingUpdateListItem}
          onSave={handleSubmit(onSubmit)}
          onClose={() => {
            setOpenEditModal(false);
            setSelectedRow(null);
          }}
        >
          <form className="flex gap-3 flex-col">
            <Controller
              control={control}
              name="title"
              render={({ field }) => (
                <InputField
                  field={field}
                  label="Page title"
                  placeholder="Enter page title"
                  errorMessage={errors?.title?.message}
                />
              )}
            />
            <Controller
              control={control}
              name="slug"
              render={({ field }) => (
                <InputField
                  field={field}
                  disabled
                  label="Page slug"
                  placeholder="Enter page slug"
                  errorMessage={errors?.slug?.message}
                />
              )}
            />
            <Controller
              name="content"
              control={control}
              render={({ field }) => (
                <>
                  <label
                    className={clsx(
                      'text-sm font-medium text-left text-gray-700 pt-2'
                    )}
                  >
                    Page body
                  </label>
                  <ReactQuill
                    {...field}
                    value={field.value}
                    onChange={(value) => field.onChange(sanitizeHtml(value))}
                  />
                  {errors?.content?.message && (
                    <p className="text-red-500 text-sm break-words">
                      {errors?.content?.message}
                    </p>
                  )}
                </>
              )}
            />
          </form>
        </Modal>
      </ActionBarLayout>
    </ContentLayout>
  );
}

export default StaticPages;
