import ActionButtons from '@Components/Common/ActionButtons';
import ActionBarLayout from '@Components/Layout/ActionBarLayout';
import ContentLayout from '@Components/Layout/ContentLayout';
import { Button, DataTable, InputField, Modal } from '@Components/UI';
import Text<PERSON>reaField from '@Components/UI/InputField/TextAreaField';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { MODULE_KEY } from '@Helpers/Constants';
import { DateConvertor2 } from '@Helpers/Utils';
import { yupResolver } from '@hookform/resolvers/yup';
import { useDebounce } from '@Hooks/useDebounce';
import useHasAccess from '@Hooks/useHasAccess';
import { AddIcon, SearchIcon } from '@Icons';
import {
  useEditTemplate,
  useGetEmailTemplatesList,
} from '@Query/Hooks/useNotification';
import { ColumnDef, SortingState } from '@tanstack/react-table';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';

interface NotificationTemplatesType {
  id: number;
  type: number;
  platform_type: string;
  title: string;
  body: string;
  created_at: string;
  updated_at: string;
}

interface FormValues {
  title: string;
  body: string;
}

const EmailTemplates = () => {
  const { addToast } = useToast();
  const module_access = useHasAccess(MODULE_KEY.NOTIFICATION, [
    'read',
    'write',
    'update',
    'delete',
  ]);

  const [isOpenView, setIsOpenView] = useState<boolean>(false);

  const [isOpenAddModal, setIsOpenAddModal] = useState<boolean>(false);

  const [sorting, setSorting] = useState<SortingState>([
    { id: 'created_at', desc: true },
  ]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchText, setSearchText] = useState<string>('');
  const [selectedRow, setSelectedRow] = useState<any>(null);

  const columns: ColumnDef<NotificationTemplatesType>[] = [
    {
      header: 'Title',
      accessorKey: 'title',
      enableSorting: false,
      cell: (info) => info.getValue(),
    },
    {
      header: 'Action',
      accessorKey: 'action',
      enableSorting: false,
      cell: (info) => (
        <div className="flex gap-4">
          <ActionButtons
            editIcon
            viewIcon
            editIconDisabled={!module_access?.update}
            onView={() => {
              setSelectedRow(info.row.original);
              setIsOpenView(true);
            }}
            onEdit={() => {
              setSelectedRow(info.row.original);
              setValue('title', info.row.original?.title);
              setValue('body', info.row.original?.body);
              // setEditVideoValue('title', info.row.original?.title);
              // setEditVideoValue(
              //   'thumbnail_img',
              //   // eslint-disable-next-line @typescript-eslint/no-explicit-any
              //   info.row.original?.presigned_thumbnail_url as any
              // );
              // setEditVideoValue('author_name', info.row?.original?.author_name);
              // setEditVideoValue('price', info.row?.original?.price);
              // setEditVideoValue(
              //   'description',
              //   info?.row?.original?.description
              // );
              // setEditVideoValue('category', {
              //   label: info?.row?.original?.category_name,
              //   value: info?.row?.original?.category,
              // });
              // setOpenEditVideoModal(true);
              setIsOpenAddModal(true);
            }}
          />
        </div>
      ),
    },
  ];

  const {
    data: templateList,
    isError: templateListError,
    isLoading: templateListLoading,
    refetch: templateListRefetch,
    isFetching: templateListIsRefetching,
    error: templateListErrorMsg,
  } = useGetEmailTemplatesList({
    ordering:
      sorting.length > 0
        ? `${sorting[0]?.desc === true ? '-' + sorting[0]?.id : sorting[0]?.id}`
        : '-created_at',
    page: currentPage,
    limit: pageSize,
    search: searchText,
    isSearchable: searchText.length > 0,
    platform: 'email',
  });

  useEffect(() => {
    if (templateListError) {
      addToast('error', templateListErrorMsg as string);
    }
  }, [templateListError, templateListErrorMsg]);

  const currentData = templateList?.data?.data?.list;

  const onPageSizeChange = (newSize: number) => {
    setPageSize(newSize);
    setCurrentPage(1); // Reset to first page when page size changes
  };

  const onPageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  useEffect(() => {
    templateListRefetch();
  }, [sorting, pageSize, currentPage]);

  useEffect(() => {
    if (searchText.length > 0) {
      setCurrentPage(1);
    }
  }, [searchText]);

  const debouncedSearchText = useDebounce(searchText, 500);

  useEffect(() => {
    templateListRefetch();
  }, [debouncedSearchText]);

  const MAX_TITLE_LENGTH = 100;
  const MAX_BODY_LENGTH = 2000;
  const MIN_BODY_LENGTH = 10;

  const schema = yup.object().shape({
    title: yup
      .string()
      .trim()
      .required('Notification title is required')
      .max(
        MAX_TITLE_LENGTH,
        `Title must be less than ${MAX_TITLE_LENGTH} characters`
      )
      .test('no-html', 'HTML tags are not allowed in the title', (value) =>
        value ? !/<\/?[a-z][\s\S]*>/i.test(value) : true
      ),

    body: yup
      .string()
      .trim()
      .required('Notification body is required')
      .min(
        MIN_BODY_LENGTH,
        `Body must be at least ${MIN_BODY_LENGTH} characters`
      )
      .max(
        MAX_BODY_LENGTH,
        `Body must be less than ${MAX_BODY_LENGTH} characters`
      ),
  });

  // Form setup
  const {
    control,
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
  } = useForm<FormValues>({
    resolver: yupResolver(schema),
    mode: 'onChange',
  });
  const { data, isLoading, isError, error, isSuccess, mutate } =
    useEditTemplate();

  useEffect(() => {
    if (isError) {
      addToast('error', error as string);
    }
  }, [isError, error]);

  useEffect(() => {
    if (isSuccess) {
      addToast('success', data?.data?.message);
      templateListRefetch();
      setIsOpenAddModal(false);
    }
  }, [isSuccess]);

  const onSubmit = async (formData: FormValues): Promise<void> => {
    mutate({ ...formData, id: selectedRow?.id });
  };

  return (
    <ContentLayout>
      <ActionBarLayout
        actionbarChildren={
          <React.Fragment>
            <div className="w-[400px]">
              <InputField
                placeholder="Search..."
                inputContainerClassName="max-h-[46px] w-max "
                className="p-0"
                containerClassName="p-0"
                onChange={(i) => setSearchText(i.target.value)}
                rightIcon={<SearchIcon height={18} width={18} />}
              />
            </div>
          </React.Fragment>
        }
      >
        <DataTable
          data={currentData}
          columns={columns}
          sorting={sorting}
          setSorting={setSorting}
          totalPages={templateList?.data?.data?.total_pages}
          currentPage={currentPage}
          onPageChange={onPageChange}
          pageSize={pageSize}
          setPageSize={(num) => {
            setPageSize(num);
            setCurrentPage(1);
          }}
          pageSizeOptions={[10, 20, 30, 50]}
          onPageSizeChange={onPageSizeChange}
          isPagination={true}
          loading={templateListLoading || templateListIsRefetching} // set to true if loading data from an API
        />

        <Modal
          isOpen={isOpenAddModal}
          header="Add Notification Template"
          footerButton
          size="xl"
          onSave={handleSubmit(onSubmit)}
          onClose={() => {
            setIsOpenAddModal(false);
            reset();
          }}
          onCancel={() => {
            setIsOpenAddModal(false);
            reset();
          }}
          cancelLoading={isLoading}
          saveLoading={isLoading}
        >
          <form>
            <div>
              <Controller
                name="title"
                control={control}
                render={({ field }) => (
                  <InputField
                    label="Title"
                    field={field}
                    placeholder="Title"
                    errorMessage={errors.title?.message}
                    autoFocus
                  />
                )}
              />
            </div>
            <div>
              <Controller
                name="body"
                control={control}
                render={({ field }) => (
                  <TextAreaField
                    label="Body"
                    field={field}
                    placeholder="Body"
                    errorMessage={errors.body?.message}
                    autoFocus
                    rows={10}
                  />
                )}
              />
              <div className="bg-error-100 px-2 rounded">
                <span className="text-xs font-bold text-error-0">
                  Note: Please do not change any word which is enclosed in
                  braces and ||| (it's for new line)
                  {}
                </span>
              </div>
            </div>
          </form>
        </Modal>

        <Modal
          isOpen={isOpenView}
          size="xl"
          header={selectedRow?.title}
          onClose={() => {
            setIsOpenView(false);
            reset();
          }}
        >
          <div className="flex justify-center items-center">
            <div className="bg-white rounded-lg shadow-lg max-w-lg w-full overflow-hidden">
              {/* Email Header */}
              <div className="bg-[#FF8800] text-white text-center py-6 rounded-t-lg">
                {/* Logo */}
                <img
                  src="https://rex-construction-web.openxcell.dev/assets/RexLogo-Cej96ivp.png"
                  alt="Company Logo"
                  className="mx-auto rounded-lg bg-white p-2"
                  style={{ maxWidth: '150px' }}
                />
              </div>

              {/* Main Content Block */}
              <div className="p-6 text-gray-800 leading-relaxed text-base">
                {/* Replace this block content dynamically */}
                <div
                  dangerouslySetInnerHTML={{
                    __html: selectedRow?.body?.replace(/\|\|\|/g, '<br />'),
                  }}
                />
              </div>

              {/* Email Footer */}
              <div className="bg-[#FF8800] text-white text-center py-4 rounded-b-lg text-sm">
                <p>
                  &copy; {new Date().getFullYear()} REX Construction. All rights
                  reserved.
                </p>
              </div>
            </div>
          </div>
        </Modal>
      </ActionBarLayout>
    </ContentLayout>
  );
};

export default EmailTemplates;
