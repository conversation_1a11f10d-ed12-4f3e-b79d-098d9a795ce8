import ActionButtons from '@Components/Common/ActionButtons';
import ContentLayout from '@Components/Layout/ContentLayout';
import {
  Button,
  DataTable,
  InputField,
  InputSelect,
  Modal,
} from '@Components/UI';
import TextAreaField from '@Components/UI/InputField/TextAreaField';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { MODULE_KEY } from '@Helpers/Constants';
import { DateConvertor2 } from '@Helpers/Utils';
import { yupResolver } from '@hookform/resolvers/yup';
import { useDebounce } from '@Hooks/useDebounce';
import useHasAccess from '@Hooks/useHasAccess';
import { AddIcon, InfoIcon, SearchIcon } from '@Icons';
import {
  useEditTemplate,
  useGetEmailTemplatesList,
  useGetNotificationType,
  useSendNotification,
} from '@Query/Hooks/useNotification';
import { ColumnDef, SortingState } from '@tanstack/react-table';
import React, { useEffect, useMemo, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import NotificationView from './NotificationPreview';
import { useStateList } from '@Query/Hooks/useUser';
import ActionBarLayout from '@Components/Layout/ActionBarLayout';

interface NotificationTemplatesType {
  id: number;
  type: number;
  platform_type: string;
  title: string;
  body: string;
  created_at: string;
  updated_at: string;
}

interface FormValues {
  title: string;
  body: string;
}
interface FormValuesGB {
  state: any;
  title: string;
  body: string;
  // type: any;
}

const NotificationTemplates = () => {
  const { addToast } = useToast();
  const module_access = useHasAccess(MODULE_KEY.NOTIFICATION, [
    'read',
    'write',
    'update',
    'delete',
  ]);

  const [isOpenAddModal, setIsOpenAddModal] = useState<boolean>(false);
  const [isOpenSendModal, setIsOpenSendModal] = useState<boolean>(false);

  const [isOpenView, setIsOpenView] = useState<boolean>(false);

  const [sorting, setSorting] = useState<SortingState>([
    { id: 'created_at', desc: true },
  ]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchText, setSearchText] = useState<string>('');
  const [selectedRow, setSelectedRow] = useState<any>(null);

  interface LicenseFormat {
    format_id?: number;
    prefix: string | null;
    postfix: string | null;
    min_number: number;
    max_number: number;
  }

  interface StateData {
    id: number;
    name: string;
    license_formats: LicenseFormat[];
  }

  const { data: stateListData, isLoading: stateListLoading } = useStateList();

  // const {
  //   data: notificationType,
  //   isLoading: typeLoading,
  //   isError: typeIsError,
  //   error: typeError,
  // } = useGetNotificationType();

  const {
    mutate: sendNow,
    data: sendNotification,
    isLoading: sendNotificationLoading,
    isSuccess: sendNotificationIsSuccess,
    isError: sendNotificationIsError,
    error: sendNotificationError,
  } = useSendNotification();

  // useEffect(() => {
  //   if (typeIsError) {
  //     addToast('error', typeError as string);
  //   }
  // }, [typeError, typeIsError]);

  useEffect(() => {
    if (sendNotificationIsSuccess) {
      addToast('success', sendNotification?.data?.message);
      setIsOpenSendModal(false);
    }
  }, [sendNotificationIsSuccess]);

  useEffect(() => {
    if (sendNotificationIsError) {
      addToast('error', sendNotificationError as string);
    }
  }, [sendNotificationIsError, sendNotificationError]);

  const statesOptions = useMemo(() => {
    return (
      stateListData?.data?.data?.map((state: StateData) => ({
        label: state.name,
        value: state.id.toString(),
        license_formats: state.license_formats,
      })) || []
    );
  }, [stateListData]);

  // const typeOptions = useMemo(() => {
  //   return (
  //     notificationType?.data?.data?.map((types: any) => ({
  //       label: types.type,
  //       value: types.id.toString(),
  //     })) || []
  //   );
  // }, [notificationType]);

  const columns: ColumnDef<NotificationTemplatesType>[] = [
    {
      header: 'Title',
      accessorKey: 'title',
      enableSorting: false,
      cell: (info) => info.getValue(),
    },
    {
      header: 'Action',
      accessorKey: 'action',
      enableSorting: false,
      cell: (info) => (
        <div className="flex gap-4">
          <ActionButtons
            editIcon
            viewIcon
            editIconDisabled={!module_access?.update}
            onView={() => {
              setSelectedRow(info.row.original);
              setIsOpenView(true);
              // setOpenViewModal(true);
            }}
            onEdit={() => {
              setSelectedRow(info.row.original);
              setValue('title', info.row.original?.title);
              setValue('body', info.row.original?.body);
              // setEditVideoValue('title', info.row.original?.title);
              // setEditVideoValue(
              //   'thumbnail_img',
              //   // eslint-disable-next-line @typescript-eslint/no-explicit-any
              //   info.row.original?.presigned_thumbnail_url as any
              // );
              // setEditVideoValue('author_name', info.row?.original?.author_name);
              // setEditVideoValue('price', info.row?.original?.price);
              // setEditVideoValue(
              //   'description',
              //   info?.row?.original?.description
              // );
              // setEditVideoValue('category', {
              //   label: info?.row?.original?.category_name,
              //   value: info?.row?.original?.category,
              // });
              // setOpenEditVideoModal(true);
              setIsOpenAddModal(true);
            }}
          />
        </div>
      ),
    },
  ];

  const {
    data: templateList,
    isError: templateListError,
    isLoading: templateListLoading,
    refetch: templateListRefetch,
    isFetching: templateListIsRefetching,
    error: templateListErrorMsg,
  } = useGetEmailTemplatesList({
    ordering:
      sorting.length > 0
        ? `${sorting[0]?.desc === true ? '-' + sorting[0]?.id : sorting[0]?.id}`
        : '-created_at',
    page: currentPage,
    limit: pageSize,
    search: searchText,
    isSearchable: searchText.length > 0,
    platform: 'in-app',
  });

  useEffect(() => {
    if (templateListError) {
      addToast('error', templateListErrorMsg as string);
    }
  }, [templateListError, templateListErrorMsg]);

  const currentData = templateList?.data?.data?.list;

  const onPageSizeChange = (newSize: number) => {
    setPageSize(newSize);
    setCurrentPage(1); // Reset to first page when page size changes
  };

  const onPageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  useEffect(() => {
    templateListRefetch();
  }, [sorting, pageSize, currentPage]);

  useEffect(() => {
    if (searchText.length > 0) {
      setCurrentPage(1);
    }
  }, [searchText]);

  const debouncedSearchText = useDebounce(searchText, 500);

  useEffect(() => {
    templateListRefetch();
  }, [debouncedSearchText]);

  const MAX_TITLE_LENGTH = 100;
  const MAX_BODY_LENGTH = 2000;
  const MIN_BODY_LENGTH = 10;

  const schema = yup.object().shape({
    title: yup
      .string()
      .trim()
      .required('Notification title is required')
      .max(
        MAX_TITLE_LENGTH,
        `Title must be less than ${MAX_TITLE_LENGTH} characters`
      )
      .test('no-html', 'HTML tags are not allowed in the title', (value) =>
        value ? !/<\/?[a-z][\s\S]*>/i.test(value) : true
      ),

    body: yup
      .string()
      .trim()
      .required('Notification body is required')
      .min(
        MIN_BODY_LENGTH,
        `Body must be at least ${MIN_BODY_LENGTH} characters`
      )
      .max(
        MAX_BODY_LENGTH,
        `Body must be less than ${MAX_BODY_LENGTH} characters`
      ),
  });

  const GloablNotificationschema = yup.object().shape({
    title: yup
      .string()
      .trim()
      .required('Notification title is required')
      .max(
        MAX_TITLE_LENGTH,
        `Title must be less than ${MAX_TITLE_LENGTH} characters`
      )
      .test('no-html', 'HTML tags are not allowed in the title', (value) =>
        value ? !/<\/?[a-z][\s\S]*>/i.test(value) : true
      ),

    body: yup
      .string()
      .trim()
      .required('Notification body is required')
      .min(
        MIN_BODY_LENGTH,
        `Body must be at least ${MIN_BODY_LENGTH} characters`
      )
      .max(
        MAX_BODY_LENGTH,
        `Body must be less than ${MAX_BODY_LENGTH} characters`
      ),

    state: yup.mixed().required('State is required'),

    // type: yup.mixed().required('Type is required'),
  });

  // Form setup
  const {
    control,
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
  } = useForm<FormValues>({
    resolver: yupResolver(schema),
    mode: 'onChange',
  });

  const {
    control: GBcontrol,
    handleSubmit: GBSubmit,
    reset: GBReset,
    formState: { errors: GBErrors },
  } = useForm<FormValuesGB>({
    resolver: yupResolver(GloablNotificationschema) as any,
    mode: 'onChange',
  });

  const { data, isLoading, isError, error, isSuccess, mutate } =
    useEditTemplate();

  useEffect(() => {
    if (isError) {
      addToast('error', error as string);
    }
  }, [isError, error]);

  useEffect(() => {
    if (isSuccess) {
      addToast('success', data?.data?.message);
      templateListRefetch();
      setIsOpenAddModal(false);
    }
  }, [isSuccess]);

  const onSubmit = async (formData: FormValues): Promise<void> => {
    mutate({ ...formData, id: selectedRow?.id });
  };
  const onGBSubmit = async (formData: FormValuesGB): Promise<void> => {
    sendNow({
      ...formData,
      state: Number(formData.state.value),
      // type: Number(formData.type.value),
    });
  };

  return (
    <ContentLayout>
      <ActionBarLayout
        actionbarChildren={
          <React.Fragment>
            <div className="w-auto">
              <InputField
                placeholder="Search..."
                inputContainerClassName="max-h-[46px] w-max "
                className="p-0"
                containerClassName="p-0"
                onChange={(i) => setSearchText(i.target.value)}
                rightIcon={<SearchIcon height={18} width={18} />}
              />
            </div>
            <div>
              <Button
                disabled={!module_access.write}
                text={
                  <div className="flex gap-4 items-center justify-center text-white">
                    Send Global Notifications
                  </div>
                }
                onClick={() => setIsOpenSendModal(true)}
              />
            </div>
          </React.Fragment>
        }
      >
        <DataTable
          data={currentData}
          columns={columns}
          sorting={sorting}
          setSorting={setSorting}
          totalPages={templateList?.data?.data?.total_pages}
          currentPage={currentPage}
          onPageChange={onPageChange}
          pageSize={pageSize}
          setPageSize={(num) => {
            setPageSize(num);
            setCurrentPage(1);
          }}
          pageSizeOptions={[10, 20, 30, 50]}
          onPageSizeChange={onPageSizeChange}
          isPagination={true}
          loading={templateListLoading || templateListIsRefetching} // set to true if loading data from an API
        />

        <Modal
          isOpen={isOpenAddModal}
          header="Edit Notification Template"
          footerButton
          onSave={handleSubmit(onSubmit)}
          onClose={() => {
            setIsOpenAddModal(false);
            reset();
          }}
          onCancel={() => {
            setIsOpenAddModal(false);
            reset();
          }}
          cancelLoading={isLoading}
          saveLoading={isLoading}
        >
          <form>
            <div>
              <Controller
                name="title"
                control={control}
                render={({ field }) => (
                  <InputField
                    label="Title"
                    field={field}
                    placeholder="Title"
                    errorMessage={errors.title?.message}
                    autoFocus
                  />
                )}
              />
            </div>
            <div className="mt-4">
              <Controller
                name="body"
                control={control}
                render={({ field }) => (
                  <TextAreaField
                    label="Body"
                    field={field}
                    placeholder="Body"
                    errorMessage={errors.body?.message}
                    autoFocus
                  />
                )}
              />
              <div className="bg-error-100 px-2 rounded">
                <span className="text-xs font-bold text-error-0">
                  Note: Please do not change any word which is enclosed in
                  braces {}
                </span>
              </div>
            </div>
          </form>
        </Modal>

        <Modal
          isOpen={isOpenSendModal}
          header="Global Notification Send"
          footerButton
          onSave={GBSubmit(onGBSubmit)}
          onClose={() => {
            setIsOpenSendModal(false);
            GBReset();
          }}
          onCancel={() => {
            setIsOpenSendModal(false);
            GBReset();
          }}
          cancelLoading={sendNotificationLoading}
          saveLoading={sendNotificationLoading}
          renameSave={'Send'}
        >
          <form>
            <div className="col-span-3">
              <Controller
                name="state"
                control={GBcontrol}
                render={({ field }) => (
                  <InputSelect
                    label={'State'}
                    field={field}
                    containerClassName="pt-0"
                    placeholder="Select your state"
                    onChange={(selected) => {
                      field.onChange(selected);
                    }}
                    errorMessage={
                      GBErrors.state?.message
                        ? String(GBErrors.state.message)
                        : undefined
                    }
                    options={statesOptions}
                    isLoading={stateListLoading}
                  />
                )}
              />
            </div>
            {/* <div className="col-span-3">
            <Controller
              name="type"
              control={GBcontrol}
              render={({ field }) => (
                <InputSelect
                  label={'Type'}
                  field={field}
                  containerClassName="pt-0"
                  placeholder="Select your state"
                  onChange={(selected) => {
                    field.onChange(selected);
                  }}
                  errorMessage={
                    GBErrors.type?.message
                      ? String(GBErrors.type.message)
                      : undefined
                  }
                  options={typeOptions}
                  isLoading={typeLoading}
                />
              )}
            />
          </div> */}
            <div className="mt-4">
              <Controller
                name="title"
                control={GBcontrol}
                render={({ field }) => (
                  <InputField
                    label="Title"
                    field={field}
                    placeholder="Title"
                    errorMessage={GBErrors.title?.message}
                  />
                )}
              />
            </div>
            <div className="mt-4">
              <Controller
                name="body"
                control={GBcontrol}
                render={({ field }) => (
                  <TextAreaField
                    label="Body"
                    field={field}
                    placeholder="Body"
                    errorMessage={GBErrors.body?.message}
                  />
                )}
              />
            </div>
          </form>
        </Modal>

        <NotificationView
          title={selectedRow?.title}
          appName="Rex"
          onClose={() => setIsOpenView(false)}
          isOpen={isOpenView}
          message={selectedRow?.body}
        />
      </ActionBarLayout>
    </ContentLayout>
  );
};

export default NotificationTemplates;
