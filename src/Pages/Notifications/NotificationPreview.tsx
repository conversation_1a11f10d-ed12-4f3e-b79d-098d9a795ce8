import { AccessibleImage } from '@Components/UI';
import { CaretRightIcon, CloseIcon } from '@Icons';
import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Logo from '@Assets/Images/Logo2.png';
import { FC } from 'react';

interface NotificationPreviewProps {
  isOpen?: boolean;
  title?: string;
  message?: string;
  appName?: string;
  time?: string;
  theme?: 'light' | 'dark';
  onClose?: () => void;
  onAction?: () => void;
}

const NotificationPreview: FC<NotificationPreviewProps> = ({
  isOpen = false,
  title = 'Notification Title',
  message = 'This is the notification message that will be displayed to the user',
  appName = 'Your App',
  time = 'Just now',
  theme = 'light',
  onClose = () => undefined,
  onAction = () => undefined,
}) => {
  // Theme styles
  const themeStyles: Record<string, any> = {
    light: {
      bg: 'bg-white',
      text: 'text-gray-800',
      subText: 'text-gray-500',
      border: 'border-gray-200',
      backdropLight: 'bg-white/30',
      shadow: 'shadow-lg',
    },
    dark: {
      bg: 'bg-gray-900',
      text: 'text-gray-100',
      subText: 'text-gray-400',
      border: 'border-gray-700',
      backdropDark: 'bg-gray-900/30',
      shadow: 'shadow-xl',
    },
  };

  const currentTheme = themeStyles[theme] || themeStyles.light;

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 flex items-center justify-center z-50">
          {/* Glassmorphism Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className={`fixed inset-0 backdrop-blur-md ${
              theme === 'light' ? 'bg-white/30' : 'bg-gray-900/30'
            }`}
            onClick={onClose}
          />

          {/* Notification Card */}
          <motion.div
            initial={{ y: -20, opacity: 0, scale: 0.95 }}
            animate={{ y: 0, opacity: 1, scale: 1 }}
            exit={{ y: -20, opacity: 0, scale: 0.95 }}
            transition={{
              type: 'spring',
              stiffness: 300,
              damping: 20,
            }}
            className={`${currentTheme.bg} max-w-[500px] w-full rounded-xl p-4 ${currentTheme.shadow} 
                                             border ${currentTheme.border} z-50 relative`}
            onClick={onAction}
          >
            <div className="flex items-start">
              {/* App icon */}
              <motion.div
                initial={{ scale: 0.8 }}
                animate={{ scale: 1 }}
                transition={{ type: 'spring', stiffness: 400, delay: 0.1 }}
                className="p-2 rounded-xl mr-3 text-white relative overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-blue-900 to-blue-950 opacity-90"></div>
                <AccessibleImage
                  src={Logo}
                  alt="logo"
                  className="w-[28px] h-[25px] relative z-10"
                />
              </motion.div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <div className="flex justify-between items-start">
                  <div>
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.2 }}
                      className={`font-semibold text-sm ${currentTheme.text}`}
                    >
                      {appName}
                    </motion.div>
                  </div>
                  <motion.button
                    whileHover={{ rotate: 90 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={(e) => {
                      e.stopPropagation();
                      onClose();
                    }}
                    className={`p-1 rounded-full ${theme === 'dark' ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
                  >
                    <CloseIcon height={16} className={currentTheme.text} />
                  </motion.button>
                </div>

                <motion.div
                  initial={{ opacity: 0, y: 5 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                  className={`mt-1 ${currentTheme.text} font-medium break-words`}
                >
                  {title}
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, y: 5 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  className={`mt-1 text-sm ${currentTheme.subText} break-words`}
                >
                  {message}
                </motion.div>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default NotificationPreview;
