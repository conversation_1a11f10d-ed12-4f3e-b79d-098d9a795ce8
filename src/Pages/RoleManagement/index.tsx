/* eslint-disable @typescript-eslint/no-explicit-any */
import ActionButtons from '@Components/Common/ActionButtons';
import ContentLayout from '@Components/Layout/ContentLayout';
import * as yup from 'yup';

import {
  Button,
  CustomCheckbox,
  DataTable,
  InputField,
  Modal,
} from '@Components/UI';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { yupResolver } from '@hookform/resolvers/yup';
import { AddIcon, CloseIcon, Loader } from '@Icons';
import { useGetRoleList } from '@Query/Hooks/useAdmin';
import {
  useDeleteRole,
  useGetRolePermission,
  useGetRolePermissionById,
  useGetUserCountByRole,
  useSaveRolePermission,
  useUpdateRolePermissionById,
} from '@Query/Hooks/useRolePermission';
// import { useGetRolePermission } from '@Query/Hooks/useRolePermission';
import { ColumnDef, SortingState } from '@tanstack/react-table';
import React, { useEffect, useMemo, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import ActionBarLayout from '@Components/Layout/ActionBarLayout';
interface Role {
  id: number;
  name: string;
}

type SearchableColumnDef<T> = ColumnDef<T> & {
  search?: boolean;
  vKey?: string;
};

interface AccessPermissions {
  read?: boolean;
  write?: boolean;
  update?: boolean;
  delete?: boolean;
}

interface RolePermissionForm {
  name: string;
}

interface RolePermission {
  id?: number;
  name: string;
  access?: AccessPermissions;
}

function formatName(name: string): string {
  return name
    .split('-')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

export default function RoleManagement() {
  const [rolePermissions, setRolePermissions] = useState<RolePermission[]>([]);

  const [selectedRow, setSelectedRow] = useState<Role | null>(null);
  const [openDeleteRoleModal, setOpenDeleteRoleModal] =
    useState<boolean>(false);
  const [isEdit, setIsEdit] = useState<boolean>(false);

  const [openRolePermissionModal, setOpenRolePermissionModal] =
    useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const [sorting, setSorting] = useState<SortingState>([
    { id: 'id', desc: true },
  ]);

  const { addToast } = useToast();

  const {
    data: roleListData,
    refetch: refetchRoleList,
    isError: roleListIsError,
    error: roleListError,
    isLoading: roleListLoading,
  } = useGetRoleList();

  const {
    data: userCountData,
    isError: userCountIsError,
    refetch: userCountRefetch,
    error: userCountError,
    isSuccess: userCountIsSuccess,
    isLoading: userCountIsLoading,
    isRefetching: userCountIsRefetching,
  } = useGetUserCountByRole(
    selectedRow ? Number(selectedRow.id) : 1,
    selectedRow !== null
  );

  const {
    data: deleteRoleData,
    mutate: deleteRole,
    isError: deleteRoleDataIsError,
    error: deleteRoleDataError,
    isSuccess: deleteRoleDataIsSuccess,
    isLoading: deleteRoleDataIsLoading,
  } = useDeleteRole();

  const {
    data: rolePermissionListData,
    // refetch: refetchRolePermissionList,
    isError: rolePermissionListIsError,
    error: rolePermissionListError,
    isLoading: rolePermissionListLoading,
  } = useGetRolePermission();

  const {
    mutate: savedRolePermission,
    data: savedRolePermissionData,
    isSuccess: saveRolePermissionSuccess,
    isError: savedRolePermissionIsError,
    error: savedRolePermissionError,
    isLoading: savedRolePermissionisLoading,
  } = useSaveRolePermission();

  const {
    mutate: updateRolePermission,
    data: updateRolePermissionData,
    isSuccess: updateRolePermissionSuccess,
    isError: updateRolePermissionIsError,
    error: updateRolePermissionError,
    isLoading: updateRolePermissionisLoading,
  } = useUpdateRolePermissionById();

  useEffect(() => {
    if (updateRolePermissionSuccess) {
      addToast('success', updateRolePermissionData?.data?.message);
      setOpenRolePermissionModal(false);
      setRolePermissions([]);
      refetchRoleList();
      setSelectedRow(null);
      setIsEdit(false);
    }
    if (updateRolePermissionIsError) {
      addToast('error', updateRolePermissionError as string);
    }
  }, [
    updateRolePermissionSuccess,
    updateRolePermissionIsError,
    updateRolePermissionError,
    updateRolePermissionData,
  ]);

  useEffect(() => {
    if (userCountIsError) {
      addToast('error', userCountError as string);
    }
  }, [userCountIsError, userCountError, userCountIsSuccess]);

  useEffect(() => {
    if (deleteRoleDataIsError) {
      addToast('error', deleteRoleDataError as string);
    }
  }, [deleteRoleDataIsError, deleteRoleDataError]);

  useEffect(() => {
    if (deleteRoleDataIsSuccess) {
      addToast('success', deleteRoleData?.data?.message);
      setSelectedRow(null);
      refetchRoleList();
      setOpenDeleteRoleModal(false);
    }
  }, [deleteRoleDataIsSuccess, deleteRoleData]);

  const {
    data: getRoleById,
    refetch: getRoleByIdRefetch,
    isError: getRoleByIdIsError,
    isSuccess: getRoleByIdIsSuccess,
    error: getRoleByIdError,
    isRefetching: getRoleByIdRefetching,
  } = useGetRolePermissionById(
    selectedRow !== null ? Number(selectedRow?.id) : 1,
    selectedRow !== null ? true : false
  );

  const columns: SearchableColumnDef<Role>[] = [
    // {
    //   header: 'ID',
    //   accessorKey: 'id',
    //   cell: (info) => info.getValue(), // Custom cell renderer
    // },
    {
      header: 'Role name',
      accessorKey: 'name',
      vKey: 'name',
      search: true,
      enableSorting: false,
      cell: (info) => info.getValue(),
    },
    {
      header: 'Action',
      accessorKey: 'action',
      enableSorting: false,
      cell: (info) => (
        <ActionButtons
          editIcon
          deleteIcon
          onDelete={() => {
            setSelectedRow(info.row.original);
            setOpenDeleteRoleModal(true);
          }}
          onEdit={() => {
            setIsEdit(true);
            setSelectedRow(info.row.original);
            setValue('name', info.row.original.name);
            setOpenRolePermissionModal(true);
          }}
        />
      ),
    },
  ];

  const columnsForRolePermission = useMemo(
    () => [
      {
        header: 'Module name',
        accessorKey: 'name',
        enableSorting: false,
        cell: (info: any) => {
          return <>{formatName(info.row.original?.name)}</>;
        },
      },
      {
        header: 'View',
        accessorKey: 'access.read',
        enableSorting: false,
        cell: (info: any) => (
          <CustomCheckbox
            onChange={(e) => {
              setRolePermissions((prevPermissions) => {
                const updatedPermissions = prevPermissions.map((role) =>
                  role.name === info.row.original.name
                    ? {
                        ...role,
                        access: {
                          ...role.access,
                          read: e.target.checked,
                          update: true,
                          write: true,
                          delete: true,
                        },
                      }
                    : role
                );

                // If the role isn't found, add a new entry
                if (
                  !updatedPermissions.find(
                    (role) => role.name === info.row.original.name
                  )
                ) {
                  updatedPermissions.push({
                    name: info.row.original.name,
                    access: {
                      read: e.target.checked,
                      write: true,
                      update: true,
                      delete: true,
                    },
                  });
                }

                return updatedPermissions;
              });
            }}
            checked={
              rolePermissions.find(
                (role) => role.name === info.row.original.name
              )?.access?.read || false
            }
          />
        ),
      },
      {
        header: 'Add',
        accessorKey: 'access.write',
        enableSorting: false,
        cell: (info: any) => (
          <CustomCheckbox
            onChange={(e) => {
              setRolePermissions((prevPermissions) => {
                const updatedPermissions = prevPermissions.map((role) =>
                  role.name === info.row.original.name
                    ? {
                        ...role,
                        access: { ...role.access, write: e.target.checked },
                      }
                    : role
                );

                if (
                  !updatedPermissions.find(
                    (role) => role.name === info.row.original.name
                  )
                ) {
                  updatedPermissions.push({
                    name: info.row.original.name,
                    access: {
                      read: false,
                      write: e.target.checked,
                      update: false,
                      delete: false,
                    },
                  });
                }

                return updatedPermissions;
              });
            }}
            disabled={
              !rolePermissions.find(
                (role) => role.name === info.row.original.name
              )?.access?.read || false
            }
            checked={
              !rolePermissions.find(
                (role) => role.name === info.row.original.name
              )?.access?.read
                ? false
                : rolePermissions.find(
                    (role) => role.name === info.row.original.name
                  )?.access?.write || false
            }
          />
        ),
      },
      {
        header: 'Edit',
        accessorKey: 'access.update',
        enableSorting: false,
        cell: (info: any) => (
          <CustomCheckbox
            onChange={(e) => {
              setRolePermissions((prevPermissions) => {
                const updatedPermissions = prevPermissions.map((role) =>
                  role.name === info.row.original.name
                    ? {
                        ...role,
                        access: { ...role.access, update: e.target.checked },
                      }
                    : role
                );

                if (
                  !updatedPermissions.find(
                    (role) => role.name === info.row.original.name
                  )
                ) {
                  updatedPermissions.push({
                    name: info.row.original.name,
                    access: {
                      read: false,
                      write: false,
                      update: e.target.checked,
                      delete: false,
                    },
                  });
                }

                return updatedPermissions;
              });
            }}
            disabled={
              !rolePermissions.find(
                (role) => role.name === info.row.original.name
              )?.access?.read || false
            }
            checked={
              !rolePermissions.find(
                (role) => role.name === info.row.original.name
              )?.access?.read
                ? false
                : rolePermissions.find(
                    (role) => role.name === info.row.original.name
                  )?.access?.update || false
            }
          />
        ),
      },
      {
        header: 'Delete',
        accessorKey: 'access.delete',
        enableSorting: false,
        cell: (info: any) => (
          <CustomCheckbox
            disabled={
              !rolePermissions.find(
                (role) => role.name === info.row.original.name
              )?.access?.read || false
            }
            onChange={(e) => {
              setRolePermissions((prevPermissions) => {
                const updatedPermissions = prevPermissions.map((role) =>
                  role.name === info.row.original.name
                    ? {
                        ...role,
                        access: { ...role.access, delete: e.target.checked },
                      }
                    : role
                );

                if (
                  !updatedPermissions.find(
                    (role) => role.name === info.row.original.name
                  )
                ) {
                  updatedPermissions.push({
                    name: info.row.original.name,
                    access: {
                      read: false,
                      write: false,
                      update: false,
                      delete: e.target.checked,
                    },
                  });
                }

                return updatedPermissions;
              });
            }}
            checked={
              !rolePermissions.find(
                (role) => role.name === info.row.original.name
              )?.access?.read
                ? false
                : rolePermissions.find(
                    (role) => role.name === info.row.original.name
                  )?.access?.delete || false
            }
          />
        ),
      },
    ],
    [rolePermissions, getRoleById]
  );

  const currentData = roleListData?.data?.data?.list;

  const rolePermissionSchema: yup.ObjectSchema<RolePermissionForm> = yup.object(
    {
      name: yup
        .string()
        .trim()
        .required('Role name is required')
        .min(3, 'Role name must be at least 3 characters long')
        .max(50, 'Role name must not exceed 50 characters'),
    }
  );

  const {
    handleSubmit,
    control,
    setValue,
    reset,
    formState: { errors },
  } = useForm<RolePermissionForm>({
    defaultValues: {
      name: '',
    },
    resolver: yupResolver(rolePermissionSchema),
    mode: 'onChange',
  });

  useEffect(() => {
    if (rolePermissionListIsError) {
      addToast('error', rolePermissionListError as string);
    }
    if (rolePermissionListData) {
      setRolePermissions(getRoleById?.data?.features);
    }
  }, [rolePermissionListIsError, rolePermissionListData]);

  useEffect(() => {
    if (getRoleByIdIsSuccess && getRoleById?.data?.features) {
      setRolePermissions(getRoleById.data.features);
    }
  }, [getRoleByIdIsSuccess, getRoleById]);

  useEffect(() => {
    if (roleListIsError) {
      addToast('error', roleListError as string);
    }
  }, [roleListIsError]);

  useEffect(() => {
    refetchRoleList();
  }, [sorting, pageSize, currentPage]);

  useEffect(() => {
    if (saveRolePermissionSuccess) {
      addToast('success', savedRolePermissionData?.data?.message);
      refetchRoleList();
      reset();
      setOpenRolePermissionModal(false);
    }
    if (savedRolePermissionIsError) {
      addToast('error', savedRolePermissionError as string);
    }
  }, [
    saveRolePermissionSuccess,
    savedRolePermissionIsError,
    savedRolePermissionError,
    savedRolePermissionData,
  ]);

  useEffect(() => {
    if (getRoleByIdIsError) {
      addToast('error', getRoleByIdError as string);
    }
  }, [getRoleByIdIsError, getRoleByIdError]);

  useEffect(() => {
    getRoleByIdRefetch();
    userCountRefetch();
  }, [selectedRow]);

  const onPageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const onPageSizeChange = (newSize: number) => {
    setPageSize(newSize);
    setCurrentPage(1); // Reset to first page when page size changes
  };

  const onSubmit = async (formData: RolePermissionForm): Promise<void> => {
    const formattedRolePermissions = rolePermissions.map((role) => ({
      name: role.name,
      access: {
        read: role.access?.read ?? false,
        write: role.access?.write ?? false,
        update: role.access?.update ?? false,
        delete: role.access?.delete ?? false,
      },
    }));
    if (isEdit) {
      updateRolePermission({
        name: formData.name,
        features: formattedRolePermissions,
        listId: Number(selectedRow?.id),
      });
    } else {
      savedRolePermission({ ...formData, features: formattedRolePermissions });
    }
  };

  return (
    <ContentLayout>
      <ActionBarLayout
        actionbarChildren={
          <React.Fragment>
            <div className="w-[300px]"></div>
            <div>
              <Button
                text={
                  <div className="flex gap-4 items-center justify-center text-white">
                    <AddIcon height={18} width={18} fill="#fff" /> Add new role
                  </div>
                }
                onClick={() => {
                  setRolePermissions([]);
                  setSelectedRow(null);
                  setOpenRolePermissionModal(true);
                  setRolePermissions([]);
                }}
              />
            </div>
          </React.Fragment>
        }
      >
        <DataTable
          data={currentData}
          columns={columns}
          sorting={sorting}
          setSorting={setSorting}
          totalPages={roleListData?.data?.data?.total_pages}
          currentPage={currentPage}
          onPageChange={onPageChange}
          pageSize={pageSize}
          setPageSize={setPageSize}
          pageSizeOptions={[10, 20, 30, 50]}
          onPageSizeChange={onPageSizeChange}
          isPagination={true}
          loading={roleListLoading}
        />
        <Modal
          size="dxl"
          // header="Role wise permissions"
          onClose={() => {
            setOpenRolePermissionModal(false);
            setSelectedRow(null);
            reset();
            setRolePermissions([]);
            setValue('name', '');
            setIsEdit(false);
          }}
          isOpen={openRolePermissionModal}
          footerButton
          saveLoading={savedRolePermissionisLoading}
          onCancel={() => {
            setOpenRolePermissionModal(false);
            setSelectedRow(null);
            reset();
            setRolePermissions([]);
            setValue('name', '');
            setIsEdit(false);
          }}
          onSave={handleSubmit(onSubmit)}
        >
          <form
            className="flex flex-col "
            onSubmit={handleSubmit(onSubmit)}
            autoComplete="off"
          >
            <Controller
              control={control}
              name="name"
              render={({ field }) => (
                <InputField
                  label={'Role name'}
                  placeholder={'Enter role name'}
                  disabled={false}
                  field={field}
                  type="text"
                  errorMessage={errors?.name?.message}
                />
              )}
            />

            <div className="mt-5" />
            {getRoleByIdRefetching ? (
              <Loader height={20} width={20} fill="#ffffff" />
            ) : (
              <DataTable
                data={rolePermissionListData?.data?.data}
                columns={columnsForRolePermission}
                sorting={sorting}
                setSorting={setSorting}
                totalPages={1}
                currentPage={currentPage}
                onPageChange={onPageChange}
                pageSize={pageSize}
                setPageSize={setPageSize}
                pageSizeOptions={[10, 20, 30, 50]}
                onPageSizeChange={onPageSizeChange}
                isPagination={false}
                loading={
                  rolePermissionListLoading || updateRolePermissionisLoading
                }
              />
            )}
          </form>
        </Modal>

        {/* Delete role */}
        <Modal
          isOpen={openDeleteRoleModal}
          hideCloseIcon
          onClose={() => {
            setSelectedRow(null);
            setOpenDeleteRoleModal(false);
          }}
          children={
            <div className="flex flex-col items-center gap-[36px] ">
              {!(userCountIsRefetching || userCountIsLoading) ? (
                <div className="bg-[#FFD8E0] h-14 w-14 flex justify-center items-center rounded-full">
                  <CloseIcon width={18} height={18} fill="#FF3B30" />
                </div>
              ) : (
                <Loader height={20} width={20} fill="#fff" />
              )}

              <div className="text-black font-bold text-center w-full">
                {!(userCountIsRefetching || userCountIsLoading) ? (
                  userCountData?.data?.data?.user_count > 0 ? (
                    <>
                      This role is assigned to{' '}
                      {userCountData?.data?.data?.user_count} users please
                      change their role. Then you will be able to delete role.
                    </>
                  ) : (
                    <>
                      Are you sure you want to delete this role? This action
                      cannot be undone.
                    </>
                  )
                ) : (
                  ''
                )}
              </div>
              {!(userCountIsRefetching || userCountIsLoading) && (
                <div className=" w-full flex justify-center gap-6">
                  {!(userCountData?.data?.data?.user_count > 0) && (
                    <Button
                      onClick={() => deleteRole(Number(selectedRow?.id))}
                      text="Yes"
                      variant="other"
                      loading={deleteRoleDataIsLoading}
                      className="border border-error-0
                  text-error-0 bg-transparent hover:border-error-0"
                      style={{
                        visibility:
                          userCountData?.data?.data?.user_count > 0
                            ? 'hidden'
                            : 'visible',
                      }}
                    />
                  )}
                  <Button
                    text="Cancel"
                    variant="outline"
                    disabled={deleteRoleDataIsLoading}
                    onClick={() => {
                      setSelectedRow(null);
                      setOpenDeleteRoleModal(false);
                    }}
                  />
                </div>
              )}
            </div>
          }
        />
      </ActionBarLayout>
    </ContentLayout>
  );
}
