import ContentLayout from '@Components/Layout/ContentLayout';
import { InputSelect } from '@Components/UI';

import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Area,
  AreaChart,
  ComposedChart,
  Pie,
  Pie<PERSON>hart,
  Cell,
} from 'recharts';
import CustomDatePicker from '@Components/UI/DatePicker';
import {
  useGetCategoryWiseIssues,
  useGetCategoryWiseQuestionAsked,
  useGetOrgAnalytics,
  useGetStateWiseQuestionAsked,
  useGetStateWiseUploadDocs,
  useGetUserAnalytics,
  useRectificationAnalytics,
} from '@Query/Hooks/useAnalytics';
import { useEffect, useState } from 'react';
import { DateConvertor2 } from '@Helpers/Utils';
import Skeleton from '@Components/UI/Skeleton';
import PATHS from '@Config/Path.Config';
import { useNavigate } from 'react-router';
import { useToast } from '@Components/UI/Toast/ToastProvider';

function Dashboard() {
  const navigate = useNavigate();
  const { addToast } = useToast();

  const [userAnalyticsInputs, setUserAnalyticsInputs] = useState<{
    timeRange: string;
    start_date?: string;
    end_date?: string;
  }>({ timeRange: 'week' });

  const [orgAnalyticsInputs, setOrgAnalyticsInputs] = useState<{
    timeRange: string;
    start_date?: string;
    end_date?: string;
  }>({ timeRange: 'week' });

  const [SWUDAnalyticsInputs, setSWUDAnalyticsInputs] = useState<{
    timeRange: string;
    start_date?: string;
    end_date?: string;
  }>({ timeRange: 'week' });

  const [SWQAnalyticsInputs, setSWQAnalyticsInputs] = useState<{
    timeRange: string;
    start_date?: string;
    end_date?: string;
  }>({ timeRange: 'week' });

  const [CWQAnalyticsInputs, setCWQAnalyticsInputs] = useState<{
    timeRange: string;
    start_date?: string;
    end_date?: string;
  }>({ timeRange: 'week' });

  const [CWIAnalyticsInputs, setCWIAnalyticsInputs] = useState<{
    timeRange: string;
    start_date?: string;
    end_date?: string;
  }>({ timeRange: 'week' });

  const [RFAnalyticsInputs, setRFAnalyticsInputs] = useState<{
    timeRange: string;
    start_date?: string;
    end_date?: string;
  }>({ timeRange: 'week' });

  const userAntFilterKit =
    userAnalyticsInputs.timeRange +
    (userAnalyticsInputs.start_date && userAnalyticsInputs.end_date
      ? `&start_date=${DateConvertor2(userAnalyticsInputs.start_date, 'YYYY-MM-DD')}&end_date=${DateConvertor2(userAnalyticsInputs.end_date, 'YYYY-MM-DD')}`
      : '');

  const orgAntFilterKit =
    orgAnalyticsInputs.timeRange +
    (orgAnalyticsInputs.start_date && orgAnalyticsInputs.end_date
      ? `&start_date=${DateConvertor2(orgAnalyticsInputs.start_date, 'YYYY-MM-DD')}&end_date=${DateConvertor2(orgAnalyticsInputs.end_date, 'YYYY-MM-DD')}`
      : '');

  const swudAntFilterKit =
    SWUDAnalyticsInputs.timeRange +
    (SWUDAnalyticsInputs.start_date && SWUDAnalyticsInputs.end_date
      ? `&start_date=${DateConvertor2(SWUDAnalyticsInputs.start_date, 'YYYY-MM-DD')}&end_date=${DateConvertor2(SWUDAnalyticsInputs.end_date, 'YYYY-MM-DD')}`
      : '');

  const swqAntFilterKit =
    SWQAnalyticsInputs.timeRange +
    (SWQAnalyticsInputs.start_date && SWQAnalyticsInputs.end_date
      ? `&start_date=${DateConvertor2(SWQAnalyticsInputs.start_date, 'YYYY-MM-DD')}&end_date=${DateConvertor2(SWQAnalyticsInputs.end_date, 'YYYY-MM-DD')}`
      : '');

  const cwqAntFilterKit =
    CWQAnalyticsInputs.timeRange +
    (CWQAnalyticsInputs.start_date && CWQAnalyticsInputs.end_date
      ? `&start_date=${DateConvertor2(CWQAnalyticsInputs.start_date, 'YYYY-MM-DD')}&end_date=${DateConvertor2(CWQAnalyticsInputs.end_date, 'YYYY-MM-DD')}`
      : '');

  const cwiAntFilterKit =
    CWIAnalyticsInputs.timeRange +
    (CWIAnalyticsInputs.start_date && CWIAnalyticsInputs.end_date
      ? `&start_date=${DateConvertor2(CWIAnalyticsInputs.start_date, 'YYYY-MM-DD')}&end_date=${DateConvertor2(CWIAnalyticsInputs.end_date, 'YYYY-MM-DD')}`
      : '');

  const rfAntFilterKit =
    RFAnalyticsInputs.timeRange +
    (RFAnalyticsInputs.start_date && RFAnalyticsInputs.end_date
      ? `&start_date=${DateConvertor2(RFAnalyticsInputs.start_date, 'YYYY-MM-DD')}&end_date=${DateConvertor2(RFAnalyticsInputs.end_date, 'YYYY-MM-DD')}`
      : '');

  // const {
  //   data: userAnt,
  //   error: userAntError,
  //   isError: userAntIsError,
  //   refetch: userAntRefetch,
  //   isLoading: userAntIsLoading,
  //   isRefetching: userAntIsRefetching,
  // } = useGetUserAnalytics(userAntFilterKit);

  // const {
  //   data: orgAnt,
  //   error: orgAntError,
  //   isError: orgAntIsError,
  //   isLoading: orgAntIsLoading,
  //   refetch: orgAntRefetch,
  //   isRefetching: orgAntIsRefetching,
  // } = useGetOrgAnalytics(orgAntFilterKit);

  // const {
  //   data: stateWiserUDAnt,
  //   error: stateWiserUDError,
  //   isError: stateWiserUDIsError,
  //   refetch: SWUDAntRefetch,
  //   isRefetching: SWUDAntIsRefetching,
  //   isLoading: SWUDAntIsLoading,
  // } = useGetStateWiseUploadDocs(swudAntFilterKit);

  // const {
  //   data: stateWiserQueAnt,
  //   error: stateWiserQueError,
  //   isError: stateWiserQueIsError,
  //   refetch: SWUDQueAntRefetch,
  //   isRefetching: SWUDQueAntIsRefetching,
  //   isLoading: SWUDQueAntIsLoading,
  // } = useGetStateWiseQuestionAsked(swqAntFilterKit);

  // const {
  //   data: CWQAnt,
  //   error: CWQError,
  //   isError: CWQIsError,
  //   refetch: CWQAntRefetch,
  //   isRefetching: CWQIsRefetching,
  //   isLoading: CWQIsLoading,
  // } = useGetCategoryWiseQuestionAsked(cwqAntFilterKit);

  // const {
  //   data: CWIAnt,
  //   error: CWIError,
  //   isError: CWIIsError,
  //   refetch: CWIAntRefetch,
  //   isRefetching: CWIIsRefetching,
  //   isLoading: CWIIsLoading,
  // } = useGetCategoryWiseIssues(cwiAntFilterKit);

  // const {
  //   data: RFAnt,
  //   error: RFError,
  //   isError: RFIsError,
  //   refetch: RFAntRefetch,
  //   isRefetching: RFIsRefetching,
  //   isLoading: RFIsLoading,
  // } = useRectificationAnalytics(rfAntFilterKit);

  // useEffect(() => {
  //   if (
  //     userAntIsError ||
  //     orgAntIsError ||
  //     stateWiserUDIsError ||
  //     stateWiserQueIsError ||
  //     CWQIsError ||
  //     CWIIsError ||
  //     RFIsError
  //   ) {
  //     addToast(
  //       'error',
  //       (RFError as string) ||
  //         (userAntError as string) ||
  //         (orgAntError as string) ||
  //         (stateWiserUDError as string) ||
  //         (stateWiserQueError as string) ||
  //         (CWQError as string) ||
  //         (CWIError as string)
  //     );
  //   }
  // }, [
  //   RFError,
  //   RFIsError,
  //   userAntError,
  //   userAntIsError,
  //   orgAntError,
  //   orgAntIsError,
  //   stateWiserUDError,
  //   stateWiserUDIsError,
  //   stateWiserQueError,
  //   stateWiserQueIsError,
  //   CWQError,
  //   CWQIsError,
  //   CWIError,
  //   CWIIsError,
  //   addToast,
  // ]);

  // const chartTitles = {
  //   users: 'Users Overview',
  //   organisations: 'Organisation Growth',
  //   questions: 'Statewise Questions',
  //   documents: 'Upload Overview',
  //   categoryWiseQue: 'What People Are Asking to Rex (In %)',
  //   categoryWiseIssues: 'Categorized Issues from Reports (In %)',
  //   rectificationIssues: 'Rectification Issues (In %)',
  // };

  // const timeRangeOptions = [
  //   { label: 'Year', value: 'year' },
  //   { label: 'Month', value: 'month' },
  //   { label: 'Week', value: 'week' },
  //   { label: 'Custom', value: 'custom' },
  // ];

  // useEffect(() => {
  //   if (!(userAnalyticsInputs.timeRange === 'custom')) {
  //     userAntRefetch();
  //   } else {
  //     if (userAnalyticsInputs.start_date && userAnalyticsInputs.end_date) {
  //       userAntRefetch();
  //     }
  //   }
  // }, [userAnalyticsInputs]);

  // useEffect(() => {
  //   if (!(orgAnalyticsInputs.timeRange === 'custom')) {
  //     orgAntRefetch();
  //   } else {
  //     if (orgAnalyticsInputs.start_date && orgAnalyticsInputs.end_date) {
  //       orgAntRefetch();
  //     }
  //   }
  // }, [orgAnalyticsInputs]);

  // useEffect(() => {
  //   if (!(SWUDAnalyticsInputs.timeRange === 'custom')) {
  //     SWUDAntRefetch();
  //   } else {
  //     if (SWUDAnalyticsInputs.start_date && SWUDAnalyticsInputs.end_date) {
  //       SWUDAntRefetch();
  //     }
  //   }
  // }, [SWUDAnalyticsInputs]);

  // useEffect(() => {
  //   if (!(SWQAnalyticsInputs.timeRange === 'custom')) {
  //     SWUDQueAntRefetch();
  //   } else {
  //     if (SWQAnalyticsInputs.start_date && SWQAnalyticsInputs.end_date) {
  //       SWUDQueAntRefetch();
  //     }
  //   }
  // }, [SWQAnalyticsInputs]);

  // useEffect(() => {
  //   if (!(CWQAnalyticsInputs.timeRange === 'custom')) {
  //     CWQAntRefetch();
  //   } else {
  //     if (CWQAnalyticsInputs.start_date && CWQAnalyticsInputs.end_date) {
  //       CWQAntRefetch();
  //     }
  //   }
  // }, [CWQAnalyticsInputs]);

  // useEffect(() => {
  //   if (!(CWIAnalyticsInputs.timeRange === 'custom')) {
  //     CWIAntRefetch();
  //   } else {
  //     if (CWIAnalyticsInputs.start_date && CWIAnalyticsInputs.end_date) {
  //       CWIAntRefetch();
  //     }
  //   }
  // }, [CWIAnalyticsInputs]);

  // useEffect(() => {
  //   if (!(RFAnalyticsInputs.timeRange === 'custom')) {
  //     RFAntRefetch();
  //   } else {
  //     if (RFAnalyticsInputs.start_date && RFAnalyticsInputs.end_date) {
  //       RFAntRefetch();
  //     }
  //   }
  // }, [RFAnalyticsInputs]);

  const colorPalette = [
    '#BEDDF1', // Pastel Sky
    '#DAD4B6', // Pastel Khaki
    '#E9C9AA', // Pastel Tan
    '#F1BEB5', // Pastel Flesh
    '#F8C57C', // Pastel Amber
    '#D7CAB7', // Pastel Earth
    '#A4D8D8', // Pastel Cyan
    '#D4C6AA', // Pastel Beige
    '#D3C7A2', // Pastel Sand
    '#F0EBD8', // Pastel Pearl
    '#D1FEB8', // Pastel Lime
    '#EFDFD8', // Pastel Skin
    '#F7DFC2', // Pastel Peach
    '#EBCCFF', // Pastel Mauve
    '#E7D7CA', // Pastel Nude
  ];

  return (
    <ContentLayout>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Users Chart Card */}
        {/* <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200">
          <div className="px-6 pb-3 pt-3 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-800">
              {chartTitles.users}
            </h3>
            <div className="flex items-center gap-2">
              <InputSelect
                containerClassName="pt-[0px]"
                paddingBlock="p-3"
                label=""
                value={timeRangeOptions.find(
                  (result) => result.value === userAnalyticsInputs.timeRange
                )}
                onChange={(event) =>
                  setUserAnalyticsInputs({ timeRange: event.value })
                }
                options={timeRangeOptions}
              />

              {userAnalyticsInputs.timeRange === 'custom' && (
                <CustomDatePicker
                  inputContainerClassName="py-[0.5px] w-[230px]"
                  containerClassName="pt-2"
                  value={{
                    startDate: userAnalyticsInputs.start_date as any,
                    endDate: userAnalyticsInputs.end_date as any,
                  }}
                  onChange={(range: any) => {
                    setUserAnalyticsInputs({
                      ...userAnalyticsInputs,
                      start_date: range.startDate,
                      end_date: range.endDate,
                    });
                  }}
                  isSingleMode={false}
                  placeholder="Choose a date range"
                />
              )}
            </div>
          </div>
          <div className="p-6">
            <div className="h-64">
              {userAntIsLoading || userAntIsRefetching ? (
                <Skeleton className="h-64" />
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart
                    data={userAnt?.data?.data}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <defs>
                      <linearGradient
                        id="colorActive"
                        x1="0"
                        y1="0"
                        x2="0"
                        y2="1"
                      >
                        <stop
                          offset="5%"
                          stopColor="#FF8800"
                          stopOpacity={0.8}
                        />
                        <stop
                          offset="95%"
                          stopColor="#FFA033"
                          stopOpacity={0.1}
                        />
                      </linearGradient>
                      <linearGradient id="colorNew" x1="0" y1="0" x2="0" y2="1">
                        <stop
                          offset="5%"
                          stopColor="#FF8800"
                          stopOpacity={0.8}
                        />
                        <stop
                          offset="95%"
                          stopColor="#FF8800"
                          stopOpacity={0.1}
                        />
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />

                    <Area
                      type="monotone"
                      dataKey="new_users"
                      stroke="#FF8800"
                      fillOpacity={1}
                      fill="url(#colorNew)"
                      name="New Users"
                    />
                    <Area
                      type="monotoneX"
                      dataKey="active_users_custom"
                      stroke="#FF8800"
                      fill="url(#colorNew)"
                      fillOpacity={1}
                      name={`Total Users ` + userAnt?.data?.total_active_users}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              )}
            </div>
          </div>
        </div> */}

        {/* Organisation Chart Card */}
        {/* <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200">
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-800">
              {chartTitles.organisations}
            </h3>
            <div className="flex items-center gap-2">
              <InputSelect
                containerClassName="pt-[0px]"
                paddingBlock="p-3"
                label=""
                value={timeRangeOptions.find(
                  (result) => result.value === orgAnalyticsInputs.timeRange
                )}
                onChange={(event) =>
                  setOrgAnalyticsInputs({ timeRange: event.value })
                }
                options={[
                  { label: 'Year', value: 'year' },
                  { label: 'Month', value: 'month' },
                  { label: 'Week', value: 'week' },
                  { label: 'Custom', value: 'custom' },
                ]}
              />

              {orgAnalyticsInputs.timeRange === 'custom' && (
                <CustomDatePicker
                  inputContainerClassName="py-[0.5px] w-[230px]"
                  containerClassName="pt-2"
                  value={{
                    startDate: orgAnalyticsInputs.start_date as any,
                    endDate: orgAnalyticsInputs.end_date as any,
                  }}
                  onChange={(range: any) => {
                    setOrgAnalyticsInputs({
                      ...orgAnalyticsInputs,
                      start_date: range.startDate,
                      end_date: range.endDate,
                    });
                  }}
                  isSingleMode={false}
                  placeholder="Choose a date range"
                />
              )}
            </div>
          </div>
          <div className="p-6">
            <div className="h-64">
              {orgAntIsRefetching || orgAntIsLoading ? (
                <Skeleton className="h-64" />
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <ComposedChart
                    data={orgAnt?.data?.data?.trend}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <defs>
                      <linearGradient
                        id="colorCount"
                        x1="0"
                        y1="0"
                        x2="0"
                        y2="1"
                      >
                        <stop
                          offset="5%"
                          stopColor="#FF8800"
                          stopOpacity={0.8}
                        />
                        <stop
                          offset="95%"
                          stopColor="#FFA033"
                          stopOpacity={0.1}
                        />
                      </linearGradient>
                      <linearGradient
                        id="colorGrowth"
                        x1="0"
                        y1="0"
                        x2="0"
                        y2="1"
                      >
                        <stop
                          offset="5%"
                          stopColor="#FF8800"
                          stopOpacity={0.8}
                        />
                        <stop
                          offset="95%"
                          stopColor="#FF8800"
                          stopOpacity={0.1}
                        />
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis dataKey="period" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Legend />
                    <Area
                      yAxisId="left"
                      type="monotone"
                      dataKey="new_organizations"
                      stroke="#FF8800"
                      fill="url(#colorCount)"
                      name={'New Enterprises'}
                    />
                    <Area
                      yAxisId="left"
                      stroke="#FF8800"
                      type="monotoneX"
                      dataKey="total_active_organizations_custom"
                      fill="url(#colorCount)"
                      name={
                        'Total Enterprises ' +
                        orgAnt?.data?.data?.summary?.total_active_organizations
                      }
                    />
                  </ComposedChart>
                </ResponsiveContainer>
              )}
            </div>
          </div>
        </div> */}

        {/* Statewise Uploaded Documents Bar Chart Card */}
        {/* <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200">
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-800">
              {chartTitles.documents}
            </h3>
            <div className="flex items-center gap-2">
              <InputSelect
                containerClassName="pt-[0px]"
                paddingBlock="p-3"
                label=""
                value={timeRangeOptions.find(
                  (result) => result.value === SWUDAnalyticsInputs.timeRange
                )}
                onChange={(event) =>
                  setSWUDAnalyticsInputs({ timeRange: event.value })
                }
                options={[
                  { label: 'Year', value: 'year' },
                  { label: 'Month', value: 'month' },
                  { label: 'Week', value: 'week' },
                  { label: 'Custom', value: 'custom' },
                ]}
              />

              {SWUDAnalyticsInputs.timeRange === 'custom' && (
                <CustomDatePicker
                  inputContainerClassName="py-[0.5px] w-[230px]"
                  containerClassName="pt-2"
                  value={{
                    startDate: SWUDAnalyticsInputs.start_date as any,
                    endDate: SWUDAnalyticsInputs.end_date as any,
                  }}
                  onChange={(range: any) => {
                    setSWUDAnalyticsInputs({
                      ...SWUDAnalyticsInputs,
                      start_date: range.startDate,
                      end_date: range.endDate,
                    });
                  }}
                  isSingleMode={false}
                  placeholder="Choose a date range"
                />
              )}
            </div>
          </div>
          <div className="p-6">
            <div className="h-64">
              {SWUDAntIsRefetching || SWUDAntIsLoading ? (
                <Skeleton className="h-64" />
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Tooltip />
                    <Legend
                      layout="vertical"
                      align="right"
                      verticalAlign="middle"
                      wrapperStyle={{
                        paddingLeft: '10px',
                        fontSize: '12px',
                        fontWeight: '500',
                      }}
                      payload={stateWiserUDAnt?.data?.data?.states
                        .filter((item: any) => item.document_count > 0)
                        .map((item: any, index: number) => {
                          const total =
                            stateWiserUDAnt?.data?.data?.states.reduce(
                              (acc: number, domain: any) =>
                                acc + domain.document_count,
                              0
                            );
                          return {
                            id: item.state_id,
                            type: 'square',
                            value: `${item.state_name}: ${item.percentage}%`,
                            color: colorPalette[index % colorPalette.length],
                          };
                        })}
                    />
                    <Pie
                      data={stateWiserUDAnt?.data?.data?.states.filter(
                        (item: any) => item.document_count > 0
                      )}
                      dataKey="document_count"
                      nameKey="state_name"
                      cx="40%"
                      cy="50%"
                      outerRadius={100}
                      label={(entry) => `${entry.percentage}%`}
                    >
                      {stateWiserUDAnt?.data?.data?.states.map(
                        (_: any, index: number) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={colorPalette[index % colorPalette.length]}
                          />
                        )
                      )}
                    </Pie>
                  </PieChart>
                </ResponsiveContainer>
              )}
            </div>
          </div>
        </div> */}

        {/* Statewise Questions Bar Chart Card */}
        {/* <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200">
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-800">
              {chartTitles.questions}
            </h3>
            <div className="flex items-center gap-2">
              <InputSelect
                containerClassName="pt-[0px]"
                paddingBlock="p-3"
                label=""
                value={timeRangeOptions.find(
                  (result) => result.value === SWQAnalyticsInputs.timeRange
                )}
                onChange={(event) =>
                  setSWQAnalyticsInputs({ timeRange: event.value })
                }
                options={[
                  { label: 'Year', value: 'year' },
                  { label: 'Month', value: 'month' },
                  { label: 'Week', value: 'week' },
                  { label: 'Custom', value: 'custom' },
                ]}
              />

              {SWQAnalyticsInputs.timeRange === 'custom' && (
                <CustomDatePicker
                  inputContainerClassName="py-[0.5px] w-[230px]"
                  containerClassName="pt-2"
                  value={{
                    startDate: SWQAnalyticsInputs.start_date as any,
                    endDate: SWQAnalyticsInputs.end_date as any,
                  }}
                  onChange={(range: any) => {
                    setSWQAnalyticsInputs({
                      ...SWQAnalyticsInputs,
                      start_date: range.startDate,
                      end_date: range.endDate,
                    });
                  }}
                  isSingleMode={false}
                  placeholder="Choose a date range"
                />
              )}
            </div>
          </div>
          <div className="p-6">
            <div className="h-72">
              {SWUDQueAntIsLoading || SWUDQueAntIsRefetching ? (
                <Skeleton className="h-72" />
              ) : (
                <>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Tooltip />
                      <Legend
                        layout="vertical"
                        align="right"
                        verticalAlign="middle"
                        wrapperStyle={{
                          paddingLeft: '10px',
                          fontSize: '12px',
                          fontWeight: '500',
                        }}
                        payload={stateWiserQueAnt?.data?.data?.states
                          .filter((item: any) => item.questions_asked > 0)
                          .map((item: any, index: number) => {
                            const total =
                              stateWiserQueAnt?.data?.data?.states.reduce(
                                (acc: number, domain: any) =>
                                  acc + domain.questions_asked,
                                0
                              );
                            return {
                              id: item.state_id,
                              type: 'square',
                              value: `${item.state_name}: ${item.percentage}%`,
                              color: colorPalette[index % colorPalette.length],
                            };
                          })}
                      />
                      <Pie
                        data={stateWiserQueAnt?.data?.data?.states.filter(
                          (item: any) => item.questions_asked > 0
                        )}
                        dataKey="questions_asked"
                        nameKey="state_name"
                        cx="40%"
                        cy="50%"
                        outerRadius={100}
                        label={(entry) => `${entry.percentage}%`}
                      >
                        {stateWiserQueAnt?.data?.data?.states.map(
                          (_: any, index: number) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={colorPalette[index % colorPalette.length]}
                            />
                          )
                        )}
                      </Pie>
                    </PieChart>
                  </ResponsiveContainer>
                </>
              )}
            </div>
          </div>
        </div> */}

        {/* Categorywise Questions Asked Bar Chart Card */}
        {/* <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 lg:col-span-2">
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-800">
              {chartTitles.categoryWiseQue}
            </h3>
            <div className="flex items-center gap-2">
              <InputSelect
                containerClassName="pt-[0px]"
                paddingBlock="p-3"
                label=""
                value={timeRangeOptions.find(
                  (result) => result.value === CWQAnalyticsInputs.timeRange
                )}
                onChange={(event) =>
                  setCWQAnalyticsInputs({ timeRange: event.value })
                }
                options={[
                  { label: 'Year', value: 'year' },
                  { label: 'Month', value: 'month' },
                  { label: 'Week', value: 'week' },
                  { label: 'Custom', value: 'custom' },
                ]}
              />

              {CWQAnalyticsInputs.timeRange === 'custom' && (
                <CustomDatePicker
                  inputContainerClassName="py-[0.5px] w-[230px]"
                  containerClassName="pt-2"
                  value={{
                    startDate: CWQAnalyticsInputs.start_date as any,
                    endDate: CWQAnalyticsInputs.end_date as any,
                  }}
                  onChange={(range: any) => {
                    setCWQAnalyticsInputs({
                      ...CWQAnalyticsInputs,
                      start_date: range.startDate,
                      end_date: range.endDate,
                    });
                  }}
                  isSingleMode={false}
                  placeholder="Choose a date range"
                />
              )}
            </div>
          </div>
          <div className="p-6">
            <div className="h-64">
              {CWQIsLoading || CWQIsRefetching ? (
                <Skeleton className="h-64" />
              ) : (
                <>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                      }}
                    >
                      <Tooltip />


                      <Pie
                        data={CWQAnt?.data?.data?.domains.filter(
                          (item: any) => item.questions_asked > 0
                        )}
                        dataKey="questions_asked"
                        nameKey="domain_name"
                        cx="40%"
                        cy="50%"
                        outerRadius={100}
                        label={({ percentage }) => `${percentage}%`} 
                        onClick={(data, index) => {
                          navigate(PATHS.CHAT_LIST, {
                            state: {
                              id: data.domain_id,
                            },
                          });
                        }}
                      >
                        {CWQAnt?.data?.data?.domains.map(
                          (_: any, index: number) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={colorPalette[index % colorPalette.length]}
                            />
                          )
                        )}
                      </Pie>

                      <Legend
                        layout="vertical"
                        align="right"
                        verticalAlign="middle"
                        wrapperStyle={{
                          paddingLeft: '10px',
                          fontSize: '12px',
                          fontWeight: '500',
                        }}
                        payload={CWQAnt?.data?.data?.domains
                          .filter((item: any) => item.questions_asked > 0)
                          .map((item: any, index: number) => ({
                            id: item.domain_id,
                            type: 'square',
                            value: `${item.domain_name}`,
                            color: colorPalette[index % colorPalette.length],
                          }))}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                </>
              )}
            </div>
          </div>
        </div> */}

        {/* Categorywise Issues Pie Chart Card */}
        {/* <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 lg:col-span-2">
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-800">
              {chartTitles.categoryWiseIssues}
            </h3>
            <div className="flex items-center gap-2">
              <InputSelect
                containerClassName="pt-[0px]"
                paddingBlock="p-3"
                label=""
                value={timeRangeOptions.find(
                  (result) => result.value === CWIAnalyticsInputs.timeRange
                )}
                onChange={(event) =>
                  setCWIAnalyticsInputs({ timeRange: event.value })
                }
                options={[
                  { label: 'Year', value: 'year' },
                  { label: 'Month', value: 'month' },
                  { label: 'Week', value: 'week' },
                  { label: 'Custom', value: 'custom' },
                ]}
              />

              {CWIAnalyticsInputs.timeRange === 'custom' && (
                <CustomDatePicker
                  inputContainerClassName="py-[0.5px] w-[230px]"
                  containerClassName="pt-2"
                  value={{
                    startDate: CWIAnalyticsInputs.start_date as any,
                    endDate: CWIAnalyticsInputs.end_date as any,
                  }}
                  onChange={(range: any) => {
                    setCWIAnalyticsInputs({
                      ...CWIAnalyticsInputs,
                      start_date: range.startDate,
                      end_date: range.endDate,
                    });
                  }}
                  isSingleMode={false}
                  placeholder="Choose a date range"
                />
              )}
            </div>
          </div>

          <div className="p-6">
            <div className="h-72">
              {CWIIsLoading || CWIIsRefetching ? (
                <Skeleton className="h-72" />
              ) : (
                <>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Tooltip />
                      <Legend
                        layout="vertical"
                        align="right"
                        verticalAlign="middle"
                        wrapperStyle={{
                          paddingLeft: '10px',
                          fontSize: '12px',
                          fontWeight: '500',
                        }}
                        payload={CWIAnt?.data?.data?.domains
                          .filter((item: any) => item.questions_asked > 0)
                          .map((item: any, index: number) => {
                            const total = CWIAnt?.data?.data?.domains.reduce(
                              (acc: number, domain: any) =>
                                acc + domain.questions_asked,
                              0
                            );
                            return {
                              id: item.domain_id,
                              type: 'square',
                              value: `${item.domain_name}: ${item.percentage}%`,
                              color: colorPalette[index % colorPalette.length],
                            };
                          })}
                      />
                      <Pie
                        data={CWIAnt?.data?.data?.domains.filter(
                          (item: any) => item.questions_asked > 0
                        )}
                        dataKey="questions_asked"
                        nameKey="domain_name"
                        cx="40%"
                        cy="50%"
                        outerRadius={100}
                        label={(entry) => `${entry.percentage}%`}
                        onClick={(data, index) => {
                          navigate(PATHS.ISSUE_LIST, {
                            state: {
                              id: data.domain_id,
                            },
                          });
                        }}
                      >
                        {CWIAnt?.data?.data?.domains.map(
                          (_: any, index: number) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={colorPalette[index % colorPalette.length]}
                            />
                          )
                        )}
                      </Pie>
                    </PieChart>
                  </ResponsiveContainer>
                </>
              )}
            </div>
          </div>
        </div> */}

        {/* Rectification Issues Pie Chart Card */}
        {/* <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 ">
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-800">
              {chartTitles.rectificationIssues}
            </h3>
            <div className="flex items-center gap-2">
              <InputSelect
                containerClassName="pt-[0px]"
                paddingBlock="p-3"
                label=""
                value={timeRangeOptions.find(
                  (result) => result.value === RFAnalyticsInputs.timeRange
                )}
                onChange={(event) =>
                  setRFAnalyticsInputs({ timeRange: event.value })
                }
                options={[
                  { label: 'Year', value: 'year' },
                  { label: 'Month', value: 'month' },
                  { label: 'Week', value: 'week' },
                  { label: 'Custom', value: 'custom' },
                ]}
              />

              {RFAnalyticsInputs.timeRange === 'custom' && (
                <CustomDatePicker
                  inputContainerClassName="py-[0.5px] w-[230px]"
                  containerClassName="pt-2"
                  value={{
                    startDate: RFAnalyticsInputs.start_date as any,
                    endDate: RFAnalyticsInputs.end_date as any,
                  }}
                  onChange={(range: any) => {
                    setRFAnalyticsInputs({
                      ...RFAnalyticsInputs,
                      start_date: range.startDate,
                      end_date: range.endDate,
                    });
                  }}
                  isSingleMode={false}
                  placeholder="Choose a date range"
                />
              )}
            </div>
          </div>

          <div className="p-6">
            <div className="h-72">
              {RFIsLoading || RFIsRefetching ? (
                <Skeleton className="h-72" />
              ) : (
                <>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Tooltip />
                      <Legend
                        layout="vertical"
                        align="right"
                        verticalAlign="middle"
                        wrapperStyle={{
                          paddingLeft: '10px',
                          fontSize: '12px',
                          fontWeight: '500',
                        }}
                        payload={RFAnt?.data?.data?.rectification_types
                          .filter((item: any) => item.issues_count > 0)
                          .map((item: any, index: number) => {
                            const total =
                              RFAnt?.data?.data?.rectification_types.reduce(
                                (acc: number, domain: any) =>
                                  acc + domain.issues_count,
                                0
                              );
                            return {
                              id: item.domain_id,
                              type: 'square',
                              value: `${item.rectification_type_name}: ${item.percentage}%`,
                              color: colorPalette[index % colorPalette.length],
                            };
                          })}
                      />
                      <Pie
                        data={RFAnt?.data?.data?.rectification_types.filter(
                          (item: any) => item.issues_count > 0
                        )}
                        dataKey="issues_count"
                        nameKey="rectification_type_name"
                        cx="40%"
                        cy="50%"
                        outerRadius={100}
                        label={(entry) => `${entry.percentage}%`}
                        onClick={(data) => {
                          navigate(PATHS.REC_ISSUES, {
                            state: {
                              id: data.rectification_type_code,
                            },
                          });
                        }}
                      >
                        {RFAnt?.data?.data?.rectification_types.map(
                          (_: any, index: number) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={colorPalette[index % colorPalette.length]}
                            />
                          )
                        )}
                      </Pie>
                    </PieChart>
                  </ResponsiveContainer>
                </>
              )}
            </div>
          </div>
        </div> */}
      </div>
    </ContentLayout>
  );
}

export default Dashboard;
