/* eslint-disable @typescript-eslint/no-unused-vars */
import ContentLayout from '@Components/Layout/ContentLayout';
import { ViewMore } from '@Components/UI';
import { ArrowLeftIcon, EyeIcon, Loader } from '@Icons';
import {
  useGetChatList,
  useGetChatView,
  useGetIssueList,
  useGetViewIssue,
} from '@Query/Hooks/useAnalytics';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useLocation } from 'react-router';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import rehypeKatex from 'rehype-katex';
import remarkMath from 'remark-math';
import { motion } from 'framer-motion';

const ChatList = () => {
  const location = useLocation();
  const [pageNum, setPageNum] = useState(1);
  const [dataList, setDataList] = useState<any>([]);
  const observer = useRef<any>(null);

  const [openChat, setOpenChat] = useState<{
    open: boolean;
    chatId: number | null;
  }>({ open: false, chatId: null });

  const { data, isLoading, isError, error, refetch, isRefetching, isSuccess } =
    useGetChatList(
      location?.state?.id + `&ordering=-created_at&page=${pageNum}&page_size=20`
    );

  const lastElementRef = useCallback(
    (node: any) => {
      if (isLoading) return;
      if (observer.current) observer.current.disconnect();
      observer.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && data?.data?.data?.next > 0) {
          setPageNum((prev) => prev + 1);
        }
      });
      if (node) observer.current.observe(node);
    },
    [isLoading]
  );

  useEffect(() => {
    if (isSuccess && data?.data?.data?.list) {
      setDataList((prev: any) => [...prev, ...(data?.data?.data?.list as any)]);
    }
  }, [isSuccess, data]);

  useEffect(() => {
    if (pageNum > 1 && data?.data?.data?.next > 0) {
      refetch();
    } else {
      observer.current?.disconnect();
      observer.current = null;
    }
  }, [pageNum]);

  const {
    data: chatData,
    isLoading: chatDataIsLoading,
    isError: chatDataIsError,
    error: chatDataError,
    refetch: chatDataRefetch,
  } = useGetChatView(
    openChat.chatId
      ? 'question_id=' + openChat.chatId.toString() + '&is_chat=true'
      : '0',
    openChat.open
  );

  useEffect(() => {
    if (openChat.open && openChat.chatId) {
      chatDataRefetch();
    }
  }, [openChat, chatDataRefetch]);

  useEffect(() => {
    if (location?.state?.id) {
      refetch();
    }
  }, [location?.state?.id]);

  const cleanMath = (text: string | undefined) =>
    text ? text.replace(/\\\[/g, '$$').replace(/\\\]/g, '$$') : '';

  // Animation variants for list items and chat messages
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.4 },
    },
  };

  // Handle back button press
  const handleBackPress = () => {
    setOpenChat({
      open: false,
      chatId: null,
    });
  };

  // Render loading state
  const renderLoading = () => (
    <div className="flex justify-center items-center h-64">
      <div className="animate-pulse flex space-x-4">
        <div className="rounded-full bg-gray-200 h-10 w-10"></div>
        <div className="flex-1 space-y-4 py-1">
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    </div>
  );

  // Render error state
  const renderError = (errorMessage: any) => (
    <div className="p-4 bg-red-50 border border-red-200 rounded-lg text-red-600">
      <p className="font-medium">Something went wrong</p>
      <p className="text-sm">
        {errorMessage?.message || 'Failed to load data'}
      </p>
    </div>
  );

  // Render issue list
  const renderIssueList = () => {
    if (isLoading) return renderLoading();
    if (isError) return renderError(error);

    if (!data?.data?.data?.list?.length) {
      return (
        <div className="text-center p-8 text-gray-500">No issues found</div>
      );
    }

    return (
      <motion.div
        className="space-y-4"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {dataList?.map((item: any) => (
          <motion.div
            key={item.id}
            ref={lastElementRef}
            variants={itemVariants}
            className="p-6 border border-[#F1F1F1] rounded-lg shadow-sm hover:shadow-md transition-shadow bg-white flex justify-between gap-4"
          >
            <div className="flex-grow">
              <ViewMore text={item.question} maxLength={140} />
            </div>
            <button
              onClick={() => {
                setOpenChat({
                  open: true,
                  chatId: item.id,
                });
              }}
              className="flex items-center  transition-colors"
              aria-label="View issue details"
            >
              <EyeIcon height={20} width={20} className="mr-1 cursor-pointer" />
            </button>
          </motion.div>
        ))}
        {(isLoading || isRefetching) && (
          <Loader height={20} width={20} fill="#f0f0f0" />
        )}
      </motion.div>
    );
  };

  // Render chat view
  const renderChatView = () => {
    if (chatDataIsLoading) return renderLoading();
    if (chatDataIsError) return renderError(chatDataError);

    const question = chatData?.data?.data?.question;
    const answer = chatData?.data?.data?.answer;
    const category = chatData?.data?.data?.category;

    return (
      <div className="space-y-6">
        <div className="flex items-center mb-6 sticky top-0 bg-white py-5">
          <button
            onClick={handleBackPress}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
            aria-label="Go back to issue list"
          >
            <ArrowLeftIcon height={24} width={24} />
          </button>
          <h2 className="ml-2 text-xl font-semibold">Issue Details</h2>
        </div>

        {/* Question */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="p-5 bg-gray-50 rounded-lg"
        >
          <h3 className="text-sm text-gray-500 uppercase mb-2">Question</h3>
          <div className="text-gray-800">{question}</div>
          {category && (
            <div className="bg-primary-0 text-xs font-bold px-4 py-1 w-fit rounded-full mt-3">
              {category}
            </div>
          )}
        </motion.div>

        {/* Answer */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="p-5 bg-blue-50 rounded-lg"
        >
          <h3 className="text-sm text-primary-100 uppercase mb-2">Answer</h3>
          <div className="prose max-w-full">
            <ReactMarkdown
              remarkPlugins={[remarkGfm, remarkMath]}
              rehypePlugins={[rehypeRaw, rehypeKatex]}
              components={{
                strong: ({ node, ...props }) => (
                  <strong className="font-bold" {...props} />
                ),
                em: ({ node, ...props }) => (
                  <em className="italic" {...props} />
                ),
                table: ({ children }) => (
                  <div className="overflow-x-auto my-4">
                    <table className="table-auto border-collapse border border-gray-300 w-full text-left">
                      {children}
                    </table>
                  </div>
                ),
                th: ({ children }) => (
                  <th className="border border-gray-300 bg-gray-100 px-4 py-2">
                    {children}
                  </th>
                ),
                td: ({ children }) => (
                  <td className="border border-gray-300 px-4 py-2">
                    {children}
                  </td>
                ),
                h3: ({ node, ...props }) => (
                  <h3 {...props} className="text-xl font-bold py-3" />
                ),
                h2: ({ node, ...props }) => (
                  <h2 {...props} className="text-2xl font-bold py-3" />
                ),
                h1: ({ node, ...props }) => (
                  <h1 {...props} className="text-3xl font-bold py-3" />
                ),
                h4: ({ node, ...props }) => (
                  <h4 {...props} className="text-lg font-bold py-3" />
                ),
                p: ({ children }) => <p className="mb-4">{children}</p>,
                ul: ({ children }) => (
                  <ul className="list-disc pl-5 mb-4">{children}</ul>
                ),
                ol: ({ children }) => (
                  <ol className="list-decimal pl-5 mb-4">{children}</ol>
                ),
                li: ({ children }) => <li className="mb-1">{children}</li>,
                code: ({ node, className, children, ...props }) => {
                  return (
                    <code
                      className="block bg-gray-100 p-4 rounded-md overflow-x-auto text-sm"
                      {...props}
                    >
                      {children}
                    </code>
                  );
                },
                blockquote: ({ children }) => (
                  <blockquote className="border-l-4 border-gray-300 pl-4 italic my-4">
                    {children}
                  </blockquote>
                ),
                br: () => <br />,
                hr: () => <hr className="my-6 border-t border-gray-300" />,
              }}
            >
              {cleanMath(answer)}
            </ReactMarkdown>
          </div>
        </motion.div>
      </div>
    );
  };

  return (
    <ContentLayout removeHeader>
      <div className="bg-white rounded-xl">
        {openChat.open ? renderChatView() : renderIssueList()}
      </div>
    </ContentLayout>
  );
};

export default ChatList;
