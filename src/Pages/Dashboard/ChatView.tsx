// import React from 'react';

// export const ChatView = () => {
//   return (
//     <div className="flex overflow-hidden h-screen max-h-screen flex-col m-6">
//       <div className="flex flex-1 flex-col">
//         <div className="flex pb-4 w-full justify-between items-center">
//           <div className="font-semibold text-2xl">Search Result</div>
//           <div className="flex items-center gap-x-4">
//             {state?.isDownloadAvailable && state?.id && (
//               <div
//                 className={
//                   isDonloadLoading ? 'cursor-not-allowed' : 'cursor-pointer'
//                 }
//               >
//                 {isDonloadLoading ? (
//                   <Loader height={20} width={20} />
//                 ) : (
//                   <DownloadIcon
//                     height={22}
//                     width={22}
//                     onClick={() => downloadFile(state?.id)}
//                     className="mr-2"
//                   />
//                 )}
//               </div>
//             )}
//             <div
//               className="cursor-pointer"
//               onClick={() => {
//                 stopFetchingRef.current = true;
//                 setIsFetchingStopped(true);
//                 setSessionId(null);
//                 setChats([]);
//                 setIsNewChat(true);
//                 setLink('');
//                 setSelectedFile(null);
//                 if (state?.from === 'HISTORY')
//                   navigate('.', { replace: true, state: null }); // Clears the state
//               }}
//             >
//               <NewChatIcon height={24} width={24} />
//             </div>
//           </div>
//         </div>

//         <div
//           ref={chatContainerRef}
//           className={clsx(
//             'flex flex-col h-full max-h-[76vh] px-4 overflow-auto custom-scrollbar w-full flex-1',
//             chats?.length && chats[chats?.length - 1]?.isNew && 'scroll-smooth'
//           )}
//         >
//           {chats.map((el, index) => (
//             <div
//               className={clsx(
//                 'flex w-full py-4 items-start',
//                 el?.type === 'user' ? 'justify-end' : 'justify-start',
//                 index === chats?.length - 1 &&
//                   chats?.length > 2 &&
//                   el?.isNew &&
//                   `min-h-[69vh]`
//               )}
//               style={
//                 index === chats?.length - 1 &&
//                 chats?.length > 2 &&
//                 el?.isNew &&
//                 lastMessageMinHeight
//                   ? { minHeight: lastMessageMinHeight ?? 'auto' }
//                   : {}
//               }
//               id={el?.id}
//               key={`${el?.id}_${el?.type}`}
//               ref={index === 0 ? firstMessageRef : null}
//             >
//               {el?.type === 'bot' && (
//                 <div className="flex h-8 w-8">
//                   <RoboIcon />
//                 </div>
//               )}
//               <div
//                 className={clsx(
//                   'flex flex-wrap bg-primary-light px-4  rounded-[10px] max-w-full',
//                   el?.type === 'user'
//                     ? 'bg-primary-light border border-primary-100 py-2.5'
//                     : 'bg-white ml-2'
//                 )}
//               >
//                 {el?.type === 'user' ? (
//                   el?.text &&
//                   el?.text.split('\n').map((line, index) => (
//                     <React.Fragment key={index}>
//                       {el?.text?.split('.').pop() === 'pdf' && (
//                         <PdfIcon height={20} width={20} className="mr-2" />
//                       )}
//                       {line}
//                       <br />
//                     </React.Fragment>
//                   ))
//                 ) : (
//                   <motion.div
//                     initial={{ opacity: 0 }}
//                     animate={{ opacity: 1 }}
//                     transition={{ duration: 0.5 }}
//                     className="prose max-w-none leading-10 max-w-full flex flex-1 flex-col"
//                   >
//                     <ReactMarkdown
//                       remarkPlugins={[remarkGfm, remarkMath]}
//                       rehypePlugins={[rehypeRaw, rehypeKatex]}
//                       components={{
//                         strong: ({ node, ...props }) => <strong {...props} />,
//                         em: ({ node, ...props }) => <em {...props} />,
//                         table: ({ children }) => (
//                           <table className="table-auto border-collapse border border-gray-300 w-full text-left">
//                             {children}
//                           </table>
//                         ),
//                         th: ({ children }) => (
//                           <th className="border border-gray-300 bg-gray-100 px-4 py-2">
//                             {children}
//                           </th>
//                         ),
//                         td: ({ children }) => (
//                           <td className="border border-gray-300 px-4 py-2">
//                             {children}
//                           </td>
//                         ),
//                         h3: ({ node, ...props }) => (
//                           <h3 {...props} className="text-xl font-bold py-3" />
//                         ),
//                         h2: ({ node, ...props }) => (
//                           <h2 {...props} className="text-2xl font-bold py-3" />
//                         ),
//                         h1: ({ node, ...props }) => (
//                           <h1 {...props} className="text-3xl font-bold py-3" />
//                         ),
//                         h4: ({ node, ...props }) => (
//                           <h4 {...props} className="text-lg font-bold py-3" />
//                         ),
//                         p: ({ children }) => <p className="mb-2">{children}</p>,
//                         br: () => <div className="my-2 hidden" />,
//                         hr: () => <div className="py-2 hidden" />,
//                       }}
//                     >
//                       {cleanMath(el?.text)}
//                     </ReactMarkdown>
//                     {status && index === chats?.length - 1 && (
//                       <div className="mt-2 text-md font-medium text-gray-900">
//                         <span className="animate-left-to-right-pulse">
//                           {status}
//                         </span>
//                       </div>
//                     )}
//                   </motion.div>
//                 )}
//               </div>

//               {el?.type === 'user' && (
//                 <div className="pl-2 flex">
//                   <UserCircleIcon height={32} width={32} />
//                 </div>
//               )}
//             </div>
//           ))}
//           {showScrollButton &&
//             chats?.length &&
//             chats[chats.length - 1].type !== 'user' && (
//               <button
//                 onClick={scrollToBottom}
//                 className="absolute shadow-primary  m-auto h-10 w-10 bottom-21 right-0 left-0 bg-primary-100 hover:cursor-pointer text-white rounded-full p-3 flex items-center justify-center transition-opacity"
//                 aria-label="Scroll to bottom"
//               >
//                 <ArrowDown size={20} />
//               </button>
//             )}
//         </div>
//       </div>
//     </div>
//   );
// };
