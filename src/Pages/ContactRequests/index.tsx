import ActionButtons from '@Components/Common/ActionButtons';
import ActionBarLayout from '@Components/Layout/ActionBarLayout';
import ContentLayout from '@Components/Layout/ContentLayout';
import { Button, DataTable, Modal } from '@Components/UI';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { MODULE_KEY } from '@Helpers/Constants';
import useHasAccess from '@Hooks/useHasAccess';
import { CloseIcon } from '@Icons';
import {
  useDeleteContactUsListItem,
  useGetContactUs,
} from '@Query/Hooks/useContactUs';
import { ColumnDef, SortingState } from '@tanstack/react-table';
import { useEffect, useState } from 'react';

type SearchableColumnDef<T> = ColumnDef<T> & {
  search?: boolean;
  vKey?: string;
};

interface ContactRequests {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  mobile_no: string;
  country_code: string;
  subject: string;
  comment: string;
}

function ContactRequests() {
  const { addToast } = useToast();

  const module_access = useHasAccess(MODULE_KEY.CONTACT_REQUEST, [
    'read',
    'write',
    'update',
    'delete',
  ]);

  const [openDeleteListItemModal, setOpenDeleteListItemModal] =
    useState<boolean>(false);
  const [openViewModal, setOpenViewModal] = useState<boolean>(false);
  const [selectedRow, setSelectedRow] = useState<ContactRequests | null>();

  const [sorting, setSorting] = useState<SortingState>([
    { id: 'id', desc: true },
  ]);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  const columns: SearchableColumnDef<ContactRequests>[] = [
    // {
    //   header: 'ID',
    //   accessorKey: 'id',
    //   cell: (info) => info.getValue(), // Custom cell renderer
    // },
    {
      header: 'First name',
      accessorKey: 'first_name',
      cell: (info) => info.getValue(),
    },
    {
      header: 'Last name',
      accessorKey: 'last_name',
      cell: (info) => info.getValue(),
    },
    {
      header: 'Email',
      accessorKey: 'email',
      cell: (info) => info.getValue(),
    },
    {
      header: 'Mobile no.',
      accessorKey: 'mobile_no',
      enableSorting: false,
      cell: (info) => {
        const row = info.row.original;
        return `${row.country_code} ${info.getValue()}`;
      },
    },
    {
      header: 'Action',
      accessorKey: 'action',
      enableSorting: false,
      cell: (info) => (
        <ActionButtons
          deleteIcon
          viewIcon
          deleteIconDisabled={!module_access.delete}
          onView={() => {
            setSelectedRow(info.row.original);
            setOpenViewModal(true);
          }}
          onDelete={() => {
            setSelectedRow(info.row.original);
            setOpenDeleteListItemModal(true);
          }}
        />
      ),
    },
  ];

  const {
    data: contactDataList,
    isError: contactDataListError,
    isLoading: contactDataListIsLoading,
    refetch: refetchcontactDataList,
    isFetching: contactDataListIsFetching,
    error: contactDataListErrorMessage,
  } = useGetContactUs({
    ordering:
      sorting.length > 0
        ? `${sorting[0]?.desc === true ? '-' + sorting[0]?.id : sorting[0]?.id}`
        : 'id',
    page: currentPage,
    limit: pageSize,
  });

  const {
    mutate: deleteListItem,
    error: deleteListItemError,
    isError: isErrorDeleteListItem,
    isSuccess: isSuccessDeleteListItem,
    data: deleteListItemData,
    isLoading: isLoadingDeleteListItem,
  } = useDeleteContactUsListItem({
    listItemId: selectedRow ? selectedRow?.id : 0,
  });

  const currentData = contactDataList?.data?.data?.list;

  const onPageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const onPageSizeChange = (newSize: number) => {
    setPageSize(newSize);
    setCurrentPage(1); // Reset to first page when page size changes
  };

  useEffect(() => {
    if (contactDataListError) {
      addToast('error', contactDataListErrorMessage as string);
    }

    if (isErrorDeleteListItem) {
      addToast('error', deleteListItemError as string);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    contactDataListError,
    contactDataListErrorMessage,
    deleteListItemError,
    isErrorDeleteListItem,
  ]);

  useEffect(() => {
    refetchcontactDataList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sorting, pageSize, currentPage]);

  useEffect(() => {
    if (isSuccessDeleteListItem) {
      setOpenDeleteListItemModal(false);
      setSelectedRow(null);
      addToast('success', deleteListItemData?.data?.message);
      refetchcontactDataList();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSuccessDeleteListItem]);

  return (
    <ContentLayout>
      <ActionBarLayout>
        <DataTable
          data={currentData}
          columns={columns}
          sorting={sorting}
          setSorting={setSorting}
          totalPages={contactDataList?.data?.data?.total_pages}
          currentPage={currentPage}
          onPageChange={onPageChange}
          pageSize={pageSize}
          setPageSize={setPageSize}
          pageSizeOptions={[10, 20, 30, 50]}
          onPageSizeChange={onPageSizeChange}
          isPagination={true}
          loading={contactDataListIsLoading || contactDataListIsFetching} // set to true if loading data from an API
        />
        <Modal
          isOpen={openDeleteListItemModal}
          hideCloseIcon
          onClose={() => {
            setOpenDeleteListItemModal(false);
            setSelectedRow(null);
          }}
          children={
            <div className="flex flex-col items-center gap-[36px] ">
              <div className="bg-[#FFD8E0] h-14 w-14 flex justify-center items-center rounded-full">
                <CloseIcon width={18} height={18} fill="#FF3B30" />
              </div>
              <div className="text-black font-bold text-center w-full">
                Are you sure want to delete contact request?
              </div>
              <div className=" w-full flex justify-center gap-6">
                <Button
                  onClick={deleteListItem}
                  text="Yes"
                  variant="other"
                  className=" border border-error-0
              text-error-0 bg-transparent hover:border-error-0"
                  loading={isLoadingDeleteListItem}
                />
                <Button
                  text="No"
                  variant="outline"
                  disabled={isLoadingDeleteListItem}
                  onClick={() => {
                    setOpenDeleteListItemModal(false);
                    setSelectedRow(null);
                  }}
                />
              </div>
            </div>
          }
        />

        <Modal
          size="xl"
          isOpen={openViewModal}
          onClose={() => {
            setOpenViewModal(false);
            setSelectedRow(null);
          }}
        >
          <div className="pt-0">
            <p className="text-[#333333] font-medium text-md text-justify pb-2">
              Subject
            </p>
            <div className="bg-[#F9FAFB] p-3 rounded-xl">
              <p className="text-[#666666] font-normal text-md text-justify">
                {selectedRow?.subject}
              </p>
            </div>
            <p className="text-[#333333] font-medium text-md text-justify pb-2 mt-4">
              Comment
            </p>
            <div className="bg-[#F9FAFB] p-3 rounded-xl overflow-x-hidden overflow-y-auto custom-scrollbars">
              <p className="text-[#666666] font-normal text-md text-justify">
                {selectedRow?.comment}
              </p>
            </div>
          </div>
        </Modal>
      </ActionBarLayout>
    </ContentLayout>
  );
}

export default ContactRequests;
