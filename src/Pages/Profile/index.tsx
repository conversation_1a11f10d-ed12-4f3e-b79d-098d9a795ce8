import { <PERSON><PERSON>, Input<PERSON>ield } from '@Components/UI';
import { t } from 'i18next';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { useCallback, useEffect } from 'react';

import { useToast } from '@Components/UI/Toast/ToastProvider';
import {
  useGetAdminProfile,
  useUpdateAdminProfile,
} from '@Query/Hooks/useAdmin';
import ProfileLayout from '@Components/Layout/ProfileLayout';
import ContentLayout from '@Components/Layout/ContentLayout';
import { EMAIL_REGX, NO_SPEC_CHARACTER } from '@Helpers/Utils';

interface ProfileFormValues {
  first_name: string;
  last_name: string;
  email: string;
}

function Profile() {
  const { addToast } = useToast();

  const {
    mutate: updateProfile,
    data: updateProfileData,
    isError: isUpdateProfileError,
    error: updateProfileError,
    isSuccess: updateProfileSuccess,
    isLoading: isUpdateProfileLoading,
  } = useUpdateAdminProfile();

  const {
    data: viewProfileData,
    isError: isErrorInViewProfile,
    error: errorOfViewProfile,
    isSuccess: isSuccessViewProfile,
    // isLoading: viewProfileIsLoading,
  } = useGetAdminProfile();

  const profileSchema: yup.ObjectSchema<ProfileFormValues> = yup.object({
    first_name: yup
      .string()
      .strict()
      .trim(t('nospace'))
      .required(t('profile.validation.firstnameRequired'))
      .matches(NO_SPEC_CHARACTER, t('onlyAlphabates'))
      .min(3, t('profile.validation.validFirstname'))
      .max(50, t('profile.validation.firstnameGreaterthan')),
    last_name: yup
      .string()
      .strict()
      .trim(t('nospace'))
      .required(t('profile.validation.lastnameRequired'))
      .matches(NO_SPEC_CHARACTER, t('onlyAlphabates'))
      .min(1, t('profile.validation.validLastname'))
      .max(50, t('profile.validation.lastnameGreaterthan')),
    email: yup
      .string()
      .required(t('authentication.validation.emailRequired'))
      .matches(EMAIL_REGX, t('authentication.validation.validEmail'))
      .max(150, t('authentication.validation.emailMaxLength')),
  });

  const {
    handleSubmit,
    control,
    setValue,
    formState: { errors },
  } = useForm<ProfileFormValues>({
    defaultValues: {
      first_name: '',
      last_name: '',
    },
    resolver: yupResolver(profileSchema),
    mode: 'onChange',
  });

  useEffect(() => {
    if (isSuccessViewProfile && viewProfileData) {
      setValue('first_name', viewProfileData?.data?.data?.first_name);
      setValue('last_name', viewProfileData?.data?.data?.last_name);
      setValue('email', viewProfileData?.data?.data?.email);
    }

    if (isErrorInViewProfile) {
      addToast('error', errorOfViewProfile as string);
    }
  }, [
    viewProfileData,
    isSuccessViewProfile,
    isErrorInViewProfile,
    errorOfViewProfile,
  ]);

  useEffect(() => {
    if (updateProfileSuccess) {
      addToast('success', updateProfileData?.data?.message);
    }
    if (isUpdateProfileError) {
      addToast('error', updateProfileError as string);
    }
  }, [
    isUpdateProfileError,
    updateProfileData,
    updateProfileError,
    updateProfileSuccess,
  ]);

  const onSubmit = useCallback(
    (data: ProfileFormValues) => {
      updateProfile({ ...data });
    },
    [updateProfile]
  );

  return (
    <ProfileLayout>
      <ContentLayout
        title={t('profile.mainTitle')}
        subtitle={t('profile.subTitle')}
      >
        <div className="max-w-[470px]">
          <form
            onSubmit={handleSubmit(onSubmit)}
            className="flex flex-col gap-4"
          >
            <Controller
              control={control}
              name="first_name"
              render={({ field }) => (
                <InputField
                  label={t('profile.fname')}
                  placeholder={t('profile.fFname')}
                  field={field}
                  type="text"
                  errorMessage={errors?.first_name?.message}
                />
              )}
            />
            <Controller
              control={control}
              name="last_name"
              render={({ field }) => (
                <InputField
                  label={t('profile.lname')}
                  placeholder={t('profile.fLname')}
                  field={field}
                  type="text"
                  errorMessage={errors?.last_name?.message}
                />
              )}
            />
            <Controller
              control={control}
              name="email"
              render={({ field }) => (
                <InputField
                  label={t('profile.email')}
                  placeholder={t('profile.fEmail')}
                  disabled={true}
                  field={field}
                  type="text"
                  errorMessage={errors?.email?.message}
                />
              )}
            />
            <div className="mt-4">
              <Button
                text="Update"
                type="submit"
                disabled={isUpdateProfileLoading}
                loading={isUpdateProfileLoading}
              />
            </div>
          </form>
        </div>
      </ContentLayout>
    </ProfileLayout>
  );
}

export default Profile;
