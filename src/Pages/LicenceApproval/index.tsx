import { Button, DataTable, InputField, Modal } from '@Components/UI';
import { CheckIcon, CloseIcon, SearchIcon, WarningIcon } from '@Icons';
import React, { useEffect, useState } from 'react';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { ColumnDef, SortingState } from '@tanstack/react-table';
import ContentLayout from '@Components/Layout/ContentLayout';
import { useDebounce } from '@Hooks/useDebounce';
import {
  useGetLicenceApproveList,
  useLicenceApproveAction,
} from '@Query/Hooks/useLicenceApproval';
import { DateConvertor } from '@Helpers/Utils';
import clsx from 'clsx';
import useHasAccess from '@Hooks/useHasAccess';
import { MODULE_KEY } from '@Helpers/Constants';
import ActionBarLayout from '@Components/Layout/ActionBarLayout';

interface LicenceRequest {
  id: number;
  username: string;
  email: string;
  license: string;
  created_at: string;
  status: string;
  license_format: {
    id: number;
    prefix: string | null;
    postfix: string | null;
    state_name: string | null;
    min_number: number;
    max_number: number;
  };
}

type SearchableColumnDef<T> = ColumnDef<T> & {
  search?: boolean;
  vKey?: string;
};

function LicenceApproval() {
  const [openConfirmModal, setOpenConfirmModal] = useState<{
    open: boolean;
    type: 'approve' | 'reject';
    id: number;
  }>({ open: false, type: 'reject', id: 0 });
  const module_access = useHasAccess(MODULE_KEY.LICENCE_APPROVAL, [
    'read',
    'write',
    'update',
    'delete',
  ]);
  const [searchText, setSearchText] = useState<string>('');
  const [processingId, setProcessingId] = useState<number | null>(null);
  const [sorting, setSorting] = useState<SortingState>([
    { id: 'id', desc: true },
  ]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [filterStatus, setFilterStatus] = useState('');

  const { addToast } = useToast();

  const {
    data: licenceRequestList,
    isError: licenceRequestListError,
    isLoading: licenceRequestListIsLoading,
    refetch: refetchLicenceRequestList,
    isFetching: licenceRequestListIsFetching,
    error: licenceRequestListErrorMessage,
  } = useGetLicenceApproveList({
    ordering:
      sorting.length > 0
        ? `${sorting[0]?.desc === true ? '-' + sorting[0]?.id : sorting[0]?.id}`
        : '-id',
    page: currentPage,
    limit: pageSize,
    search: searchText,
    isSearchable: searchText.length > 0,
    isEnableStatusFilter: filterStatus !== '',
    status: filterStatus,
  });

  const {
    mutate: actionApproveReject,
    data: actionApproveRejectData,
    isError: actionApproveRejectIsError,
    isSuccess: actionApproveRejectIsSuccess,
    error: actionApproveRejectError,
  } = useLicenceApproveAction();

  const handleAction = (
    actionType: 'approve' | 'reject',
    requestId: number
  ) => {
    setProcessingId(requestId); // Track which row is being processed
    actionApproveReject({ actionType, requestId });
  };

  useEffect(() => {
    if (actionApproveRejectIsError) {
      addToast('error', actionApproveRejectError as string);
      setProcessingId(null); // Reset processing state on error
    }

    if (actionApproveRejectIsSuccess) {
      addToast('success', actionApproveRejectData?.data?.message);
      refetchLicenceRequestList();
      setProcessingId(null); // Reset processing state on success
      setOpenConfirmModal({ open: false, type: 'reject', id: 0 });
    }
  }, [
    actionApproveRejectIsError,
    actionApproveRejectIsSuccess,
    actionApproveRejectData,
    actionApproveRejectError,
  ]);

  useEffect(() => {
    if (licenceRequestListError) {
      addToast('error', licenceRequestListErrorMessage as string);
    }
  }, [licenceRequestListError, licenceRequestListErrorMessage]);

  useEffect(() => {
    if (searchText.length > 0) {
      setCurrentPage(1);
    }
  }, [searchText]);

  const debouncedSearchText = useDebounce(searchText, 500);

  useEffect(() => {
    refetchLicenceRequestList();
  }, [debouncedSearchText, sorting, pageSize, currentPage, filterStatus]);

  const columns: SearchableColumnDef<LicenceRequest>[] = [
    {
      header: 'User name',
      accessorKey: 'full_name',
      cell: (info) => info.getValue(),
    },
    {
      header: 'Email address',
      accessorKey: 'email',
      cell: (info) => info.getValue(),
    },
    {
      header: 'License number',
      accessorKey: 'license',
      enableSorting: false,
      cell: (info) => (
        <>
          {info?.row?.original?.license_format?.prefix ?? ''}
          {info.row?.original?.license}
          {info?.row?.original?.license_format?.postfix ?? ''}
        </>
      ),
    },
    {
      header: 'Date of submission',
      accessorKey: 'created_at',
      cell: (info) => {
        const row = info.row.original;
        return DateConvertor(row.created_at);
      },
    },
    {
      header: 'Current status',
      accessorKey: 'status',
      enableSorting: false,
      enableColumnFilter: true,
      meta: {
        filterVariant: 'select',
      },
      cell: (info) => {
        const row = info.row.original;
        return (
          <span
            className={clsx(
              row.status === 'pending'
                ? 'text-amber-400'
                : row.status === 'rejected'
                  ? 'text-error-0'
                  : row.status === 'resubmitted'
                    ? 'text-sky-400'
                    : 'text-primary-100',
              'font-bold uppercase'
            )}
          >
            {row.status}
          </span>
        );
      },
    },
    {
      header: 'Actions',
      accessorKey: 'action',
      enableSorting: false,
      cell: (info) => {
        const { id, status } = info.row.original;
        // const isProcessing = processingId === id;

        return (
          <div className="flex gap-3">
            {
              //   isProcessing ? (
              //   <Loader height={20} width={20} fill="#fff" />
              // ) : (
              (status === 'pending' || status === 'resubmitted') && (
                <>
                  <Button
                    text={<CheckIcon height={16} width={16} fill="#fff" />}
                    style={{
                      height: 30,
                      width: 50,
                      padding: 0,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                    width="max-w-sm"
                    disabled={!module_access?.update}
                    onClick={() =>
                      setOpenConfirmModal({ open: true, type: 'approve', id })
                    }
                  />
                  <Button
                    text={<CloseIcon height={14} width={14} fill="#fff" />}
                    style={{ height: 30, width: 50, padding: 0 }}
                    variant="other"
                    width="max-w-sm"
                    disabled={!module_access?.update}
                    className="bg-error-0 text-white flex items-center justify-center"
                    onClick={() =>
                      setOpenConfirmModal({ open: true, type: 'reject', id })
                    }
                  />
                </>
              )
              // )
            }
          </div>
        );
      },
    },
  ];

  return (
    <ContentLayout>
      <ActionBarLayout
        actionbarChildren={
          <React.Fragment>
            <div className="w-[300px]">
              <InputField
                placeholder="Search...."
                className="p-0"
                containerClassName="p-0"
                onChange={(i) => setSearchText(i.target.value)}
                rightIcon={<SearchIcon height={18} width={18} />}
              />
            </div>
          </React.Fragment>
        }
      >
        <DataTable
          data={licenceRequestList?.data?.data?.list}
          columns={columns}
          sorting={sorting}
          setSorting={setSorting}
          totalPages={licenceRequestList?.data?.data?.total_pages}
          currentPage={currentPage}
          onPageChange={setCurrentPage}
          pageSize={pageSize}
          setPageSize={setPageSize}
          pageSizeOptions={[10, 20, 30, 50]}
          onPageSizeChange={(num) => {
            setPageSize(num);
            setCurrentPage(1);
          }}
          isPagination={true}
          loading={licenceRequestListIsLoading || licenceRequestListIsFetching}
          filterData={[
            { label: 'Resubmitted', value: 'resubmitted' },
            { label: 'Pending', value: 'pending' },
            { label: 'Rejected', value: 'rejected' },
          ]}
          onFilterChange={(_, status) => setFilterStatus(status)}
        />

        <Modal
          isOpen={openConfirmModal.open}
          hideCloseIcon
          onClose={() =>
            setOpenConfirmModal({ open: false, type: 'reject', id: 0 })
          }
          children={
            <div className="flex flex-col items-center gap-[36px] ">
              <div className="bg-[#FFD8E0] h-14 w-14 flex justify-center items-center rounded-full">
                <WarningIcon width={25} height={25} fill="#FF3B30" />
              </div>
              <div className="text-black font-bold text-center w-full">
                Are you sure you want to {openConfirmModal.type}?
              </div>
              <div className=" w-full flex justify-center gap-6">
                <Button
                  text="Yes"
                  loading={processingId === openConfirmModal.id}
                  variant="primary"
                  onClick={() => {
                    handleAction(openConfirmModal.type, openConfirmModal.id);
                  }}
                />
                <Button
                  text="No"
                  variant="other"
                  onClick={() =>
                    setOpenConfirmModal({ open: false, type: 'reject', id: 0 })
                  }
                  className=" border border-error-0
              text-error-0 bg-transparent hover:border-error-0"
                />
              </div>
            </div>
          }
        />
      </ActionBarLayout>
    </ContentLayout>
  );
}

export default LicenceApproval;
