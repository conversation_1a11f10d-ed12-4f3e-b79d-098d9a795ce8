import ActionButtons from '@Components/Common/ActionButtons';
import ContentLayout from '@Components/Layout/ContentLayout';
import { Button, DataTable, InputField, Modal, ViewMore } from '@Components/UI';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { MODULE_KEY } from '@Helpers/Constants';
import useHasAccess from '@Hooks/useHasAccess';
import { CloseIcon, SearchIcon } from '@Icons';
import {
  useDeleteReportRequest,
  useTradiesHubPostReportList,
} from '@Query/Hooks/useTradiesHub';
import { ColumnDef, SortingState } from '@tanstack/react-table';
import React, { useEffect, useState, useRef } from 'react';
import { useDebounce } from '@Hooks/useDebounce';
import { useNavigate, useLocation } from 'react-router';
import { DateConvertor } from '@Helpers/Utils';
import clsx from 'clsx';
import ActionBarLayout from '@Components/Layout/ActionBarLayout';

type SearchableColumnDef<T> = ColumnDef<T> & {
  search?: boolean;
  vKey?: string;
};

interface TradiesHub {
  id: number;
  name?: string;
  title: string;
  content: string;
  is_active: boolean;
  category_ids: {
    id: number;
    name: string;
  }[];
  author?: {
    first_name: string;
    last_name: string;
  };
  comments_count: number;
  reports_count: number;
  content_type_name?: string;
  reason?: {
    name: string;
  };
  reported_content?: {
    content: string;
  };
  reported_by?: {
    first_name: string;
    last_name: string;
    profile_picture: string;
  };
  created_at: string;
  previous_content: string;
  is_reviewed: boolean;
}

function Report() {
  const { addToast } = useToast();
  const navigate = useNavigate();
  const location = useLocation();
  const toastShownRef = useRef(false);

  const module_access = useHasAccess(MODULE_KEY.TRADIES_HUB, [
    'read',
    'write',
    'update',
    'delete',
  ]);

  const [openDeleteReportItemModal, setOpenDeleteReportItemModal] =
    useState<boolean>(false);
  const [selectedRow, setSelectedRow] = useState<TradiesHub | null>(null);
  const [questionId, setQuestionId] = useState<number>(0);
  const [searchText, setSearchText] = useState<string>('');
  const [dataInitialized, setDataInitialized] = useState<boolean>(false);
  const [defaultReportActionStatus, setDefaultReportActionStatus] =
    useState<string>('delete');

  const [sorting, setSorting] = useState<SortingState>([
    { id: 'id', desc: true },
  ]);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  // Handle location state and initialization
  useEffect(() => {
    if (location?.state?.id > 0) {
      setQuestionId(location.state.id);
      setDataInitialized(true);
    }
  }, [location]);

  useEffect(() => {
    if (location && !location?.state?.id && location?.state !== null) {
      navigate(-1);
    }
  }, [location, navigate]);

  const getInitials = (firstName: string = '', lastName: string = '') => {
    return `${firstName.charAt(0).toUpperCase()}${lastName.charAt(0).toUpperCase()}`;
  };

  const getAvatarBgColor = (id: number) => {
    const colors = [
      'bg-blue-100 text-blue-600',
      'bg-green-100 text-green-600',
      'bg-purple-100 text-purple-600',
      'bg-amber-100 text-amber-600',
      'bg-pink-100 text-pink-600',
      'bg-cyan-100 text-cyan-600',
    ];
    return colors[id % colors.length];
  };

  const columns: SearchableColumnDef<TradiesHub>[] = [
    {
      header: 'Type',
      enableSorting: false,
      accessorKey: 'content_type_name',
      cell: (info) => {
        const row = info.row.original;
        const contentType = row?.content_type_name?.toLowerCase();

        let typeLabel = '';
        if (contentType === 'commentreplay') {
          typeLabel = 'Reply';
        } else if (contentType === 'post') {
          typeLabel = 'Post';
        } else if (contentType === 'comment') {
          typeLabel = 'Comment';
        } else {
          typeLabel = row?.content_type_name || 'Unknown';
        }

        return (
          <h1 className="px-2 py-1 rounded-full font-medium text-green-600">
            {typeLabel}
          </h1>
        );
      },
    },
    {
      header: 'Reason',
      enableSorting: false,
      accessorKey: 'Reason',
      cell: (info) => {
        const row = info.row.original;
        return (
          <div className="flex flex-col gap-1">
            <ViewMore text={row?.reason?.name ?? ''} maxLength={60} />
          </div>
        );
      },
    },
    {
      header: 'Content',
      enableSorting: false,
      accessorKey: 'content',
      cell: (info) => {
        const row = info.row.original;
        return (
          <div className="flex flex-col gap-1">
            <ViewMore
              text={row.previous_content || row.reported_content?.content || ''}
              maxLength={60}
            />
          </div>
        );
      },
    },
    {
      header: 'Reported By',
      enableSorting: false,
      accessorKey: 'author',
      cell: (info) => {
        const row = info.row.original;
        const avatarBgColor = getAvatarBgColor(row.id);

        return (
          <div className="flex items-center gap-2">
            {row?.reported_by?.profile_picture ? (
              <img
                src={String(row?.reported_by?.profile_picture)}
                alt="Profile"
                className="rounded-full h-10 w-10 object-cover shadow-md"
              />
            ) : (
              <div
                className={clsx(
                  'h-10 w-10 rounded-full flex items-center justify-center font-medium',
                  avatarBgColor
                )}
              >
                {getInitials(
                  row?.reported_by?.first_name,
                  row?.reported_by?.last_name
                )}
              </div>
            )}
            <div className="text-sm">
              <ViewMore
                text={`${row?.reported_by?.first_name} ${row?.reported_by?.last_name}`}
                maxLength={15}
              />
            </div>
          </div>
        );
      },
    },
    {
      header: 'Date',
      accessorKey: 'created_at',
      cell: (info) => {
        const row = info.row.original;
        return DateConvertor(row.created_at);
      },
    },
    {
      header: 'Action',
      accessorKey: 'action',
      enableSorting: false,
      cell: (info) => (
        <ActionButtons
          ignoreIcon
          ignoreIconDisabled={info.row.original.is_reviewed}
          onIgnore={() => {
            setSelectedRow(info.row.original);
            setDefaultReportActionStatus('ignore');
            setOpenDeleteReportItemModal(true);
          }}
          deleteIcon
          deleteIconDisabled={!module_access.delete}
          onDelete={() => {
            setSelectedRow(info.row.original);
            setDefaultReportActionStatus('delete');
            setOpenDeleteReportItemModal(true);
          }}
        />
      ),
    },
  ];

  const {
    data: reportDataList,
    isError: reportDataListError,
    isLoading: reportDataListIsLoading,
    refetch: refetchReportDataList,
    isFetching: reportDataListIsFetching,
    error: reportDataListErrorMessage,
  } = useTradiesHubPostReportList({
    id: questionId,
    ordering:
      sorting.length > 0
        ? `${sorting[0]?.desc === true ? '-' + sorting[0]?.id : sorting[0]?.id}`
        : 'id',
    page: currentPage,
    limit: pageSize,
    isSearchable: searchText.length > 0,
    search: searchText,
    enabled: dataInitialized && questionId > 0,
  });

  const {
    mutate: deleteReportItem,
    error: deleteReportItemError,
    isError: isErrorDeleteReportItem,
    isSuccess: isSuccessDeletePostItem,
    data: deletePostItemData,
    isLoading: isLoadingDeletePostItem,
  } = useDeleteReportRequest({
    id: selectedRow ? selectedRow?.id : 0,
    action_type: defaultReportActionStatus,
  });

  const currentData = reportDataList?.data?.data?.list;

  const onPageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const onPageSizeChange = (newSize: number) => {
    setPageSize(newSize);
    setCurrentPage(1);
  };

  // Handle search text changes
  useEffect(() => {
    if (searchText.length > 0) {
      setCurrentPage(1);
    }
  }, [searchText]);

  const debouncedSearchText = useDebounce(searchText, 500);

  // Fetch data when search text changes
  useEffect(() => {
    if (dataInitialized && questionId > 0) {
      refetchReportDataList();
    }
  }, [debouncedSearchText, dataInitialized, questionId, refetchReportDataList]);

  // Handle error scenarios
  useEffect(() => {
    if (reportDataListError) {
      addToast('error', reportDataListErrorMessage as string);
    }

    if (isErrorDeleteReportItem) {
      addToast('error', deleteReportItemError as string);
    }
  }, [
    reportDataListError,
    reportDataListErrorMessage,
    deleteReportItemError,
    isErrorDeleteReportItem,
    addToast,
  ]);

  // Fetch data when pagination or sorting changes
  useEffect(() => {
    if (dataInitialized && questionId > 0) {
      refetchReportDataList();
    }
  }, [
    sorting,
    pageSize,
    currentPage,
    dataInitialized,
    questionId,
    refetchReportDataList,
  ]);

  // Handle delete item success
  useEffect(() => {
    if (isSuccessDeletePostItem && !toastShownRef.current) {
      toastShownRef.current = true;
      setSelectedRow(null);
      addToast('success', deletePostItemData?.data?.message);
      if (currentData?.length === 1 && currentPage > 1) {
        setCurrentPage((prev) => prev - 1);
      } else if (dataInitialized && questionId > 0) {
        refetchReportDataList();
      }
      setOpenDeleteReportItemModal(false);
    }
    if (!isSuccessDeletePostItem) {
      toastShownRef.current = false;
    }
  }, [
    isSuccessDeletePostItem,
    deletePostItemData?.data?.message,
    currentData?.length,
    currentPage,
    dataInitialized,
    questionId,
    refetchReportDataList,
    addToast,
  ]);

  return (
    <ContentLayout>
      <ActionBarLayout
        actionbarChildren={
          <React.Fragment>
            <div className="w-auto">
              <InputField
                placeholder="Search reported by"
                inputContainerClassName="max-h-[46px] w-max"
                className="p-0"
                containerClassName="p-0"
                onChange={(i) => setSearchText(i.target.value)}
                rightIcon={<SearchIcon height={18} width={18} />}
              />
            </div>
          </React.Fragment>
        }
      >
        <DataTable
          data={currentData}
          columns={columns}
          sorting={sorting}
          setSorting={setSorting}
          totalPages={reportDataList?.data?.data?.total_pages}
          currentPage={currentPage}
          onPageChange={onPageChange}
          pageSize={pageSize}
          setPageSize={(num) => {
            setPageSize(num);
            setCurrentPage(1);
          }}
          pageSizeOptions={[10, 20, 30, 50]}
          onPageSizeChange={onPageSizeChange}
          isPagination={true}
          loading={reportDataListIsLoading || reportDataListIsFetching}
        />

        <Modal
          isOpen={openDeleteReportItemModal}
          hideCloseIcon
          onClose={() => {
            setOpenDeleteReportItemModal(false);
            setSelectedRow(null);
          }}
          children={
            <div className="flex flex-col items-center gap-[36px]">
              <div className="bg-[#FFD8E0] h-14 w-14 flex justify-center items-center rounded-full">
                <CloseIcon width={18} height={18} fill="#FF3B30" />
              </div>
              <div className="text-black font-bold text-center w-full">
                {defaultReportActionStatus === 'delete'
                  ? `Are you sure you want to delete this ${selectedRow?.content_type_name}?`
                  : 'Are you sure you want to ignore this report?'}
              </div>
              <div className="w-full flex justify-center gap-6">
                <Button
                  onClick={deleteReportItem}
                  text="Yes"
                  variant="other"
                  className=" border border-error-0 text-error-0 bg-transparent hover:border-error-0"
                  loading={isLoadingDeletePostItem}
                />
                <Button
                  text="No"
                  variant="outline"
                  disabled={isLoadingDeletePostItem}
                  onClick={() => {
                    setOpenDeleteReportItemModal(false);
                    setSelectedRow(null);
                  }}
                />
              </div>
            </div>
          }
        />
      </ActionBarLayout>
    </ContentLayout>
  );
}

export default Report;
