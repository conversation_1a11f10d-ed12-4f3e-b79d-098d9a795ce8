import ActionButtons from '@Components/Common/ActionButtons';
import ContentLayout from '@Components/Layout/ContentLayout';
import { Button, DataTable, InputField, Modal, ViewMore } from '@Components/UI';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { MODULE_KEY } from '@Helpers/Constants';
import useHasAccess from '@Hooks/useHasAccess';
import { CloseIcon, SearchIcon, ClockIcon } from '@Icons';
import {
  useDeleteContentRequest,
  useTradiesHubPostList,
  useTradiesHubContentStatus,
  useTradiesHubCategoryList,
} from '@Query/Hooks/useTradiesHub';
import { ColumnDef, SortingState } from '@tanstack/react-table';
import React, { useEffect, useState } from 'react';
import { useDebounce } from '@Hooks/useDebounce';
import Switch from 'react-switch';
import clsx from 'clsx';
import PATHS from '@Config/Path.Config';
import { useNavigate } from 'react-router';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import relativeTime from 'dayjs/plugin/relativeTime';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import isToday from 'dayjs/plugin/isToday';
import isYesterday from 'dayjs/plugin/isYesterday';
import ActionBarLayout from '@Components/Layout/ActionBarLayout';

dayjs.extend(utc);
dayjs.extend(relativeTime);
dayjs.extend(localizedFormat);
dayjs.extend(isToday);
dayjs.extend(isYesterday);

export const formatPostDate = (utcDateString: string): string => {
  const localDate = dayjs.utc(utcDateString).local();

  if (localDate.isToday()) {
    return localDate.format('hh:mm A'); // Example: 04:30 PM
  }

  if (localDate.isYesterday()) {
    return 'Yesterday';
  }

  const now = dayjs();
  const diffInDays = now.diff(localDate, 'day');
  const diffInWeeks = now.diff(localDate, 'week');
  const diffInMonths = now.diff(localDate, 'month');

  if (diffInDays < 7) {
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
  }

  if (diffInWeeks < 4) {
    return `${diffInWeeks} week${diffInWeeks > 1 ? 's' : ''} ago`;
  }

  return `${diffInMonths} month${diffInMonths > 1 ? 's' : ''} ago`;
};

type SearchableColumnDef<T> = ColumnDef<T> & {
  search?: boolean;
  vKey?: string;
};

interface TradiesHub {
  id: number;
  name?: string;
  title: string;
  content: string;
  is_active: boolean;
  category_ids: {
    id: number;
    name: string;
  }[];
  author?: {
    first_name: string;
    last_name: string;
    profile_picture: string;
  };
  comments_count: number;
  reports_count: number;
  content_type_name?: string;
  created_at: string;
}

function TradiesHub() {
  const { addToast } = useToast();
  const navigate = useNavigate();

  const module_access = useHasAccess(MODULE_KEY.TRADIES_HUB, [
    'read',
    'write',
    'update',
    'delete',
  ]);

  const [openDeletePostItemModal, setOpenDeletePostItemModal] =
    useState<boolean>(false);
  const [openViewPostItemModal, setOpenViewPostItemModal] =
    useState<boolean>(false);
  const [selectedRow, setSelectedRow] = useState<TradiesHub | null>();
  const [searchText, setSearchText] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState('');

  const [sorting, setSorting] = useState<SortingState>([
    { id: 'id', desc: true },
  ]);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  const getInitials = (firstName: string = '', lastName: string = '') => {
    return `${firstName.charAt(0).toUpperCase()}${lastName.charAt(0).toUpperCase()}`;
  };

  const getAvatarBgColor = (id: number) => {
    const colors = [
      'bg-blue-100 text-blue-600',
      'bg-green-100 text-green-600',
      'bg-purple-100 text-purple-600',
      'bg-amber-100 text-amber-600',
      'bg-pink-100 text-pink-600',
      'bg-cyan-100 text-cyan-600',
    ];
    return colors[id % colors.length];
  };

  const columns: SearchableColumnDef<TradiesHub>[] = [
    {
      header: 'Posted By',
      enableSorting: false,
      accessorKey: 'author',
      cell: (info) => {
        const row = info.row.original;
        const avatarBgColor = getAvatarBgColor(row.id);
        return (
          <div className="flex items-center gap-2">
            {row?.author?.profile_picture ? (
              <img
                src={String(row?.author?.profile_picture)}
                alt="Profile"
                className="rounded-full h-10 w-10 object-cover shadow-md"
              />
            ) : (
              <div
                className={clsx(
                  'h-10 w-10 rounded-full flex items-center justify-center font-medium',
                  avatarBgColor
                )}
              >
                {getInitials(row?.author?.first_name, row?.author?.last_name)}
              </div>
            )}
            <div className="text-sm">
              <ViewMore
                text={`${row?.author?.first_name} ${row?.author?.last_name}`}
                maxLength={15}
              />
            </div>
          </div>
        );
      },
    },
    {
      header: 'Category',
      enableSorting: false,
      accessorKey: 'Category',
      enableColumnFilter: true,
      meta: {
        filterVariant: 'select',
      },
      cell: (info) => {
        const { category_ids } = info.row.original;

        return (
          <div className="flex flex-wrap gap-1">
            {category_ids?.map((cat) => (
              <span
                key={cat.id}
                className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-600"
              >
                {cat.name}
              </span>
            ))}
          </div>
        );
      },
    },
    {
      header: 'Question',
      enableSorting: false,
      accessorKey: 'content',
      cell: (info) => {
        const row = info.row.original;
        return (
          <div className="flex flex-col gap-1">
            <ViewMore text={row.content} maxLength={60} />
          </div>
        );
      },
    },
    {
      header: '#Comments Count',
      enableSorting: false,
      accessorKey: 'comments_count',
      cell: (info) => {
        const row = info.row.original;

        return (
          <div
            className="w-20 flex items-center justify-center rounded-lg px-2 py-1 cursor-pointer bg-blue-100 hover:bg-blue-200 transition-colors duration-200 text-blue-600 font-medium"
            onClick={() => {
              navigate(PATHS.TRADIE_HUB_COMMENTS, {
                state: info?.row?.original,
              });
            }}
          >
            {row.comments_count}
          </div>
        );
      },
    },
    {
      header: 'Status',
      accessorKey: 'status',
      enableSorting: false,
      cell: (info) => {
        const { is_active } = info.row.original;
        return (
          <div className="flex gap-4">
            <Switch
              checked={is_active}
              onChange={() =>
                toggleTradiesHubPostStatus({
                  id: info.row.original.id,
                  type: selectedRow?.content_type_name || 'post',
                })
              }
              disabled={!module_access?.update || isLoadingtoggle}
              onColor="#E0E3FF"
              onHandleColor="#FF8800"
              offColor="#E8ECF3"
              offHandleColor="#D0D5DD"
              handleDiameter={24}
              uncheckedIcon={false}
              checkedIcon={false}
              boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
              activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
              height={16}
              width={40}
            />
          </div>
        );
      },
    },
    {
      header: 'Action',
      accessorKey: 'action',
      enableSorting: false,
      cell: (info) => (
        <ActionButtons
          reportCount={info.row.original.reports_count}
          deleteIcon
          reportIcon
          viewIcon
          deleteIconDisabled={!module_access.delete}
          onDelete={() => {
            setSelectedRow(info.row.original);
            setOpenDeletePostItemModal(true);
          }}
          onReport={() => {
            navigate(PATHS.TRADIE_HUB_POST_REPORT, {
              state: info?.row?.original,
            });
          }}
          onView={() => {
            setSelectedRow(info.row.original);
            setOpenViewPostItemModal(true);
          }}
        />
      ),
    },
  ];

  const {
    data: postDataList,
    isError: postDataListError,
    isLoading: postDataListIsLoading,
    refetch: refetchPostDataList,
    isFetching: postDataListIsFetching,
    error: postDataListErrorMessage,
  } = useTradiesHubPostList({
    ordering:
      sorting.length > 0
        ? `${sorting[0]?.desc === true ? '-' + sorting[0]?.id : sorting[0]?.id}`
        : 'id',
    page: currentPage,
    limit: pageSize,
    search: searchText,
    isSearchable: searchText.length > 0,
    status: filterStatus,
    isEnableStatusFilter: filterStatus !== '',
  });

  const {
    mutate: deletePostItem,
    error: deletePostItemError,
    isError: isErrorDeletePostItem,
    isSuccess: isSuccessDeletePostItem,
    data: deletePostItemData,
    isLoading: isLoadingDeletePostItem,
  } = useDeleteContentRequest({
    id: selectedRow ? selectedRow?.id : 0,
    type: selectedRow?.content_type_name || 'post',
  });

  const {
    mutate: toggleTradiesHubPostStatus,
    error: toggleError,
    isError: isErrortoggle,
    data: toggleData,
    isSuccess: isSuccesstoggle,
    isLoading: isLoadingtoggle,
  } = useTradiesHubContentStatus();

  const { data: categoryDataList } = useTradiesHubCategoryList({
    ordering:
      sorting.length > 0
        ? `${sorting[0]?.desc === true ? '-' + sorting[0]?.id : sorting[0]?.id}`
        : 'id',
    page: 1,
    limit: 1000,
    search: searchText,
    isSearchable: searchText.length > 0,
  });

  const filterData =
    categoryDataList?.data?.data?.list?.map(
      (category: { id: string; name: string }) => ({
        label: category.name,
        value: category.id,
      })
    ) || [];

  const currentData = postDataList?.data?.data?.list;

  const onPageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const onPageSizeChange = (newSize: number) => {
    setPageSize(newSize);
    setCurrentPage(1);
  };

  useEffect(() => {
    if (searchText.length > 0) {
      setCurrentPage(1);
    }
  }, [searchText]);

  const debouncedSearchText = useDebounce(searchText, 500); // 500ms debounce

  useEffect(() => {
    refetchPostDataList();
  }, [debouncedSearchText, filterStatus]);

  useEffect(() => {
    if (postDataListError) {
      addToast('error', postDataListErrorMessage as string);
    }

    if (isErrorDeletePostItem) {
      addToast('error', deletePostItemError as string);
    }
  }, [
    postDataListError,
    postDataListErrorMessage,
    deletePostItemError,
    isErrorDeletePostItem,
  ]);

  useEffect(() => {
    if (isErrortoggle) {
      addToast('error', toggleError as string);
    }
    if (isSuccesstoggle) {
      refetchPostDataList();
      addToast(
        'success',
        toggleData?.data?.message || 'File status updated successfully'
      );
    }
  }, [isErrortoggle, toggleError, isSuccesstoggle]);

  useEffect(() => {
    refetchPostDataList();
  }, [sorting, pageSize, currentPage]);

  useEffect(() => {
    if (isSuccessDeletePostItem) {
      setSelectedRow(null);
      addToast('success', deletePostItemData?.data?.message);
      if (currentData?.length === 1 && currentPage > 1) {
        setCurrentPage((prev) => prev - 1);
      } else {
        refetchPostDataList();
      }
      setOpenDeletePostItemModal(false);
    }
  }, [isSuccessDeletePostItem]);

  return (
    <ContentLayout>
      <ActionBarLayout
        actionbarChildren={
          <React.Fragment>
            <div className="w-auto">
              <InputField
                placeholder="Search title, posted by"
                inputContainerClassName="max-h-[46px] w-max "
                className="p-0"
                containerClassName="p-0"
                onChange={(i) => setSearchText(i.target.value)}
                rightIcon={<SearchIcon height={18} width={18} />}
              />
            </div>
          </React.Fragment>
        }
      >
        <DataTable
          data={currentData}
          columns={columns}
          sorting={sorting}
          setSorting={setSorting}
          totalPages={postDataList?.data?.data?.total_pages}
          currentPage={currentPage}
          onPageChange={onPageChange}
          pageSize={pageSize}
          setPageSize={(num) => {
            setPageSize(num);
            setCurrentPage(1);
          }}
          pageSizeOptions={[10, 20, 30, 50]}
          onPageSizeChange={onPageSizeChange}
          filterData={filterData}
          isPagination={true}
          loading={postDataListIsLoading || postDataListIsFetching}
          onFilterChange={(_, status) => {
            setFilterStatus(status);
            setCurrentPage(1);
          }}
        />

        <Modal
          isOpen={openDeletePostItemModal}
          hideCloseIcon
          onClose={() => {
            setOpenDeletePostItemModal(false);
            setSelectedRow(null);
          }}
          children={
            <div className="flex flex-col items-center gap-[36px] ">
              <div className="bg-[#FFD8E0] h-14 w-14 flex justify-center items-center rounded-full">
                <CloseIcon width={18} height={18} fill="#FF3B30" />
              </div>
              <div className="text-black font-bold text-center w-full">
                Are you sure want to delete post?
              </div>
              <div className=" w-full flex justify-center gap-6">
                <Button
                  onClick={deletePostItem}
                  text="Yes"
                  variant="other"
                  className=" border border-error-0
              text-error-0 bg-transparent hover:border-error-0"
                  loading={isLoadingDeletePostItem}
                />
                <Button
                  text="No"
                  variant="outline"
                  disabled={isLoadingDeletePostItem}
                  onClick={() => {
                    setOpenDeletePostItemModal(false);
                    setSelectedRow(null);
                  }}
                />
              </div>
            </div>
          }
        />
        <Modal
          size="dxl"
          isOpen={openViewPostItemModal}
          onClose={() => {
            setOpenViewPostItemModal(false);
            setSelectedRow(null);
          }}
          children={
            <div className="flex flex-col border border-gray-200 rounded-[8px] p-4 gap-y-2 shadow-primary">
              <div className="flex flex-1 justify-between items-start">
                <div className="flex items-center gap-x-3">
                  <div className="h-12 w-12 flex rounded-full">
                    <div className="h-full w-full rounded-full object-cover flex justify-center items-center bg-[#ffe3c5] text-primary-100 font-medium">
                      {selectedRow?.author?.first_name?.charAt(0)}
                      {selectedRow?.author?.last_name?.charAt(0)}
                    </div>
                  </div>
                  <div className="flex flex-col ">
                    <div className="font-bold">
                      <ViewMore
                        text={`${selectedRow?.author?.first_name} ${selectedRow?.author?.last_name}`}
                        maxLength={60}
                      />
                    </div>
                    <div className="text-[#666666] flex items-center text-xs gap-x-1">
                      <ClockIcon height={12} width={12} />
                      {selectedRow?.created_at
                        ? formatPostDate(selectedRow.created_at)
                        : ''}
                    </div>
                  </div>
                </div>
              </div>

              <div className="pt-4">
                <ViewMore maxLength={450} text={selectedRow?.content ?? ''} />
              </div>
              <div className="pt-4 flex flex-wrap gap-1">
                {selectedRow?.category_ids?.map((cat) => (
                  <span
                    key={cat.id}
                    className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-600"
                  >
                    {cat.name}
                  </span>
                ))}
              </div>
            </div>
          }
        />
      </ActionBarLayout>
    </ContentLayout>
  );
}

export default TradiesHub;
