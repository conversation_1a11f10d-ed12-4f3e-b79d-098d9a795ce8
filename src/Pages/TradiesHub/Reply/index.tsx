import ActionButtons from '@Components/Common/ActionButtons';
import ContentLayout from '@Components/Layout/ContentLayout';
import { Button, DataTable, InputField, Modal, ViewMore } from '@Components/UI';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { MODULE_KEY } from '@Helpers/Constants';
import useHasAccess from '@Hooks/useHasAccess';
import { CloseIcon, SearchIcon } from '@Icons';
import {
  useDeleteContentRequest,
  useTradiesHubReplyList,
} from '@Query/Hooks/useTradiesHub';
import { ColumnDef, SortingState } from '@tanstack/react-table';
import { useEffect, useState, useRef } from 'react';
import { useDebounce } from '@Hooks/useDebounce';
import { useNavigate, useLocation } from 'react-router';
import clsx from 'clsx';
import PATHS from '@Config/Path.Config';
import ActionBarLayout from '@Components/Layout/ActionBarLayout';

type SearchableColumnDef<T> = ColumnDef<T> & {
  search?: boolean;
  vKey?: string;
};

interface TradiesHub {
  id: number;
  name?: string;
  title: string;
  content: string;
  is_active: boolean;
  category_ids: {
    id: number;
    name: string;
  }[];
  author?: {
    first_name: string;
    last_name: string;
    profile_picture: string;
  };
  comments_count: number;
  reports_count: number;
  content_type_name?: string;
  report_count: number;
}

function Reply() {
  const { addToast } = useToast();
  const navigate = useNavigate();
  const location = useLocation();
  const toastShownRef = useRef(false);

  const module_access = useHasAccess(MODULE_KEY.TRADIES_HUB, [
    'read',
    'write',
    'update',
    'delete',
  ]);

  const [openDeleteReplyItemModal, setOpenDeleteReplyItemModal] =
    useState<boolean>(false);
  const [selectedRow, setSelectedRow] = useState<TradiesHub | null>();
  const [questionId, setQuestionId] = useState<number>(0);
  const [searchText, setSearchText] = useState<string>('');
  const [dataInitialized, setDataInitialized] = useState<boolean>(false);

  useEffect(() => {
    if (location?.state?.id > 0) {
      setQuestionId(location.state.id);
      setDataInitialized(true);
    }
  }, [location]);

  useEffect(() => {
    if (location && !location?.state?.id && location?.state !== null) {
      navigate(-1);
    }
  }, [location, navigate]);

  const [sorting, setSorting] = useState<SortingState>([
    { id: 'id', desc: true },
  ]);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  const getInitials = (firstName: string = '', lastName: string = '') => {
    return `${firstName.charAt(0).toUpperCase()}${lastName.charAt(0).toUpperCase()}`;
  };

  const getAvatarBgColor = (id: number) => {
    const colors = [
      'bg-blue-100 text-blue-600',
      'bg-green-100 text-green-600',
      'bg-purple-100 text-purple-600',
      'bg-amber-100 text-amber-600',
      'bg-pink-100 text-pink-600',
      'bg-cyan-100 text-cyan-600',
    ];
    return colors[id % colors.length];
  };

  const columns: SearchableColumnDef<TradiesHub>[] = [
    {
      header: 'Posted By',
      enableSorting: false,
      accessorKey: 'author',
      cell: (info) => {
        const row = info.row.original;
        const avatarBgColor = getAvatarBgColor(row.id);
        return (
          <div className="flex items-center gap-2">
            {row?.author?.profile_picture ? (
              <img
                src={String(row?.author?.profile_picture)}
                alt="Profile"
                className="rounded-full h-10 w-10 object-cover shadow-md"
              />
            ) : (
              <div
                className={clsx(
                  'h-10 w-10 rounded-full flex items-center justify-center font-medium',
                  avatarBgColor
                )}
              >
                {getInitials(row?.author?.first_name, row?.author?.last_name)}
              </div>
            )}
            <div className="text-sm">
              <ViewMore
                text={`${row?.author?.first_name} ${row?.author?.last_name}`}
                maxLength={15}
              />
            </div>
          </div>
        );
      },
    },
    {
      header: 'Reply',
      enableSorting: false,
      accessorKey: 'content',
      cell: (info) => {
        const row = info.row.original;
        return (
          <div className="flex flex-col gap-1">
            <ViewMore text={row.content} maxLength={60} />
          </div>
        );
      },
    },
    {
      header: 'Action',
      accessorKey: 'action',
      enableSorting: false,
      cell: (info) => (
        <ActionButtons
          reportCount={info.row.original.report_count}
          reportIcon
          deleteIcon
          deleteIconDisabled={!module_access.delete}
          onDelete={() => {
            setSelectedRow(info.row.original);
            setOpenDeleteReplyItemModal(true);
          }}
          onReport={() => {
            navigate(PATHS.TRADIE_HUB_REPLIES_REPORT, {
              state: info?.row?.original,
            });
          }}
        />
      ),
    },
  ];

  const {
    data: replyDataList,
    isError: replyDataListError,
    isLoading: replyDataListIsLoading,
    refetch: refetchReplyDataList,
    isFetching: replyDataListIsFetching,
    error: replyDataListErrorMessage,
  } = useTradiesHubReplyList({
    id: questionId,
    ordering:
      sorting.length > 0
        ? `${sorting[0]?.desc === true ? '-' + sorting[0]?.id : sorting[0]?.id}`
        : 'id',
    page: currentPage,
    limit: pageSize,
    isSearchable: searchText.length > 0,
    search: searchText,
    enabled: dataInitialized && questionId > 0,
  });

  const {
    mutate: deleteReplyItem,
    error: deleteReplyItemError,
    isError: isErrorDeleteReplyItem,
    isSuccess: isSuccessDeleteReplyItem,
    data: deleteReplyItemData,
    isLoading: isLoadingDeleteReplyItem,
  } = useDeleteContentRequest({
    id: selectedRow ? selectedRow?.id : 0,
    type: selectedRow?.content_type_name || 'commentreply',
  });

  const currentData = replyDataList?.data?.data?.list;

  const onPageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const onPageSizeChange = (newSize: number) => {
    setPageSize(newSize);
    setCurrentPage(1);
  };

  useEffect(() => {
    if (searchText.length > 0) {
      setCurrentPage(1);
    }
  }, [searchText]);

  const debouncedSearchText = useDebounce(searchText, 500);

  useEffect(() => {
    if (dataInitialized && questionId > 0) {
      refetchReplyDataList();
    }
  }, [debouncedSearchText, dataInitialized, questionId]);

  useEffect(() => {
    if (replyDataListError) {
      addToast('error', replyDataListErrorMessage as string);
    }

    if (isErrorDeleteReplyItem) {
      addToast('error', deleteReplyItemError as string);
    }
  }, [
    replyDataListError,
    replyDataListErrorMessage,
    deleteReplyItemError,
    isErrorDeleteReplyItem,
  ]);

  useEffect(() => {
    if (dataInitialized && questionId > 0) {
      refetchReplyDataList();
    }
  }, [
    sorting,
    pageSize,
    currentPage,
    dataInitialized,
    questionId,
    refetchReplyDataList,
  ]);

  useEffect(() => {
    if (isSuccessDeleteReplyItem && !toastShownRef.current) {
      toastShownRef.current = true;
      setSelectedRow(null);
      addToast('success', deleteReplyItemData?.data?.message);

      if (currentData?.length === 1 && currentPage > 1) {
        setCurrentPage((prev) => prev - 1);
      } else if (dataInitialized && questionId > 0) {
        refetchReplyDataList();
      }

      setOpenDeleteReplyItemModal(false);
    }
    if (!isSuccessDeleteReplyItem) {
      toastShownRef.current = false;
    }
  }, [
    isSuccessDeleteReplyItem,
    deleteReplyItemData?.data?.message,
    currentData?.length,
    currentPage,
    dataInitialized,
    questionId,
    refetchReplyDataList,
    addToast,
  ]);

  return (
    <ContentLayout>
      <ActionBarLayout
        actionbarChildren={
          <div className="w-full flex gap-5">
            <div className="w-auto">
              <InputField
                placeholder="Search posted by"
                inputContainerClassName="max-h-[46px] w-max "
                className="p-0"
                containerClassName="p-0"
                onChange={(i) => setSearchText(i.target.value)}
                rightIcon={<SearchIcon height={18} width={18} />}
              />
            </div>
          </div>
        }
      >
        <DataTable
          data={currentData}
          columns={columns}
          sorting={sorting}
          setSorting={setSorting}
          totalPages={replyDataList?.data?.data?.total_pages}
          currentPage={currentPage}
          onPageChange={onPageChange}
          pageSize={pageSize}
          setPageSize={(num) => {
            setPageSize(num);
            setCurrentPage(1);
          }}
          pageSizeOptions={[10, 20, 30, 50]}
          onPageSizeChange={onPageSizeChange}
          isPagination={true}
          loading={replyDataListIsLoading || replyDataListIsFetching}
        />

        <Modal
          isOpen={openDeleteReplyItemModal}
          hideCloseIcon
          onClose={() => {
            setOpenDeleteReplyItemModal(false);
            setSelectedRow(null);
          }}
          children={
            <div className="flex flex-col items-center gap-[36px] ">
              <div className="bg-[#FFD8E0] h-14 w-14 flex justify-center items-center rounded-full">
                <CloseIcon width={18} height={18} fill="#FF3B30" />
              </div>
              <div className="text-black font-bold text-center w-full">
                Are you sure want to delete reply?
              </div>
              <div className=" w-full flex justify-center gap-6">
                <Button
                  onClick={deleteReplyItem}
                  text="Yes"
                  variant="other"
                  className=" border border-error-0
              text-error-0 bg-transparent hover:border-error-0"
                  loading={isLoadingDeleteReplyItem}
                />
                <Button
                  text="No"
                  variant="outline"
                  disabled={isLoadingDeleteReplyItem}
                  onClick={() => {
                    setOpenDeleteReplyItemModal(false);
                    setSelectedRow(null);
                  }}
                />
              </div>
            </div>
          }
        />
      </ActionBarLayout>
    </ContentLayout>
  );
}

export default Reply;
