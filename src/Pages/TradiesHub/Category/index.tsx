import ActionButtons from '@Components/Common/ActionButtons';
import ContentLayout from '@Components/Layout/ContentLayout';
import { Button, DataTable, InputField, Modal, ViewMore } from '@Components/UI';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { MODULE_KEY } from '@Helpers/Constants';
import useHasAccess from '@Hooks/useHasAccess';
import { CloseIcon, AddIcon, SearchIcon } from '@Icons';
import {
  useDeleteCategoryRequest,
  useTradiesHubCategoryList,
  useTradiesHubCategoryStatus,
} from '@Query/Hooks/useTradiesHub';
import { ColumnDef, SortingState } from '@tanstack/react-table';
import React, { useEffect, useState } from 'react';
import { useDebounce } from '@Hooks/useDebounce';
import AddCategory from './AddCategory';
import Switch from 'react-switch';
import ActionBarLayout from '@Components/Layout/ActionBarLayout';

type SearchableColumnDef<T> = ColumnDef<T> & {
  search?: boolean;
  vKey?: string;
};

interface TradiesHub {
  id: number;
  name: string;
  description: string;
  is_active: boolean;
}

function TradiesHub() {
  const { addToast } = useToast();

  const module_access = useHasAccess(MODULE_KEY.TRADIES_HUB, [
    'read',
    'write',
    'update',
    'delete',
  ]);

  const [openDeleteCategoryItemModal, setOpenDeleteCategoryItemModal] =
    useState<boolean>(false);
  const [selectedRow, setSelectedRow] = useState<TradiesHub | null>();
  const [openAddFormModal, setOpenAddFormModal] = useState<boolean>(false);
  const [openEditFormModal, setOpenEditFormModal] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>('');

  const [sorting, setSorting] = useState<SortingState>([
    { id: 'id', desc: true },
  ]);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  const columns: SearchableColumnDef<TradiesHub>[] = [
    {
      header: 'Name',
      accessorKey: 'name',
      cell: (info) => {
        const row = info.row.original;
        return (
          <div className="flex flex-col gap-1">
            <ViewMore text={row.name} maxLength={60} />
          </div>
        );
      },
    },
    {
      header: 'Description',
      enableSorting: false,
      accessorKey: 'Description',
      cell: (info) => {
        const row = info.row.original;
        return (
          <div className="flex flex-col gap-1">
            <ViewMore text={row.description ?? ''} maxLength={60} />
          </div>
        );
      },
    },
    {
      header: 'Status',
      accessorKey: 'status',
      enableSorting: false,
      cell: (info) => {
        const { is_active } = info.row.original;
        return (
          <div className="flex gap-4">
            <Switch
              checked={is_active}
              onChange={() =>
                toggleTradiesHubCategoryStatus({
                  categoryId: info.row.original.id,
                })
              }
              disabled={!module_access?.update || isLoadingtoggle}
              onColor="#E0E3FF"
              onHandleColor="#FF8800"
              offColor="#E8ECF3"
              offHandleColor="#D0D5DD"
              handleDiameter={24}
              uncheckedIcon={false}
              checkedIcon={false}
              boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
              activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
              height={16}
              width={40}
            />
          </div>
        );
      },
    },
    {
      header: 'Action',
      accessorKey: 'action',
      enableSorting: false,
      cell: (info) => (
        <ActionButtons
          deleteIcon
          editIcon
          deleteIconDisabled={!module_access.delete}
          editIconDisabled={!module_access?.update}
          onDelete={() => {
            setSelectedRow(info.row.original);
            setOpenDeleteCategoryItemModal(true);
          }}
          onEdit={() => {
            setSelectedRow({ ...info.row.original });
            setOpenEditFormModal(true);
          }}
        />
      ),
    },
  ];

  const {
    data: categoryDataList,
    isError: categoryDataListError,
    isLoading: categoryDataListIsLoading,
    refetch: refetchCategoryDataList,
    isFetching: categoryDataListIsFetching,
    error: categoryDataListErrorMessage,
  } = useTradiesHubCategoryList({
    ordering:
      sorting.length > 0
        ? `${sorting[0]?.desc === true ? '-' + sorting[0]?.id : sorting[0]?.id}`
        : 'id',
    page: currentPage,
    limit: pageSize,
    search: searchText,
    isSearchable: searchText.length > 0,
  });

  const {
    mutate: deleteCategoryItem,
    error: deleteCategoryItemError,
    isError: isErrorDeleteCategoryItem,
    isSuccess: isSuccessDeleteCategoryItem,
    data: deleteCategoryItemData,
    isLoading: isLoadingDeleteCategoryItem,
  } = useDeleteCategoryRequest({
    categoryId: selectedRow ? selectedRow?.id : 0,
  });

  const {
    mutate: toggleTradiesHubCategoryStatus,
    error: toggleError,
    isError: isErrortoggle,
    data: toggleData,
    isSuccess: isSuccesstoggle,
    isLoading: isLoadingtoggle,
  } = useTradiesHubCategoryStatus();

  const currentData = categoryDataList?.data?.data?.list;

  const onPageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const onPageSizeChange = (newSize: number) => {
    setPageSize(newSize);
    setCurrentPage(1);
  };

  useEffect(() => {
    if (searchText.length > 0) {
      setCurrentPage(1);
    }
  }, [searchText]);

  const debouncedSearchText = useDebounce(searchText, 500); // 500ms debounce

  useEffect(() => {
    refetchCategoryDataList();
  }, [debouncedSearchText]);

  useEffect(() => {
    if (categoryDataListError) {
      addToast('error', categoryDataListErrorMessage as string);
    }

    if (isErrorDeleteCategoryItem) {
      addToast('error', deleteCategoryItemError as string);
    }
  }, [
    categoryDataListError,
    categoryDataListErrorMessage,
    deleteCategoryItemError,
    isErrorDeleteCategoryItem,
  ]);

  useEffect(() => {
    if (isErrortoggle) {
      addToast('error', toggleError as string);
    }
    if (isSuccesstoggle) {
      refetchCategoryDataList();
      addToast(
        'success',
        toggleData?.data?.message || 'File status updated successfully'
      );
    }
  }, [isErrortoggle, toggleError, isSuccesstoggle]);

  useEffect(() => {
    refetchCategoryDataList();
  }, [sorting, pageSize, currentPage]);

  useEffect(() => {
    if (isSuccessDeleteCategoryItem) {
      setSelectedRow(null);
      addToast('success', deleteCategoryItemData?.data?.message);
      if (currentData?.length === 1 && currentPage > 1) {
        setCurrentPage((prev) => prev - 1);
      } else {
        refetchCategoryDataList();
      }
      setOpenDeleteCategoryItemModal(false);
    }
  }, [isSuccessDeleteCategoryItem]);

  return (
    <ContentLayout>
      <ActionBarLayout
        actionbarChildren={
          <React.Fragment>
            <div className="w-auto">
              <InputField
                placeholder="Search name"
                inputContainerClassName="max-h-[46px] w-max "
                className="p-0"
                containerClassName="p-0"
                onChange={(i) => setSearchText(i.target.value)}
                rightIcon={<SearchIcon height={18} width={18} />}
              />
            </div>
            <div>
              <Button
                text={
                  <div className="flex gap-2 px-2 items-center justify-center text-white w-max">
                    <AddIcon height={18} width={18} fill="#fff" /> Add Category
                  </div>
                }
                disabled={!module_access?.write}
                onClick={() => setOpenAddFormModal(true)}
              />
            </div>
          </React.Fragment>
        }
      >
        <DataTable
          data={currentData}
          columns={columns}
          sorting={sorting}
          setSorting={setSorting}
          totalPages={categoryDataList?.data?.data?.total_pages}
          currentPage={currentPage}
          onPageChange={onPageChange}
          pageSize={pageSize}
          setPageSize={(num) => {
            setPageSize(num);
            setCurrentPage(1);
          }}
          pageSizeOptions={[10, 20, 30, 50]}
          onPageSizeChange={onPageSizeChange}
          isPagination={true}
          loading={categoryDataListIsLoading || categoryDataListIsFetching}
        />

        <Modal
          isOpen={openAddFormModal}
          size="xl"
          className="max-w-5xl"
          header={
            <div className="flex flex-col gap-1.5">
              <h3 className="text-xl text-black font-bold">Add Category</h3>
            </div>
          }
          onClose={() => {
            setOpenAddFormModal(false);
            setSelectedRow(null);
          }}
          children={
            <AddCategory
              handleSuccess={() => {
                setOpenAddFormModal(false);
                refetchCategoryDataList();
              }}
            />
          }
        />

        <Modal
          isOpen={openEditFormModal}
          size="xl"
          className="max-w-5xl"
          header={
            <div className="flex flex-col gap-1.5">
              <h3 className="text-xl text-black font-bold">Edit Category</h3>
            </div>
          }
          onClose={() => {
            setOpenEditFormModal(false);
            setSelectedRow(null);
          }}
          children={
            <AddCategory
              isEdit
              handleSuccess={() => {
                setOpenEditFormModal(false);
                refetchCategoryDataList();
              }}
              formData={{
                id: selectedRow?.id ?? 0,
                name: selectedRow?.name ?? '',
                description: selectedRow?.description ?? '',
              }}
            />
          }
        />

        <Modal
          isOpen={openDeleteCategoryItemModal}
          hideCloseIcon
          onClose={() => {
            setOpenDeleteCategoryItemModal(false);
            setSelectedRow(null);
          }}
          children={
            <div className="flex flex-col items-center gap-[36px] ">
              <div className="bg-[#FFD8E0] h-14 w-14 flex justify-center items-center rounded-full">
                <CloseIcon width={18} height={18} fill="#FF3B30" />
              </div>
              <div className="text-black font-bold text-center w-full">
                Are you sure want to delete category?
              </div>
              <div className=" w-full flex justify-center gap-6">
                <Button
                  onClick={deleteCategoryItem}
                  text="Yes"
                  variant="other"
                  className=" border border-error-0
              text-error-0 bg-transparent hover:border-error-0"
                  loading={isLoadingDeleteCategoryItem}
                />
                <Button
                  text="No"
                  variant="outline"
                  disabled={isLoadingDeleteCategoryItem}
                  onClick={() => {
                    setOpenDeleteCategoryItemModal(false);
                    setSelectedRow(null);
                  }}
                />
              </div>
            </div>
          }
        />
      </ActionBarLayout>
    </ContentLayout>
  );
}

export default TradiesHub;
