import { useCallback, useEffect, useState } from 'react';
import { Button, InputField } from '@Components/UI';
import { useForm, Controller, SubmitHandler } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useAddCategory, useEditCategory } from '@Query/Hooks/useTradiesHub';
import { useToast } from '@Components/UI/Toast/ToastProvider';

interface FormData {
  id: number | string;
  name: string;
  description: string;
}

interface AddCategoryProps {
  isEdit?: boolean;
  formData?: FormData;
  handleSuccess?: () => void;
}

interface FormValues {
  name: string;
  description: string;
}

interface ApiError {
  message: string;
  [key: string]: unknown;
}

const schema = yup.object().shape({
  name: yup
    .string()
    .trim()
    .required('Category name is required')
    .min(3, 'Category name must have at least 3 characters')
    .max(100, 'Category name must have at most 100 characters'),
  description: yup
    .string()
    .notRequired()
    .test(
      'description-length',
      'Category description must be between 3 and 100 characters',
      function (value) {
        if (!value || value.trim() === '') return true; // allow empty or null
        const length = value.trim().length;
        return length >= 3 && length <= 100;
      }
    ),
});

const defaultFormData: FormData = {
  id: '',
  name: '',
  description: '',
};

const AddCategory = ({
  isEdit = false,
  formData = defaultFormData,
  handleSuccess = () => null,
}: AddCategoryProps): JSX.Element => {
  const { addToast } = useToast();
  const [hasSubmitted, setHasSubmitted] = useState<boolean>(false);

  const {
    isLoading: editLoading,
    isError: editIsError,
    isSuccess: editIsSuccess,
    error: editError,
    mutate: editCategory,
    data: editData,
    reset: resetEditMutation,
  } = useEditCategory();

  const {
    isLoading: addLoading,
    isError: addIsError,
    isSuccess: addIsSuccess,
    error: addError,
    mutate: addCategory,
    data: addData,
    reset: resetAddMutation,
  } = useAddCategory();

  const isLoading = editLoading || addLoading;

  useEffect(() => {
    return () => {
      resetAddMutation();
      resetEditMutation();
    };
  }, [resetAddMutation, resetEditMutation]);

  useEffect(() => {
    if (!hasSubmitted) return;

    if (addIsSuccess) {
      addToast(
        'success',
        addData?.data?.message || 'Category added successfully'
      );
      handleSuccess();
      setHasSubmitted(false);
      resetAddMutation();
    } else if (addIsError) {
      const errorMessage =
        typeof addError === 'string'
          ? addError
          : (addError as ApiError)?.message || 'Failed to add category';
      addToast('error', errorMessage);
      setHasSubmitted(false);
    }
  }, [
    addData,
    addIsError,
    addError,
    addIsSuccess,
    addToast,
    handleSuccess,
    hasSubmitted,
    resetAddMutation,
  ]);

  // Handle success and error states for edit operation
  useEffect(() => {
    if (!hasSubmitted) return;

    if (editIsSuccess) {
      addToast(
        'success',
        editData?.data?.message || 'Category updated successfully'
      );
      handleSuccess();
      setHasSubmitted(false);
      resetEditMutation();
    } else if (editIsError) {
      const errorMessage =
        typeof editError === 'string'
          ? editError
          : (editError as ApiError)?.message || 'Failed to update category';
      addToast('error', errorMessage);
      setHasSubmitted(false);
    }
  }, [
    editData,
    editIsError,
    editError,
    editIsSuccess,
    addToast,
    handleSuccess,
    hasSubmitted,
    resetEditMutation,
  ]);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<FormValues>({
    resolver: yupResolver(schema) as any,
    defaultValues: {
      name: formData?.name || '',
      description: formData?.description || '',
    },
    mode: 'onChange',
  });

  const onSubmit: SubmitHandler<FormValues> = useCallback(
    (values) => {
      setHasSubmitted(true);
      const requestData = {
        name: values.name.trim(),
        description: values.description.trim(),
      };

      if (isEdit) {
        editCategory({ categoryId: String(formData.id), ...requestData });
      } else {
        addCategory(requestData);
      }
    },
    [addCategory, editCategory, isEdit, formData.id]
  );

  return (
    <form
      className="flex flex-col space-y-4 px-2"
      onSubmit={handleSubmit(onSubmit)}
      autoComplete="off"
    >
      <div className="col-span-3">
        <Controller
          name="name"
          control={control}
          render={({ field }) => (
            <InputField
              label="Category Name"
              field={field}
              placeholder="Name"
              errorMessage={errors.name?.message}
              autoFocus
            />
          )}
        />
      </div>
      <div className="col-span-3">
        <Controller
          name="description"
          control={control}
          render={({ field }) => (
            <InputField
              label="Category Description"
              field={field}
              placeholder="Description"
              errorMessage={errors.description?.message}
            />
          )}
        />
      </div>
      <div className="flex justify-end mt-4">
        <Button
          text={isEdit ? 'Save' : 'Create'}
          width="w-[182px]"
          type="submit"
          loading={isLoading}
          disabled={isLoading}
        />
      </div>
    </form>
  );
};

export default AddCategory;
