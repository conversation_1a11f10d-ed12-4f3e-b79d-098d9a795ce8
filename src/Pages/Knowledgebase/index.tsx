import { UploadIcon, CloseIcon, FileIcon } from '@Icons';
import clsx from 'clsx';
import { useCallback, useRef, useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@Components/UI';
import { useKnowledgebase } from '@Query/Hooks/useKnowledgebase';
import { useToast } from '@Components/UI/Toast/ToastProvider';

import { t } from 'i18next';
import useHasAccess from '@Hooks/useHasAccess';
import { MODULE_KEY } from '@Helpers/Constants';
import ActionBarLayout from '@Components/Layout/ActionBarLayout';
import ContentLayout from '@Components/Layout/ContentLayout';

function Knowledgebase() {
  const module_access = useHasAccess(MODULE_KEY.KNOWLEDGEBASE, ['write']);
  const uploadDocumentsInputRef = useRef<HTMLInputElement | null>(null);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [fileUrl, setFileURL] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  // const MAX_FILE_SIZE_MB = 10;
  const { addToast } = useToast();

  const {
    mutate: knowledgebase,
    isLoading,
    isError,
    error: errorCheck,
    isSuccess,
    data,
  } = useKnowledgebase();

  useEffect(() => {
    if (isError) {
      addToast('error', errorCheck as string);
      handleFileRemove();
    }

    if (isSuccess) {
      addToast('success', data?.data?.message);
      handleFileRemove();
    }
  }, [isError, isSuccess]);

  const handleUploadClick = () => {
    uploadDocumentsInputRef.current?.click();
  };

  const validateFile = (file: File): boolean => {
    if (file.type !== 'application/pdf') {
      setError('Only PDF files are allowed.');
      return false;
    }
    // if (file.size > MAX_FILE_SIZE_MB * 1024 * 1024) {
    //   setError(`File size should not exceed ${MAX_FILE_SIZE_MB}MB.`);
    //   return false;
    // }
    setError(null);
    return true;
  };

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback(() => {
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();
      setIsDragging(false);

      if (selectedFile) return; // Prevent selecting another file

      const file = event.dataTransfer.files[0];

      if (file && validateFile(file)) {
        handleFileUpload({
          target: { files: [file] },
        } as unknown as React.ChangeEvent<HTMLInputElement>);
      }
    },
    [selectedFile]
  );

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && validateFile(file)) {
      setSelectedFile(file);
      setFileURL(URL.createObjectURL(file));
    }
  };

  const handleFileRemove = () => {
    setSelectedFile(null);
    setFileURL('');
    setError(null);
    if (uploadDocumentsInputRef.current) {
      uploadDocumentsInputRef.current.value = ''; // Reset file input
    }
  };

  const handleSubmit = useCallback(() => {
    if (selectedFile) {
      knowledgebase({ pdf_file: selectedFile });
    }
  }, [selectedFile, knowledgebase]);

  return (
    <ContentLayout>
      <ActionBarLayout>
        <div className="flex flex-col items-center space-y-4 mt-20">
          <motion.div
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            onDragLeave={handleDragLeave}
            className={clsx(
              'flex text-[14px] text-secondary text-center items-center flex-col py-28 px-28 border border-dashed w-1/2 group bg-primary-light rounded-[16px] border-primary-100 focus-within:shadow-primary transition-all',
              isDragging && 'border-primary-300'
            )}
            initial={{ scale: 1 }}
            animate={{ scale: isDragging ? 1.05 : 1 }}
            transition={{ type: 'spring', stiffness: 300, damping: 20 }}
          >
            <input
              ref={uploadDocumentsInputRef}
              type="file"
              className="sr-only"
              accept="application/pdf"
              onChange={handleFileUpload}
              multiple={false}
              disabled={!!selectedFile}
            />
            <UploadIcon height={28} width={28} />
            <div className="pt-4">
              {selectedFile ? (
                <span className="text-gray-400">File selected</span>
              ) : (
                <>
                  Drag & Drop or{' '}
                  <span
                    className="text-primary-100 cursor-pointer hover:underline hover:underline-offset-2"
                    onClick={handleUploadClick}
                  >
                    Choose File
                  </span>{' '}
                  to upload PDF
                </>
              )}
            </div>
          </motion.div>

          <AnimatePresence>
            {error && (
              <motion.p
                className="text-red-500 text-sm"
                initial={{ opacity: 0, y: -5 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -5 }}
                transition={{ duration: 0.3 }}
              >
                {error}
              </motion.p>
            )}
          </AnimatePresence>

          <AnimatePresence>
            {selectedFile && (
              <motion.div
                className="mt-4 p-3 rounded-lg bg-gray-100 text-gray-800 flex items-center justify-between w-1/2"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.3 }}
              >
                <div className="flex items-center gap-5">
                  <FileIcon width={18} height={18} />
                  <div>
                    <p className="text-sm font-medium">{selectedFile.name}</p>
                    <p className="text-xs">
                      {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                </div>
                <motion.button
                  onClick={handleFileRemove}
                  className="text-red-500 hover:text-red-700 cursor-pointer"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <CloseIcon height={14} width={14} fill={'#FF8800'} />
                </motion.button>
              </motion.div>
            )}
          </AnimatePresence>
          {selectedFile && (
            <div className="w-1/2">
              <Button
                disabled={!module_access?.write}
                text={t('submit')}
                onClick={handleSubmit}
                loading={isLoading}
              />
            </div>
          )}
        </div>
      </ActionBarLayout>
    </ContentLayout>
  );
}

export default Knowledgebase;
