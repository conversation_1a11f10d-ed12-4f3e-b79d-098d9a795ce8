/* eslint-disable @typescript-eslint/no-explicit-any */
import { getRolesPermissionByRoleName } from '@Query/Services/rolePermission.service';
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

// Async thunk for fetching permissions
export const fetchPermissions = createAsyncThunk(
  'permissions/fetchPermissions',
  async (roleName: string | null, { rejectWithValue }) => {
    try {
      if (!roleName) return null;
      const response = await getRolesPermissionByRoleName(roleName);
      return response.data;
    } catch (error: unknown) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      if (typeof error === 'object' && error !== null && 'response' in error) {
        return rejectWithValue(
          (error as any).response?.data || 'Unknown API error'
        );
      }
      return rejectWithValue('An unexpected error occurred');
    }
  }
);

interface PermissionsState {
  data: any | null;
  loading: boolean;
  error: string | null;
}

const initialState: PermissionsState = {
  data: null,
  loading: false,
  error: null,
};

const permissionsSlice = createSlice({
  name: 'permissions',
  initialState,
  reducers: {},
  extraReducers: (tradie) => {
    tradie
      .addCase(fetchPermissions.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPermissions.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
      })
      .addCase(fetchPermissions.rejected, (state, action) => {
        state.loading = false;
        state.error =
          typeof action.payload === 'string'
            ? action.payload
            : 'An error occurred';
      });
  },
});
type Permission = {
  name: string;
  access: {
    read: boolean;
    write: boolean;
    update: boolean;
    delete: boolean;
  };
};

type Route = {
  name: string;
  path: string;
  permissionKey?: string; // Optional because some routes may not have it
  element: React.ElementType;
  breadcrumb?: { label: string; link?: string }[];
  parent?: string;
  activeHelper?: string[];
};

export const filterAccessibleRoutes = (
  permissions: Permission[] | undefined,
  routes: Route[]
): Route[] => {
  if (!permissions) return routes; // Return all routes if no permissions are available

  // Create a permission map for quick lookup
  const permissionMap = new Map(
    permissions.map((perm) => [perm.name, perm.access.read])
  );

  return routes.filter((route) => {
    // If there's no permissionKey, allow access
    if (!route.permissionKey) return true;

    // Check if the permissionKey exists in permissions and if read is true
    return permissionMap.get(route.permissionKey) ?? false;
  });
};

export default permissionsSlice.reducer;
