import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { createAsyncThunk } from '@reduxjs/toolkit';
import { AxiosError } from 'axios';

const api = new Api();

export interface PostsResponseData {
  userId: number;
  id: number;
  title: string;
  body: string;
}

export interface PostApiResponse {
  data: PostsResponseData[];
}

export interface ErrorResponse {
  message: string;
  status?: number;
}
export const getAllPostViaRedux = createAsyncThunk<
  PostsResponseData[], // The expected return type (array of PostsResponseData)
  void,
  {
    rejectValue: string; // Reject value type
  }
>('getAllPost', async (_, { rejectWithValue }) => {
  try {
    const response = await api.get(API_PATHS.LOGIN); // Make the API request
    return response as unknown as PostsResponseData[]; // Return the data, which is an array of PostsResponseData
  } catch (error) {
    const axiosError = error as AxiosError<ErrorResponse>;
    return rejectWithValue(
      axiosError.response?.data?.message || 'An error occurred'
    );
  }
});
