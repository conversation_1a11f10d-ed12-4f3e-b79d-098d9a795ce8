import CacheKeys from '@Helpers/CacheKeys';
import queryClient from '@Helpers/QueryClient';

import {
  addTrade,
  editTrade,
  getTradeList,
  toggleTradeStatus,
} from '@Query/Services/trade.service';
import { useMutation, useQuery } from 'react-query';

export const useAddTrade = () =>
  useMutation((data: { name: string }) => addTrade({ ...data }));

export const useEditTrade = () =>
  useMutation((data: { name: string; tradeId: string }) =>
    editTrade({ ...data })
  );

export const useTradeList = (props: {
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  search?: string;
}) => {
  return useQuery(CacheKeys.tradeList, () => getTradeList({ ...props }));
};

export const useTradeStatus = () => {
  return useMutation(
    ({ tradeId }: { tradeId: number }) => toggleTradeStatus(tradeId),
    {
      onSuccess: () => queryClient.invalidateQueries('tradeList'),
    }
  );
};
