import CacheKeys from '@Helpers/CacheKeys';
import queryClient from '@Helpers/QueryClient';
import {
  addSubAdmin,
  deleteSubAdminAccount,
  deleteSubAdminUserAccount,
  getRoleList,
  getSubAdminList,
  updateAdminProfile,
  updateSubAdminUserProfile,
  viewAdminProfile,
} from '@Query/Services/admin.service';
import { useMutation, useQuery } from 'react-query';

export const useAddSubAdmin = () =>
  useMutation(
    (data: {
      first_name: string;
      last_name: string;
      email: string;
      role_id: number;
    }) => addSubAdmin(data),
    { onSuccess: () => queryClient.invalidateQueries(CacheKeys.subadminlist) }
  );

export const useGetSubAdminList = (props: {
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  field?: string;
  search?: string;
}) =>
  useQuery(CacheKeys.subadminlist, () =>
    getSubAdminList({
      ...props,
    })
  );

export const useGetRoleList = () => useQuery(CacheKeys.rolelist, getRoleList);

export const useDeleteSubAdminAccount = () =>
  useMutation(deleteSubAdminAccount);

export const useDeleteSubAdminUserAccount = ({ userId }: { userId: number }) =>
  useMutation(() => deleteSubAdminUserAccount({ userId }));

export const useGetAdminProfile = () => {
  const isLoggedIn = !!localStorage.getItem('authaccess'); // Check if user is logged in

  return useQuery(CacheKeys.profileData, viewAdminProfile, {
    enabled: isLoggedIn,
    staleTime: 1000 * 60 * 5,
    refetchOnWindowFocus: false,
  });
};

export const useUpdateAdminProfile = () =>
  useMutation(
    (data: {
      first_name?: string;
      last_name?: string;
      old_password?: string;
      new_password?: string;
      confirm_password?: string;
    }) => updateAdminProfile(data),
    {
      onSuccess: () => queryClient.invalidateQueries(CacheKeys.profileData),
    }
  );

export const useUpdateSubAdminUserProfile = () =>
  useMutation(
    (data: {
      id?: number;
      first_name?: string;
      last_name?: string;
      role: number | string;
    }) => updateSubAdminUserProfile(data)
  );
