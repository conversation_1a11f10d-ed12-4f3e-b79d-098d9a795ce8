import CacheKeys from '@Helpers/CacheKeys';
import {
  deleteStaticPages,
  getStaticPages,
  updateStaticPage,
} from '@Query/Services/staticPage.service';
import { useMutation, useQuery } from 'react-query';

export const useGetStaticPages = (props: {
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  field?: string;
  search?: string;
}) => useQuery(CacheKeys.staticPages, () => getStaticPages({ ...props }));

export const useDeleteStaticPage = ({ listItemId }: { listItemId: number }) =>
  useMutation(() => deleteStaticPages({ listItemId }));

export const useUpdateStaticPage = () =>
  useMutation(
    (props: {
      listId: number;
      title?: string;
      slug?: string;
      content?: string;
      status?: string;
    }) => updateStaticPage({ ...props })
  );
