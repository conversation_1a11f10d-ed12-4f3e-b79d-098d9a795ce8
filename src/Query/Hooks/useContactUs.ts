import CacheKeys from '@Helpers/CacheKeys';
import {
  deleteContactUsRequest,
  getContactUs,
} from '@Query/Services/contactUs.service';
import { useMutation, useQuery } from 'react-query';

export const useGetContactUs = (props: {
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  field?: string;
  search?: string;
}) => useQuery(CacheKeys.contactUsData, () => getContactUs({ ...props }));

export const useDeleteContactUsListItem = ({
  listItemId,
}: {
  listItemId: number;
}) => useMutation(() => deleteContactUsRequest({ listItemId }));
