import CacheKeys from '@Helpers/CacheKeys';
import {
  editTemplate,
  getEmailTemplatesList,
  getNotificationType,
  sendNotification,
} from '@Query/Services/notification.service';
import { useMutation, useQuery } from 'react-query';

export const useGetEmailTemplatesList = (props: {
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  field?: string;
  search?: string;
  platform?: string;
}) =>
  useQuery(CacheKeys.emailTemplates, () =>
    getEmailTemplatesList({
      ...props,
    })
  );

export const useEditTemplate = () => useMutation(editTemplate);

export const useSendNotification = () => useMutation(sendNotification);

export const useGetNotificationType = () =>
  useQuery([], () => getNotificationType());
