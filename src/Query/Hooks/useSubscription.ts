import CacheKeys from '@Helpers/CacheKeys';
import {
  getSubscriptionList,
  editSubscription,
} from '@Query/Services/subscription.service';
import { useMutation, useQuery } from 'react-query';

export const useGetSubscriptionList = () =>
  useQuery(CacheKeys.subcriptionList, () => getSubscriptionList());

export const useEditSubscription = () =>
  useMutation(
    (data: {
      subscription_id: number;
      features: {
        feature_id: number;
        action: boolean;
      }[];
    }) => editSubscription({ ...data })
  );
