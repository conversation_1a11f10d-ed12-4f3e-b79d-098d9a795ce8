import {
  getCategoryWiseIssues,
  getCategoryWiseQuestionAsked,
  getListChats,
  getListChatView,
  getListIssue,
  getListIssueView,
  getOrgAnalytics,
  getRecIssues,
  getRectificationAnalytics,
  getStateWiseQuestionAsked,
  getStateWiseUploadDocs,
  getUserAnalytics,
} from '@Query/Services/analytics.service';
import { useQuery } from 'react-query';

export const useGetUserAnalytics = (data: string) =>
  useQuery(['graph1'], () => getUserAnalytics(data));

export const useGetOrgAnalytics = (data: string) =>
  useQuery(['graph2'], () => getOrgAnalytics(data));

export const useGetStateWiseUploadDocs = (data: string) =>
  useQuery(['graph3'], () => getStateWiseUploadDocs(data));

export const useGetStateWiseQuestionAsked = (data: string) =>
  useQuery(['graph4'], () => getStateWiseQuestionAsked(data));

export const useGetCategoryWiseQuestionAsked = (data: string) =>
  useQuery(['graph5'], () => getCategoryWiseQuestionAsked(data));

export const useGetCategoryWiseIssues = (data: string) =>
  useQuery(['graph6'], () => getCategoryWiseIssues(data));

export const useRectificationAnalytics = (data: string) =>
  useQuery(['graph7'], () => getRectificationAnalytics(data));

export const useGetChatList = (data: string) =>
  useQuery(['chatlist'], () => getListChats(data), {
    enabled: data ? true : false,
  });

export const useGetRecChatList = (data: string) =>
  useQuery(['recchatlist'], () => getRecIssues(data), {
    enabled: data ? true : false,
  });

export const useGetChatView = (data: string, enabled: boolean) =>
  useQuery(['chatview'], () => getListChatView(data), { enabled: enabled });

export const useGetIssueList = (data: string) =>
  useQuery(['issuelist'], () => getListIssue(data));

export const useGetViewIssue = (data: string, enabled: boolean) =>
  useQuery(['issueview'], () => getListIssueView(data), {
    enabled: enabled,
  });
