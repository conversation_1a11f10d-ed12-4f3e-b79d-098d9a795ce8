import CacheKeys from '@Helpers/CacheKeys';
import {
  getLicenceApprovalRequests,
  licenceApproveActions,
} from '@Query/Services/licenceApproval.service';
import { useMutation, useQuery } from 'react-query';

export const useLicenceApproveAction = () =>
  useMutation((data: { requestId: number; actionType: 'approve' | 'reject' }) =>
    licenceApproveActions(data)
  );

export const useGetLicenceApproveList = (props: {
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  field?: string;
  search?: string;
  status?: string;
  isEnableStatusFilter?: boolean;
}) =>
  useQuery(CacheKeys.licenceApprovalList, () =>
    getLicenceApprovalRequests({ ...props })
  );
