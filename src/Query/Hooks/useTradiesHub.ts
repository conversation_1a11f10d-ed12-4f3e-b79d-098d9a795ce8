import CacheKeys from '@Helpers/CacheKeys';
import queryClient from '@Helpers/QueryClient';
import {
  deleteCategoryRequest,
  deleteContentRequest,
  deleteReportRequest,
  getTradiesHubCategoryList,
  toggleTradiesHubCategoryStatus,
  toggleTradiesHubContentStatus,
  addCategory,
  editCategory,
  getTradiesHubPostList,
  getTradiesHubCommentList,
  getTradiesHubReportList,
  getTradiesHubReplyList,
  getTradiesHubPostReportList,
  getTradiesHubCommentReportList,
  getTradiesHubReplyReportList,
} from '@Query/Services/tradiesHub.service';
import { useMutation, useQuery } from 'react-query';

export const useTradiesHubCategoryList = (props: {
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  search?: string;
}) => {
  return useQuery(CacheKeys.tradiesHubCategoriesList, () =>
    getTradiesHubCategoryList({ ...props })
  );
};

export const useDeleteContentRequest = ({
  id,
  type,
}: {
  id: number;
  type: string;
}) => useMutation(() => deleteContentRequest({ id, type }));

export const useDeleteReportRequest = ({
  id,
  action_type,
}: {
  id: number;
  action_type: string;
}) => useMutation(() => deleteReportRequest({ id, action_type }));

export const useDeleteCategoryRequest = ({
  categoryId,
}: {
  categoryId: number;
}) => useMutation(() => deleteCategoryRequest({ categoryId }));

export const useTradiesHubCategoryStatus = () => {
  return useMutation(
    ({ categoryId }: { categoryId: number }) =>
      toggleTradiesHubCategoryStatus(categoryId),
    {
      onSuccess: () =>
        queryClient.invalidateQueries('tradiesHubCategoriesList'),
    }
  );
};

export const useTradiesHubContentStatus = () => {
  return useMutation(
    ({ id, type }: { id: number; type: string }) =>
      toggleTradiesHubContentStatus(id, type),
    {
      onSuccess: () => queryClient.invalidateQueries('tradiesHubPostList'),
    }
  );
};

export const useAddCategory = () =>
  useMutation((data: { name: string; description: string }) =>
    addCategory({ ...data })
  );

export const useEditCategory = () =>
  useMutation(
    (data: { name: string; description: string; categoryId: string }) =>
      editCategory({ ...data })
  );

export const useTradiesHubPostList = (props: {
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  search?: string;
  status?: string;
  isEnableStatusFilter?: boolean;
}) => {
  return useQuery(CacheKeys.tradiesHubPostList, () =>
    getTradiesHubPostList({ ...props })
  );
};

export const useTradiesHubCommentList = (props: {
  id: number;
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  search?: string;
  enabled: boolean;
}) => {
  return useQuery(
    CacheKeys.tradiesHubCommentList,
    () => getTradiesHubCommentList({ ...props }),
    {
      enabled: props.enabled,
    }
  );
};

export const useTradiesHubReplyList = (props: {
  id: number;
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  search?: string;
  enabled: boolean;
}) => {
  return useQuery(
    CacheKeys.tradiesHubCommentList,
    () => getTradiesHubReplyList({ ...props }),
    {
      enabled: props.enabled,
    }
  );
};

export const useTradiesHubReportList = (props: {
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  search?: string;
}) => {
  return useQuery(CacheKeys.tradiesHubReportList, () =>
    getTradiesHubReportList({ ...props })
  );
};

export const useTradiesHubPostReportList = (props: {
  id: number;
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  search?: string;
  enabled: boolean;
}) => {
  return useQuery(
    CacheKeys.tradiesHubCommentList,
    () => getTradiesHubPostReportList({ ...props }),
    {
      enabled: props.enabled,
    }
  );
};

export const useTradiesHubCommentReportList = (props: {
  id: number;
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  search?: string;
  enabled: boolean;
}) => {
  return useQuery(
    CacheKeys.tradiesHubCommentReportList,
    () => getTradiesHubCommentReportList({ ...props }),
    {
      enabled: props.enabled,
    }
  );
};

export const useTradiesHubReplyReportList = (props: {
  id: number;
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  search?: string;
  enabled: boolean;
}) => {
  return useQuery(
    CacheKeys.tradiesHubReplyReportList,
    () => getTradiesHubReplyReportList({ ...props }),
    {
      enabled: props.enabled,
    }
  );
};
