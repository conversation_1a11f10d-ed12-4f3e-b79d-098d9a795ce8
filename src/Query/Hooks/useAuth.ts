import {
  adminLogin,
  adminLogout,
  newRefreshToken,
  resetPasswordRequest,
  sendForgotPasswordMail,
} from '@Query/Services/auth.service';
import { useMutation } from 'react-query';

export const useLogin = () =>
  useMutation((data: { email: string; password: string }) => adminLogin(data));

export const useLogout = () => useMutation(adminLogout);

export const useSendForgotPasswordMail = () =>
  useMutation((data: { email: string }) => sendForgotPasswordMail(data));

export const useResetPassword = () =>
  useMutation(
    (data: {
      encrypted_token: string;
      new_password: string;
      confirm_password: string;
    }) => resetPasswordRequest(data)
  );

export const useNewRefreshToken = () => useMutation(newRefreshToken);
