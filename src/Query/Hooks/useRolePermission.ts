import CacheKeys from '@Helpers/CacheKeys';
import {
  addRolePermission,
  deleteRole,
  getAppRolePermissions,
  getRolePermissions,
  getRolePermissionsById,
  getRolesPermissionByRoleName,
  getUserCountOfRole,
  updateRolePermissionsById,
} from '@Query/Services/rolePermission.service';
import { useMutation, useQuery } from 'react-query';

interface featuresType {
  id?: number;
  name: string;
  access: {
    read: boolean;
    write: boolean;
    update: boolean;
    delete: boolean;
  };
}

export const useGetRolePermission = () =>
  useQuery(CacheKeys.rolePermissionList, getRolePermissions);

export const useGetAppRolePermission = () =>
  useQuery(CacheKeys.approlePermissionList, getAppRolePermissions);

export const useSaveRolePermission = () =>
  useMutation((data: { name: string; features: featuresType[] }) =>
    addRolePermission({ ...data })
  );

export const useGetRolePermissionById = (listId: number, enabled: boolean) =>
  useQuery([], () => getRolePermissionsById(listId), { enabled: enabled });

export const useUpdateRolePermissionById = () =>
  useMutation(
    (data: { listId: number; name: string; features: featuresType[] }) =>
      updateRolePermissionsById({ ...data })
  );

export const useGetRolesPermissionByRoleName = (
  roleName: string,
  options?: { enabled?: boolean }
) =>
  useQuery(
    [CacheKeys.permissions, roleName], // Cache by roleName to prevent duplicate queries
    () => getRolesPermissionByRoleName(roleName),
    {
      enabled:
        (localStorage.getItem('authaccess') ? true : false) &&
        (options?.enabled ?? true),
    }
  );

export const useGetUserCountByRole = (roleId: number, enabled: boolean) =>
  useQuery([CacheKeys.userCounts, roleId], () => getUserCountOfRole(roleId), {
    enabled: enabled && !!roleId, // Ensures roleId is valid before fetching
  });

export const useDeleteRole = () =>
  useMutation((roleId: number) => deleteRole({ roleId }));
