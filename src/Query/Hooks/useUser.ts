/* eslint-disable @typescript-eslint/no-explicit-any */
import CacheKeys from '@Helpers/CacheKeys';
import { useQuery, useMutation, UseMutationOptions } from 'react-query';
import queryClient from '@Helpers/QueryClient';
import {
  getUserList,
  getUserDetails,
  toggleUserStatus,
  deleteUser,
  updateUser,
  createUser,
  getPurchaseHistory,
  exportCsv,
  importCsv,
  getStateList,
  getTradeList,
  sampleCsv,
} from '@Query/Services/user.service';

// Fetch user list
export const useGetUserList = (params: {
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  search?: string;
}) => {
  return useQuery(['userList', params], () => getUserList(params));
};

// Toggle user status
export const useToggleUserStatus = () => {
  return useMutation(
    ({ userId }: { userId: number }) => toggleUserStatus(userId),
    {
      onSuccess: () => queryClient.invalidateQueries('userList'),
    }
  );
};

// Delete user
export const useDeleteUser = ({ userId }: { userId: number }) =>
  useMutation(() => deleteUser(userId), {
    onSuccess: () => queryClient.invalidateQueries('userList'),
  });

// Get user details
export const useGetUserDetails = (onSuccessCallback?: (data: any) => void) => {
  return useMutation(
    (userId: number) => getUserDetails(userId), // API Call
    {
      onSuccess: (data) => {
        if (onSuccessCallback) {
          onSuccessCallback(data);
        }
      },
    }
  );
};

// Update user
export const useUpdateUser = () => {
  return useMutation(
    (data: { id: number } & Record<string, unknown>) => updateUser(data),
    {
      onSuccess: () => queryClient.invalidateQueries('userList'),
    }
  );
};

// Create new user
export const useCreateUser = (isEdit: boolean) => {
  return useMutation(
    (data: Record<string, unknown>) =>
      isEdit
        ? updateUser(data as { id: number } & Record<string, unknown>)
        : createUser(data),
    {
      onSuccess: () => queryClient.invalidateQueries('userList'),
    }
  );
};

// Get Purchase history

export const useGetPurchaseHistory = (
  onSuccessCallback?: (data: any) => void
) => {
  return useMutation(
    (userId: number) => getPurchaseHistory(userId), // API Call
    {
      onSuccess: (data) => {
        if (onSuccessCallback) {
          onSuccessCallback(data);
        }
      },
    }
  );
};

//export csv
export const useExportCsv = () => {
  return useMutation(() => exportCsv());
};

export const useSampleCsv = () => {
  return useMutation(() => sampleCsv());
};

//import csv
export const useImportCsv = (
  options?: UseMutationOptions<any, any, FormData>
) => {
  return useMutation((formData: FormData) => importCsv(formData), {
    ...options,
  });
};

export const useStateList = (enabled = true) =>
  useQuery({
    queryKey: [CacheKeys.stateList],
    queryFn: getStateList,
    enabled,
  });

export const useTradeList = (enabled = true) => useQuery(CacheKeys.tradeList, getTradeList);

