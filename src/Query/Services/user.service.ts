import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { AxiosResponse } from 'axios';

const api = new Api();

export const getUserList = async (params: {
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  search?: string;
}): Promise<AxiosResponse> => {
  const searchQuery =
    params.isSearchable && params.search ? `&search=${params.search}` : '';
  return api.get(
    `${API_PATHS.USER_LIST}?ordering=${params.ordering}&page=${params.page}&page_size=${params.limit}${searchQuery}`
  );
};

export const getUserDetails = async (
  userId: number
): Promise<AxiosResponse> => {
  return api.get(API_PATHS.EDIT_USER.replace(':id', String(userId)));
};

export const toggleUserStatus = async (
  userId: number
): Promise<AxiosResponse> => {
  return api.patch(API_PATHS.TOGGLE_USER_STATUS.replace(':id', String(userId)));
};

export const deleteUser = async (userId: number): Promise<AxiosResponse> => {
  return api.delete(API_PATHS.DELETE_USER.replace(':id', String(userId)));
};

export const updateUser = async (
  data: { id: number } & Record<string, unknown>
): Promise<AxiosResponse> => {
  return api.put(API_PATHS.EDIT_USER.replace(':id', String(data.id)), { data });
};

export const createUser = async (
  data: Record<string, unknown>
): Promise<AxiosResponse> => {
  return api.post(API_PATHS.CREATE_USER, { data });
};

export const getPurchaseHistory = async (
  userId: number
): Promise<AxiosResponse> => {
  return api.get(API_PATHS.PURCHASE_HISTORY.replace(':id', String(userId)));
};

export const exportCsv = async (): Promise<AxiosResponse> => {
  return api.get(API_PATHS.EXPORT_CSV, { skipErrorHandling: true });
};
export const sampleCsv = async (): Promise<AxiosResponse> => {
  return api.get(API_PATHS.SAMPLE_CSV, { skipErrorHandling: true });
};

export const importCsv = async (formData: FormData): Promise<AxiosResponse> => {
  return api.post(API_PATHS.IMPORT_CSV, { data: formData, isMultipart: true });
};
export const getStateList = async (): Promise<AxiosResponse> => {
  return api.get(API_PATHS.STATES_LIST + `1/states/`);
};

export const getTradeList = async (): Promise<AxiosResponse> => {
  return api.get(API_PATHS.TRADTE_LIST);
};
