import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { AxiosResponse } from 'axios';

const api = new Api();

export const getTradiesHubCategoryList = async ({
  ordering,
  page,
  limit,
  isSearchable = false,
  search,
}: {
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  search?: string;
}): Promise<AxiosResponse> => {
  return api.get(
    API_PATHS.FETCH_TRADIES_HUB_CATEGORIES_LIST +
      `?ordering=${ordering}&page=${page}&page_size=${limit}${isSearchable ? `&search=${search}` : ''}`
  );
};

export const deleteCategoryRequest = async ({
  categoryId,
}: {
  categoryId: number;
}): Promise<AxiosResponse> => {
  return api.delete(
    API_PATHS.TRADIES_HUB_CATEGORY_DELETE.replace(':id', String(categoryId))
  );
};

export const deleteContentRequest = async ({
  id,
  type,
}: {
  id: number;
  type: string;
}): Promise<AxiosResponse> => {
  return api.delete(API_PATHS.TRADIES_HUB_DELETE, {
    data: {
      id,
      type,
    },
  });
};

export const deleteReportRequest = async ({
  id,
  action_type,
}: {
  id: number;
  action_type: string;
}): Promise<AxiosResponse> => {
  return api.post(
    API_PATHS.TRADIES_HUB_REPORT_DELETE.replace(':id', String(id)),
    {
      data: {
        action_type,
      },
    }
  );
};

export const toggleTradiesHubCategoryStatus = async (
  categoryId: number
): Promise<AxiosResponse> => {
  return api.patch(
    API_PATHS.TOGGLE_TRADIES_HUB_CATEGORY.replace(':id', String(categoryId))
  );
};

export const toggleTradiesHubContentStatus = async (
  id: number,
  type: string
): Promise<AxiosResponse> => {
  return api.patch(API_PATHS.TOGGLE_TRADIES_HUB_CONTENT, {
    data: {
      id,
      type,
    },
  });
};

export const addCategory = async (data: {
  name: string;
  description: string;
}): Promise<AxiosResponse> => {
  const formData = new FormData();
  formData.append('name', data.name);
  formData.append('description', data.description);
  return api.post(API_PATHS.TRADIES_HUB_CATEGORY_CREATE, {
    data: formData as unknown as Record<string, unknown>,
  });
};

export const editCategory = async (data: {
  categoryId: string;
  name: string;
  description: string;
}): Promise<AxiosResponse> => {
  const formData = new FormData();
  formData.append('name', data.name);
  formData.append('description', data.description);
  return api.put(
    API_PATHS.TRADIES_HUB_CATEGORY_UPDATE.replace(
      ':id',
      String(data.categoryId)
    ),
    {
      data: formData as unknown as Record<string, unknown>,
    }
  );
};

export const getTradiesHubPostList = async ({
  ordering,
  page,
  limit,
  isSearchable = false,
  search,
  status,
  isEnableStatusFilter = false,
}: {
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  search?: string;
  status?: string;
  isEnableStatusFilter?: boolean;
}): Promise<AxiosResponse> => {
  return api.get(
    API_PATHS.TRADIES_HUB_POST_LIST +
      `?ordering=${ordering}&page=${page}&page_size=${limit}${isSearchable ? `&search=${search}` : ''}${isEnableStatusFilter ? `&category_ids=${status}` : ''}`
  );
};

export const getTradiesHubCommentList = async ({
  id,
  ordering,
  page,
  limit,
  isSearchable = false,
  search,
}: {
  id: number;
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  search?: string;
}): Promise<AxiosResponse> => {
  return api.get(
    API_PATHS.TRADIES_HUB_COMMENT_LIST.replace(':id', String(id)) +
      `?ordering=${ordering}&page=${page}&page_size=${limit}${isSearchable ? `&search=${search}` : ''}`
  );
};

export const getTradiesHubReplyList = async ({
  id,
  ordering,
  page,
  limit,
  isSearchable = false,
  search,
}: {
  id: number;
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  search?: string;
}): Promise<AxiosResponse> => {
  return api.get(
    API_PATHS.TRADIES_HUB_REPLY_LIST.replace(':id', String(id)) +
      `?ordering=${ordering}&page=${page}&page_size=${limit}${isSearchable ? `&search=${search}` : ''}`
  );
};

export const getTradiesHubReportList = async ({
  ordering,
  page,
  limit,
  isSearchable = false,
  search,
}: {
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  search?: string;
}): Promise<AxiosResponse> => {
  return api.get(
    API_PATHS.TRADIES_HUB_REPORT_LIST +
      `?ordering=${ordering}&page=${page}&page_size=${limit}${isSearchable ? `&search=${search}` : ''}`
  );
};

export const getTradiesHubPostReportList = async ({
  id,
  ordering,
  page,
  limit,
  isSearchable = false,
  search,
}: {
  id: number;
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  search?: string;
}): Promise<AxiosResponse> => {
  return api.get(
    API_PATHS.TRADIES_HUB_POST_REPORT_LIST.replace(':id', String(id)) +
      `?ordering=${ordering}&page=${page}&page_size=${limit}${isSearchable ? `&search=${search}` : ''}`
  );
};

export const getTradiesHubCommentReportList = async ({
  id,
  ordering,
  page,
  limit,
  isSearchable = false,
  search,
}: {
  id: number;
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  search?: string;
}): Promise<AxiosResponse> => {
  return api.get(
    API_PATHS.TRADIES_HUB_COMMENT_REPORT_LIST.replace(':id', String(id)) +
      `?ordering=${ordering}&page=${page}&page_size=${limit}${isSearchable ? `&search=${search}` : ''}`
  );
};

export const getTradiesHubReplyReportList = async ({
  id,
  ordering,
  page,
  limit,
  isSearchable = false,
  search,
}: {
  id: number;
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  search?: string;
}): Promise<AxiosResponse> => {
  return api.get(
    API_PATHS.TRADIES_HUB_REPLY_REPORT_LIST.replace(':id', String(id)) +
      `?ordering=${ordering}&page=${page}&page_size=${limit}${isSearchable ? `&search=${search}` : ''}`
  );
};
