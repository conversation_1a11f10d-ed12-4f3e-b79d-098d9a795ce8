import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { AxiosResponse } from 'axios';

const api = new Api();

export const getContactUs = async ({
  ordering,
  page,
  limit,
  isSearchable = false,
  field,
  search,
}: {
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  field?: string;
  search?: string;
}): Promise<AxiosResponse> => {
  return api.get(
    API_PATHS.CONTACT_US +
      `?ordering=${ordering}&page=${page}&page_size=${limit}${isSearchable ? `&${field}=${search}` : ''}`
  );
};

export const deleteContactUsRequest = async ({
  listItemId,
}: {
  listItemId: number;
}): Promise<AxiosResponse> => {
  return api.delete(API_PATHS.CONTACT_US + `${listItemId}/`);
};
