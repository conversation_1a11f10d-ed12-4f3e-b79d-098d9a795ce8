import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { AxiosResponse } from 'axios';

const api = new Api();

export const getLicenceApprovalRequests = async ({
  ordering,
  page,
  limit,
  isSearchable = false,
  search,
  status,
  isEnableStatusFilter,
}: {
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  search?: string;
  status?: string;
  isEnableStatusFilter?: boolean;
}): Promise<AxiosResponse> => {
  return api.get(
    API_PATHS.LICENCE_APPROVAL_REQUESTS +
      `?ordering=${ordering}&page=${page}&page_size=${limit}${isSearchable ? `&search=${search}` : ''}${isEnableStatusFilter ? `&status=${status}` : ''}`
  );
};

export const licenceApproveActions = async (data: {
  requestId: number;
  actionType: 'approve' | 'reject';
}): Promise<AxiosResponse> => {
  return api.post(
    API_PATHS.LICENCE_APPROVAL_ACTIONS + `${data.requestId}/${data.actionType}/`
  );
};
