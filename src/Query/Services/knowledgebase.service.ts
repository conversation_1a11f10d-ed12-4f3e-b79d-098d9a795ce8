import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { AxiosResponse } from 'axios';

const api = new Api();

export const knowledgebaseApi = async (data: {
  pdf_file: File;
}): Promise<AxiosResponse> => {
  const formData = new FormData();
  formData.append('pdf_file', data.pdf_file);

  return api.post(API_PATHS.KNOWLEDGEBASE_UPLOAD_API, {
    data: formData as unknown as Record<string, unknown>,
    isMultipart: true,
  });
};
