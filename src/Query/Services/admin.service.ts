import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { AxiosResponse } from 'axios';

const api = new Api();

export const addSubAdmin = async (data: {
  first_name: string;
  last_name: string;
  email: string;
  role_id: number;
}): Promise<AxiosResponse> => {
  return api.post(API_PATHS.ADDSUBADMIN, { data });
};

export const getSubAdminList = async ({
  ordering,
  page,
  limit,
  isSearchable = false,
  search,
}: {
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  search?: string;
}): Promise<AxiosResponse> => {
  return api.get(
    API_PATHS.SUBADMIN_LIST +
      `?ordering=${ordering}&page=${page}&page_size=${limit}${isSearchable ? `&search=${search}` : ''}`
  );
};

export const getRoleList = async (): Promise<AxiosResponse> => {
  return api.get(API_PATHS.ROLE_LIST);
};

export const deleteSubAdminAccount = async (): Promise<AxiosResponse> => {
  return api.delete(API_PATHS.OWN_ACC_DELETE);
};

export const deleteSubAdminUserAccount = async ({
  userId,
}: {
  userId: number;
}): Promise<AxiosResponse> => {
  return api.delete(API_PATHS.DELETE_SUBADMIN + `${userId}/`);
};

export const updateAdminProfile = async (data: {
  first_name?: string;
  last_name?: string;
  old_password?: string;
  new_password?: string;
  confirm_password?: string;
}): Promise<AxiosResponse> => {
  return api.put(API_PATHS.UPDATE_ADMIN_PROFILE, { data });
};

export const updateSubAdminUserProfile = async (data: {
  id?: number;
  first_name?: string;
  last_name?: string;
  role?: string | number;
}): Promise<AxiosResponse> => {
  return api.put(API_PATHS.UPDATE_ADMIN_PROFILE, { data });
};

export const viewAdminProfile = async (): Promise<AxiosResponse> => {
  return api.get(API_PATHS.ADMIN_PROFILE);
};
