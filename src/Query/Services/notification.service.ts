import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { AxiosResponse } from 'axios';

const api = new Api();

export const getEmailTemplatesList = async ({
  ordering,
  page,
  limit,
  isSearchable = false,
  search,
  platform,
}: {
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  search?: string;
  platform?: string;
}): Promise<AxiosResponse> => {
  return api.get(
    API_PATHS.GET_NOTIFICATION_TEMPLATE +
      `?ordering=${ordering}&page=${page}&page_size=${limit}${platform ? `&platform_type=${platform}` : ''}${isSearchable ? `&search=${search}` : ''}`
  );
};

export const editTemplate = async (data: any): Promise<AxiosResponse> => {
  return api.put(API_PATHS.EDIT_NOTIFICATION_TEMPLATE + `${data.id}/`, {
    data,
  });
};

export const sendNotification = async (data: any): Promise<AxiosResponse> => {
  return api.post(API_PATHS.SEND_NOTIFICATION, { data });
};

export const getNotificationType = async (): Promise<AxiosResponse> => {
  return api.get(API_PATHS.GET_NOTIFICATION_TYPE);
};
