import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { AxiosResponse } from 'axios';

const api = new Api();

export const getRolePermissions = async (): Promise<AxiosResponse> => {
  return api.get(API_PATHS.FEATURES + '?is_cms_feature=True');
};

export const getAppRolePermissions = async (): Promise<AxiosResponse> => {
  return api.get(API_PATHS.FEATURES + '?is_cms_feature=False');
};

export const getRolePermissionsById = async (
  listId: number
): Promise<AxiosResponse> => {
  return api.get(API_PATHS.ROLE_BY_ID + `${listId}/`);
};

interface featuresType {
  name: string;
  access: {
    read: boolean;
    write: boolean;
    update: boolean;
    delete: boolean;
  };
}

export const updateRolePermissionsById = async (data: {
  listId: number;
  name: string;
  features: featuresType[];
}): Promise<AxiosResponse> => {
  return api.patch(API_PATHS.ROLE_BY_ID + `${data.listId}/`, { data });
};

export const addRolePermission = async (data: {
  name: string;
  features: featuresType[];
}): Promise<AxiosResponse> => {
  return api.post(API_PATHS.ROLE_FEATURE_MAPPING, { data });
};

export const getRolesPermissionByRoleName = async (
  rolenamne: string
): Promise<AxiosResponse> => {
  return api.get(API_PATHS.ROLE_BY_ID + `${rolenamne}/`);
};

export const getUserCountOfRole = async (
  roleId: number
): Promise<AxiosResponse> => {
  return api.get(API_PATHS.USER_COUNT_BY_ROLE + `${roleId}/`);
};

export const deleteRole = async ({
  roleId,
}: {
  roleId: number;
}): Promise<AxiosResponse> => {
  return api.delete(API_PATHS.DELETE_ROLE + `${roleId}/`);
};
