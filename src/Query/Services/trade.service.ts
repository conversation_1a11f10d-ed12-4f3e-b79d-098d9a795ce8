import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { AxiosResponse } from 'axios';

const api = new Api();

export const addTrade = async (data: {
  name: string;
}): Promise<AxiosResponse> => {
  const formData = new FormData();
  formData.append('name', data.name);
  return api.post(API_PATHS.ADD_TRADE, {
    data: formData as unknown as Record<string, unknown>,
  });
};

export const editTrade = async (data: {
  tradeId: string;
  name: string;
}): Promise<AxiosResponse> => {
  const formData = new FormData();
  formData.append('name', data.name);
  return api.put(API_PATHS.EDIT_TRADE.replace(':id', String(data.tradeId)), {
    data: formData as unknown as Record<string, unknown>,
  });
};

export const getTradeList = async ({
  ordering,
  page,
  limit,
  isSearchable = false,
  search,
}: {
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  search?: string;
}): Promise<AxiosResponse> => {
  return api.get(
    API_PATHS.TRADE_LIST +
      `?ordering=${ordering}&page=${page}&page_size=${limit}${isSearchable ? `&search=${search}` : ''}`
  );
};

export const toggleTradeStatus = async (
  tradeId: number
): Promise<AxiosResponse> => {
  return api.patch(
    API_PATHS.TRADE_TOGGLE_STATUS.replace(':id', String(tradeId))
  );
};
