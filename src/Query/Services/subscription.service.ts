import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { AxiosResponse } from 'axios';

const api = new Api();

export const getSubscriptionList = async (): Promise<AxiosResponse> => {
  return api.get(API_PATHS.SUBCRIPTION_LIST);
};

export const editSubscription = async (data: {
  subscription_id: number;
  features: {
    feature_id: number;
    action: boolean;
  }[];
}): Promise<AxiosResponse> => {
  return api.post(API_PATHS.SUBCRIPTION_EDIT, {
    data: data as unknown as Record<string, unknown>,
  });
};
