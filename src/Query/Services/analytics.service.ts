import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { AxiosResponse } from 'axios';

const api = new Api();

export const getUserAnalytics = async (data: any): Promise<AxiosResponse> => {
  //Data => year,month,week, custom->start_date, end_date
  return api.get(API_PATHS.USER_ANALYTICS + data);
};

export const getOrgAnalytics = async (data: any): Promise<AxiosResponse> => {
  //Data => year,month,week, custom->start_date, end_date
  return api.get(API_PATHS.USER_ANALYTICS + data);
};

export const getStateWiseUploadDocs = async (
  data: any
): Promise<AxiosResponse> => {
  //Data => year,month,week, custom->start_date, end_date
  return api.get(API_PATHS.STATE_WISE_UPLOAD_DOCS + data);
};

export const getStateWiseQuestionAsked = async (
  data: any
): Promise<AxiosResponse> => {
  //Data => year,month,week, custom->start_date, end_date
  return api.get(API_PATHS.STATE_WISE_QUESTIONS_ASKED + data);
};

export const getCategoryWiseQuestionAsked = async (
  data: any
): Promise<AxiosResponse> => {
  //Data => year,month,week, custom->start_date, end_date
  return api.get(API_PATHS.CATEGORY_WISE_QUESTIONS_ASKED + data);
};

export const getCategoryWiseIssues = async (
  data: any
): Promise<AxiosResponse> => {
  //Data => year,month,week, custom->start_date, end_date
  return api.get(API_PATHS.CATEGORY_WISE_ISSUES + data);
};

export const getRectificationAnalytics = async (
  data: any
): Promise<AxiosResponse> => {
  //Data => year,month,week, custom->start_date, end_date
  return api.get(API_PATHS.RECTIFICATION_ANALYTICS + data);
};

export const getListChats = async (data: any): Promise<AxiosResponse> => {
  return api.get(API_PATHS.LIST_CHATS + data);
};

export const getListChatView = async (data: any): Promise<AxiosResponse> => {
  return api.get(API_PATHS.VIEW_CHATS + data);
};

export const getListIssue = async (data: any): Promise<AxiosResponse> => {
  return api.get(API_PATHS.LIST_ISSUES + data);
};
export const getRecIssues = async (data: any): Promise<AxiosResponse> => {
  return api.get(API_PATHS.REC_ISSUES + data);
};

export const getListIssueView = async (data: any): Promise<AxiosResponse> => {
  return api.get(API_PATHS.VIEW_ISSUES + data);
};
