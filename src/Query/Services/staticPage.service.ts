import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { AxiosResponse } from 'axios';

const api = new Api();

export const getStaticPages = async ({
  ordering,
  page,
  limit,
  isSearchable = false,
  field,
  search,
}: {
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  field?: string;
  search?: string;
}): Promise<AxiosResponse> => {
  return api.get(
    API_PATHS.STATIC_PAGE +
      `?ordering=${ordering}&page=${page}&page_size=${limit}${isSearchable ? `&${field}=${search}` : ''}`
  );
};

export const deleteStaticPages = async ({
  listItemId,
}: {
  listItemId: number;
}): Promise<AxiosResponse> => {
  return api.delete(API_PATHS.STATIC_PAGE + `${listItemId}/`);
};

export const updateStaticPage = async (props: {
  listId: number;
  title?: string;
  slug?: string;
  content?: string;
  status?: string;
}): Promise<AxiosResponse> => {
  return api.patch(API_PATHS.STATIC_PAGE + `${props.listId}/`, { data: props });
};
