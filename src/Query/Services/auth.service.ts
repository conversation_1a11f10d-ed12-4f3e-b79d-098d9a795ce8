import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { AxiosResponse } from 'axios';

const api = new Api();

export const adminLogin = async (data: {
  email: string;
  password: string;
}): Promise<AxiosResponse> => {
  return api.post(API_PATHS.LOGIN, { data });
};

export const adminLogout = async (): Promise<AxiosResponse> => {
  return api.delete(API_PATHS.LOGOUT);
};

export const sendForgotPasswordMail = async (data: {
  email: string;
}): Promise<AxiosResponse> => {
  return api.post(API_PATHS.SEND_FORGOT_PASSWORD_EMAIL, { data });
};

export const resetPasswordRequest = async (data: {
  encrypted_token: string;
  new_password: string;
  confirm_password: string;
}): Promise<AxiosResponse> => {
  return api.post(API_PATHS.RESET_PASSWORD, { data });
};

export const newRefreshToken = async (): Promise<AxiosResponse> => {
  return api.post(API_PATHS.NEW_REFRESH_TOKEN);
};

export const getUserProfileFromLocalStorage = () => {
  const storedUser = localStorage.getItem('profile');
  return storedUser ? JSON.parse(storedUser) : null;
};
