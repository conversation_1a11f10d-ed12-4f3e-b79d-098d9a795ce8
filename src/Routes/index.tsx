import '@LanguageProvider/i18n';
import { BrowserRouter, Route, Routes } from 'react-router';
import { Suspense, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { t } from 'i18next';

import MainLoader from '@Components/Layout/MainLoader';
import AuthLayout from '@Components/Layout/AuthLayout';
import MainLayout from '@Components/Layout/MainLayout';
import NotFoundPage from '@Pages/404';
import Breadcrumb from '@Components/UI/Breadcrumb';

import { privateRoutes, publicRoutes } from '@Config/Routes.Config';
import { useGetAdminProfile } from '@Query/Hooks/useAdmin';
import { useAppDispatch } from '@Hooks/useAppDispatch';
import { RootState } from '@Redux/store';
import {
  fetchPermissions,
  filterAccessibleRoutes,
} from '@Redux/SystemControl/PermissionControl';
import InternetConnectionMonitor from '@Pages/InternetConnectionMonitor';
import { RoleGuard } from '@Helpers/Roleguard';
import PATHS from '@Config/Path.Config';

interface LayoutDescription {
  title: string;
  subTitle: string;
}

const RegLayoutDesc: Record<string, LayoutDescription> = {
  '/login': {
    title: t('authentication.pageTitle'),
    subTitle: '',
  },
  '/forgot-password': {
    title: t('authentication.pageTitleFP'),
    subTitle: '',
  },
  '/reset-password': {
    title: t('authentication.changePassword'),
    subTitle: '',
  },
};

const App = () => {
  const dispatch = useAppDispatch();
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  const { data: viewProfileData } = useGetAdminProfile();
  const roleName = viewProfileData?.data?.data?.role;

  const { data } = useSelector((state: RootState) => state.PermissionControl);

  const accessibleRoutes = filterAccessibleRoutes(
    data?.features || [],
    roleName === 1
      ? privateRoutes
      : privateRoutes.filter((i) => i.path !== PATHS.ROLE_MANAGEMENT)
  );

  useEffect(() => {
    if (roleName) {
      dispatch(fetchPermissions(roleName));
    }
  }, [roleName, dispatch]);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  if (!isOnline) {
    return <InternetConnectionMonitor />;
  }

  if (localStorage.getItem('authaccess') && !data) {
    return <MainLoader />;
  }

  return (
    <BrowserRouter>
      <Routes>
        {/* Private Routes */}
        <Route element={<RoleGuard isPublicRoute={false} />}>
          {accessibleRoutes.map(
            ({ path, name, breadcrumb, element: Element }) => (
              <Route
                key={path}
                path={path}
                element={
                  <Suspense fallback={<MainLoader />}>
                    <MainLayout
                      heading={name || ''}
                      breadcrumbs={<Breadcrumb items={breadcrumb || []} />}
                    >
                      <Element />
                    </MainLayout>
                  </Suspense>
                }
              />
            )
          )}
        </Route>

        {/* Public Routes */}
        <Route element={<RoleGuard isPublicRoute={true} />}>
          {publicRoutes.map(({ path, element: Element }) => (
            <Route
              key={path}
              path={path}
              element={
                <Suspense fallback={<MainLoader />}>
                  <AuthLayout
                    title={RegLayoutDesc[path]?.title || ''}
                    subTitle={RegLayoutDesc[path]?.subTitle || ''}
                  >
                    <Element />
                  </AuthLayout>
                </Suspense>
              }
            />
          ))}
        </Route>

        {/* 404 Page */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </BrowserRouter>
  );
};

export default App;
