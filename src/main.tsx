import './index.css';
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import { store } from '@Redux/store';
import { QueryClientProvider } from 'react-query';
import queryClient from '@Helpers/QueryClient';
// import ErrorBoundary from '@Components/UI/Error';
import App from './Routes';
import { ToastProvider } from '@Components/UI/Toast/ToastProvider';

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <QueryClientProvider client={queryClient}>
      <Provider store={store}>
        {/* <ErrorBoundary> */}
        <ToastProvider>
          <App />
        </ToastProvider>
        {/* </ErrorBoundary> */}
      </Provider>
    </QueryClientProvider>
  </StrictMode>
);
