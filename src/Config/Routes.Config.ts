import { lazy, LazyExoticComponent } from 'react';
import PATHS from './Path.Config';
import { t } from 'i18next';
import { MODULE_KEY } from '@Helpers/Constants';

const LicenceApproval = lazy(() => import('@Pages/LicenceApproval'));
const Subscription = lazy(() => import('@Pages/Subscription'));
const UserManagement = lazy(() => import('@Pages/Users'));
const TradieManagement = lazy(() => import('@Pages/TradieManagement'));
const StaticPages = lazy(() => import('@Pages/StaticPage'));
const ContactRequests = lazy(() => import('@Pages/ContactRequests'));
const ForgetPassword = lazy(() => import('@Pages/Auth/ForgetPassword'));
const Login = lazy(() => import('@Pages/Auth/Login'));
const ChangePassword = lazy(() => import('@Pages/Auth/ResetPassword'));
const App = lazy(() => import('@Pages/Dashboard'));
const Profile = lazy(() => import('@Pages/Profile'));
const AdminManagement = lazy(() => import('@Pages/AdminManagement'));
const Knowledgebase = lazy(() => import('@Pages/Knowledgebase'));
const NotificationTemplates = lazy(
  () => import('@Pages/Notifications/NotificationTemplates')
);
const EmailTemplates = lazy(
  () => import('@Pages/Notifications/EmailTemplates')
);

const CategoryWiseChatList = lazy(() => import('@Pages/Dashboard/ChatList'));
const CategoryWiseIssueList = lazy(() => import('@Pages/Dashboard/IssueList'));
const RecIssueList = lazy(() => import('@Pages/Dashboard/RecChatList'));

const TradiesHub = lazy(() => import('@Pages/TradiesHub'));
const TradiesHubPostReport = lazy(() => import('@Pages/TradiesHub/PostReport'));
const TradiesHubCategory = lazy(() => import('@Pages/TradiesHub/Category'));
const TradiesHubComments = lazy(() => import('@Pages/TradiesHub/Comments'));
const TradiesHubCommentReport = lazy(
  () => import('@Pages/TradiesHub/Comments/CommentReports')
);
const TradiesHubReplyReport = lazy(
  () => import('@Pages/TradiesHub/Reply/ReplyReports')
);
const TradiesHubReply = lazy(() => import('@Pages/TradiesHub/Reply'));
const TradiesHubReports = lazy(() => import('@Pages/TradiesHub/Reports'));
const RoleManagement = lazy(() => import('@Pages/RoleManagement'));

const ChangePasswordDirect = lazy(
  () => import('@Pages/Settings/ChangePassword')
);

export const publicRoutes = [
  {
    path: PATHS.LOGIN,
    element: Login,
  },
  {
    path: PATHS.FORGOTPASSWORD,
    element: ForgetPassword,
  },
  {
    path: PATHS.CHANGEPASSWORD,
    element: ChangePassword,
  },
];
export interface PrivateRoutes {
  name?: string;
  path?: string;
  element: LazyExoticComponent<() => JSX.Element>; // Ensure it's always required
  breadcrumb?: Array<{ label: string; link?: string }>;
  permissionKey?: string;
  activeHelper?: string[];
}

export const privateRoutes = [
  {
    name: t('pages.dashboard.title'),
    path: PATHS.DASHBOARD,
    element: App,
    breadcrumb: [{ label: t('pages.dashboard.title') }],
  },
  {
    name: 'Categorywise chat list',
    path: PATHS.CHAT_LIST,
    element: CategoryWiseChatList,
    breadcrumb: [],
  },
  {
    name: 'Categorywise Issue list',
    path: PATHS.ISSUE_LIST,
    element: CategoryWiseIssueList,
    breadcrumb: [],
  },
  {
    name: 'Rectification Issue list',
    path: PATHS.REC_ISSUES,
    element: RecIssueList,
    breadcrumb: [],
  },
  {
    name: t('pages.knowledgebase.title'),
    path: PATHS.KNOWLEDGEBASE,
    permissionKey: MODULE_KEY.KNOWLEDGEBASE,
    element: Knowledgebase,
    breadcrumb: [
      { label: t('pages.dashboard.title'), link: PATHS.DASHBOARD },
      { label: t('pages.knowledgebase.title') },
    ],
  },
  {
    name: t('pages.profile'),
    path: PATHS.PROFILE,
    element: Profile,
    activeHelper: [PATHS.PROFILE, PATHS.CHANGE_PASSWORD],
    breadcrumb: [
      { label: t('pages.dashboard.title'), link: PATHS.DASHBOARD },
      { label: t('pages.profile') },
    ],
  },
  {
    name: t('pages.admins.title'),
    path: PATHS.ADMINMANAGEMENT,
    permissionKey: MODULE_KEY.ADMINS,
    element: AdminManagement,
    breadcrumb: [
      { label: t('pages.dashboard.title'), link: PATHS.DASHBOARD },
      { label: t('pages.admins.title') },
    ],
  },
  {
    name: t('pages.changePassword'),
    path: PATHS.CHANGE_PASSWORD,
    parent: PATHS.ADMINMANAGEMENT,
    element: ChangePasswordDirect,
    breadcrumb: [
      { label: t('pages.dashboard.title'), link: PATHS.DASHBOARD },
      { label: t('pages.profile'), link: PATHS.PROFILE },
      { label: t('pages.changePassword') },
    ],
  },
  {
    name: t('pages.roleManagement.title'),
    path: PATHS.ROLE_MANAGEMENT,
    element: RoleManagement,
    breadcrumb: [
      { label: t('pages.dashboard.title'), link: PATHS.DASHBOARD },
      { label: t('pages.roleManagement.title') },
    ],
  },
  {
    name: t('pages.contactRequest'),
    path: PATHS.CONTACT_REQUESTS,
    permissionKey: MODULE_KEY.CONTACT_REQUEST,
    element: ContactRequests,
    breadcrumb: [
      { label: t('pages.dashboard.title'), link: PATHS.DASHBOARD },
      { label: t('pages.contactRequest') },
    ],
  },
  {
    name: t('pages.staticPages.title'),
    path: PATHS.STATIC_PAGES,
    permissionKey: MODULE_KEY.STATIC_PAGES,
    element: StaticPages,
    breadcrumb: [
      { label: t('pages.dashboard.title'), link: PATHS.DASHBOARD },
      { label: t('pages.staticPages.title') },
    ],
  },
  {
    name: t('pages.users.title'),
    path: PATHS.USERS,
    permissionKey: MODULE_KEY.USERS,
    element: UserManagement,
    breadcrumb: [
      { label: t('pages.dashboard.title'), link: PATHS.DASHBOARD },
      { label: t('pages.users.title') },
    ],
  },
  {
    name: 'Tradies',
    path: PATHS.TRADIES,
    permissionKey: MODULE_KEY.USERS,
    element: TradieManagement,
    breadcrumb: [
      { label: t('pages.dashboard.title'), link: PATHS.DASHBOARD },
      { label: t('pages.users.title') },
    ],
  },
  {
    name: t('pages.licenceApproval.title'),
    path: PATHS.LICENCE_APPROVAL,
    permissionKey: MODULE_KEY.LICENCE_APPROVAL,
    element: LicenceApproval,
    breadcrumb: [
      { label: t('pages.dashboard.title'), link: PATHS.DASHBOARD },
      { label: t('pages.licenceApproval.title') },
    ],
  },
  {
    name: 'Subscriptions',
    path: PATHS.SUBSCRIPTIONS,
    permissionKey: MODULE_KEY.SUBSCRIPTION,
    element: Subscription,
    breadcrumb: [
      { label: t('pages.dashboard.title'), link: PATHS.DASHBOARD },
      { label: 'Subscriptions' },
    ],
  },
  {
    name: 'Tradies Hub',
    path: PATHS.TRADIE_HUB,
    permissionKey: MODULE_KEY.TRADIES_HUB,
    element: TradiesHub,
    breadcrumb: [
      { label: t('pages.dashboard.title'), link: PATHS.DASHBOARD },
      { label: t('Tradies Hub'), link: PATHS.TRADIE_HUB },
    ],
  },
  {
    name: 'Tradies Hub Post Reports',
    path: PATHS.TRADIE_HUB_POST_REPORT,
    permissionKey: MODULE_KEY.TRADIES_HUB,
    element: TradiesHubPostReport,
    breadcrumb: [
      { label: t('pages.dashboard.title'), link: PATHS.DASHBOARD },
      { label: `Tradies Hub`, link: PATHS.TRADIE_HUB },
      { label: `Post Reports`, link: PATHS.TRADIE_HUB_POST_REPORT },
    ],
  },
  {
    name: 'Tradies Hub Comment Reports',
    path: PATHS.TRADIE_HUB_COMMENTS_REPORT,
    permissionKey: MODULE_KEY.TRADIES_HUB,
    element: TradiesHubCommentReport,
    breadcrumb: [
      { label: t('pages.dashboard.title'), link: PATHS.DASHBOARD },
      { label: `Tradies Hub`, link: PATHS.TRADIE_HUB },
      { label: `Comment Reports`, link: PATHS.TRADIE_HUB_COMMENTS_REPORT },
    ],
  },
  {
    name: 'Tradies Hub Reply Reports',
    path: PATHS.TRADIE_HUB_REPLIES_REPORT,
    permissionKey: MODULE_KEY.TRADIES_HUB,
    element: TradiesHubReplyReport,
    breadcrumb: [
      { label: t('pages.dashboard.title'), link: PATHS.DASHBOARD },
      { label: `Tradies Hub`, link: PATHS.TRADIE_HUB },
      { label: `Reply Reports`, link: PATHS.TRADIE_HUB_REPLIES_REPORT },
    ],
  },
  {
    name: 'Tradies Hub Post Comments',
    path: PATHS.TRADIE_HUB_COMMENTS,
    permissionKey: MODULE_KEY.TRADIES_HUB,
    element: TradiesHubComments,
    breadcrumb: [
      { label: t('pages.dashboard.title'), link: PATHS.DASHBOARD },
      { label: `Tradies Hub`, link: PATHS.TRADIE_HUB },
      { label: `Post Comments`, link: PATHS.TRADIE_HUB_COMMENTS },
    ],
  },
  {
    name: 'Tradies Hub Comment Replies',
    path: PATHS.TRADIE_HUB_REPLY,
    permissionKey: MODULE_KEY.TRADIES_HUB,
    element: TradiesHubReply,
    breadcrumb: [
      { label: t('pages.dashboard.title'), link: PATHS.DASHBOARD },
      { label: `Tradies Hub`, link: PATHS.TRADIE_HUB },
      { label: `Comment Replies`, link: PATHS.TRADIE_HUB_REPLY },
    ],
  },
  {
    name: 'Tradies Hub Reports',
    path: PATHS.TRADIE_HUB_REPORTS,
    permissionKey: MODULE_KEY.TRADIES_HUB,
    element: TradiesHubReports,
    breadcrumb: [
      { label: t('pages.dashboard.title'), link: PATHS.DASHBOARD },
      { label: `Tradies Hub`, link: PATHS.TRADIE_HUB },
      { label: `Reports`, link: PATHS.TRADIE_HUB_REPORTS },
    ],
  },
  {
    name: 'Tradies Hub Categories',
    path: PATHS.TRADIE_HUB_CATEGORY,
    permissionKey: MODULE_KEY.TRADIES_HUB,
    element: TradiesHubCategory,
    breadcrumb: [
      { label: t('pages.dashboard.title'), link: PATHS.DASHBOARD },
      { label: `Tradies Hub`, link: PATHS.TRADIE_HUB },
      { label: t('Tradies Hub Categories'), link: PATHS.TRADIE_HUB_REPORTS },
    ],
  },
  {
    name: 'Notification Templates',
    path: PATHS.NOTIFICATIONS_TEMPLATES,
    permissionKey: MODULE_KEY.NOTIFICATION,
    element: NotificationTemplates,
    breadcrumb: [],
  },
  {
    name: 'Email Templates',
    path: PATHS.EMAIL_TEMPLATES,
    permissionKey: MODULE_KEY.NOTIFICATION,
    element: EmailTemplates,
    breadcrumb: [],
  },
];
