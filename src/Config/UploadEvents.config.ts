/* eslint-disable @typescript-eslint/no-explicit-any */
export type UploadEvent = {
  id: string;
  isVideoCreationCompleted: boolean;
  isVideoUploadCompleted: boolean;
  isVideoCreateLoading: boolean;
  multipart_upload: number;

  videoDetails: any;
  fileSize: string;
  fileName: string;
  progress: number;
  isVideoError: boolean;
  videoError: any;
};

export type UploadEventUpdate = Partial<Omit<UploadEvent, 'id'>> & {
  id: string;
};
export type UploadEventRemove = { id: string; remove: true };
export type UploadEventOrRemove = UploadEventUpdate | UploadEventRemove;

type UploadEventBus = {
  listeners: ((event: UploadEventOrRemove) => void)[];
  data: Record<string, UploadEvent>;
  emit: (event: UploadEventOrRemove) => void;
  subscribe: (listener: (event: UploadEventOrRemove) => void) => () => void;
  get: () => UploadEvent[];
  clearAll: () => void;
  edit: (update: UploadEventUpdate) => void;
  hasActiveUploads: () => boolean;
};

export const uploadEventBus: UploadEventBus = {
  listeners: [],
  data: {},
  emit(event) {
    if ('remove' in event) {
      delete this.data[event.id];
    } else {
      this.data[event.id] = {
        ...this.data[event.id],
        ...event,
      } as UploadEvent;
    }
    this.listeners.forEach((l) => l(event));
  },
  subscribe(listener) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter((l) => l !== listener);
    };
  },
  get() {
    return Object.values(this.data);
  },
  clearAll() {
    Object.keys(this.data).forEach((id) => {
      this.emit({ id, remove: true });
    });
  },
  edit(update) {
    const existing = this.data[update.id];
    if (!existing) {
      console.warn(
        `Cannot edit upload event. No event found with id: ${update.id}`
      );
      return;
    }
    const updatedEvent = { ...existing, ...update };
    this.data[update.id] = updatedEvent;
    this.listeners.forEach((l) => l(update));
  },
  hasActiveUploads() {
    return Object.values(this.data).some(
      (upload) => !upload.isVideoCreationCompleted && !upload.isVideoError
    );
  },
};
