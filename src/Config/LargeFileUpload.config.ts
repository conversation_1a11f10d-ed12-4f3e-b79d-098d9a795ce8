/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation } from 'react-query';
import axios from 'axios';
import { saveUploadState, clearUploadState } from './uploadState';
import { uploadEventBus } from './UploadEvents.config';
import { SecondsToMilliseconds } from '@Helpers/Utils';
import queryClient from '@Helpers/QueryClient';
import CacheKeys from '@Helpers/CacheKeys';

const API =
  import.meta.env.VITE_APP_API_URL + 'cms/training-education/video/uploads/';

const getToken = localStorage.getItem('authaccess');

// Step 1: Initialize multipart upload
const uploadInit = async (file: File) => {
  const res = await axios.post(
    API,
    {
      file_name: file.name,
      file_size: file.size,
    },
    {
      headers: {
        Authorization: `Bearer ${getToken}`,
      },
    }
  );
  return res.data.data;
};

// Step 2: Get presigned URLs from backend
const getPresignedUrls = async (id: number) => {
  const res = await axios.get(`${API}${id}/presigned-urls/`, {
    headers: {
      Authorization: `Bearer ${getToken}`,
    },
  });
  return res.data.data;
};

// Step 3: Upload individual chunk to S3
const uploadChunk = async ({
  url,
  chunk,
  onChunkComplete,
}: {
  url: string;
  chunk: Blob;
  onChunkComplete: (uploadedBytes: number) => void;
}) => {
  const res = await axios.put(url, chunk, {
    headers: {
      'Content-Type': 'application/octet-stream',
    },
  });

  onChunkComplete(chunk.size);
  return res.headers.etag;
};

// Step 4: Notify backend that a part is uploaded
const completePart = async ({
  id,
  part_number,
  etag,
}: {
  id: number;
  part_number: number;
  etag: string;
}) => {
  await axios.post(
    `${API}${id}/complete-part/`,
    { part_number, etag },
    {
      headers: {
        Authorization: `Bearer ${getToken}`,
      },
    }
  );
};

const completeUpload = async ({
  id,
  key,
  parts,
  createData,
  busProcessId,
  reset,
}: {
  id: number;
  key: string;
  parts: { ETag: string; PartNumber: number }[];
  createData: {
    category: any;
    title: string;
    price: number;
    description: string;
    multipart_upload: number;
    author_name: string;
    duration: number;
    video_url: string;
    thumbnail_img: File;
    video: any;
  };
  busProcessId: any;
  reset: () => void;
}) => {
  const res = await axios.post(
    `${API}${id}/complete/`,
    {
      uploadId: id,
      key,
      parts,
    },
    {
      headers: {
        Authorization: `Bearer ${getToken}`,
      },
    }
  );

  const finalVideoUrl = res?.data?.data?.video_url;
  const stackId = uploadEventBus.get()?.length;
  const stackMultipart = uploadEventBus.get()[stackId - 1]?.multipart_upload;

  delete createData.video;

  uploadEventBus.edit({
    id: busProcessId,
    isVideoCreateLoading: true,
  });
};

// Main Upload Hook
export const useVideoUpload = () => {
  const startUpload = async (
    file: File,
    busProcessId: string,
    onProgress: (percent: number) => void,

    formData: any,
    reset: () => void
  ) => {
    const initData = await uploadInit(file);
    const { id, upload_id, file_name } = initData;
    const busData = uploadEventBus.get();
    uploadEventBus.emit({
      id: busProcessId,
      multipart_upload: id,
      fileName: file.name,
      progress: 0,
      fileSize: file.size.toString(),
      isVideoCreationCompleted: false,
      isVideoUploadCompleted: false,
      videoDetails: formData,
    });

    const presignedUrls = await getPresignedUrls(id);
    const chunks = Object.entries(presignedUrls);
    const partSize = 100 * 1024 * 1024; // 100 MB

    const totalSize = file.size;
    let uploaded = 0;
    const parts: { ETag: string; PartNumber: number }[] = [];

    for (let i = 0; i < chunks.length; i++) {
      const [partNumberStr, url] = chunks[i];
      const partNumber = Number(partNumberStr);

      const start = i * partSize;
      const end = Math.min(start + partSize, totalSize);
      const chunkBlob = file.slice(start, end);

      try {
        const etag = await uploadChunk({
          url: url as string,
          chunk: chunkBlob,
          onChunkComplete: (bytes) => {
            uploaded += bytes;
            const percent = Math.floor((uploaded / totalSize) * 100);
            onProgress(percent);

            uploadEventBus.edit({
              id: busProcessId,
              progress: percent,
              ...busData,
            });
          },
        });

        await completePart({ id, part_number: partNumber, etag });
        parts.push({ ETag: etag, PartNumber: partNumber });

        saveUploadState({
          id,
          uploadId: upload_id,
          key: `rex_construction/training-education-video/${file_name}`,
          parts: parts.map((p) => ({
            partNumber: p.PartNumber,
            etag: p.ETag,
            completed: true,
          })),
          file,
        });
      } catch (error) {
        console.error(`❌ Chunk ${partNumber} failed`, error);
        throw error;
      }
    }

    const result = await completeUpload({
      id,
      key: `rex_construction/training-education-video/${file_name}`,
      parts,
      createData: { ...formData },
      busProcessId: busProcessId,
      reset,
    });

    clearUploadState();
    return result;
  };

  return useMutation(
    ({
      file,
      onProgress,
      busProcessId,
      formData,
      reset,
    }: {
      file: File;
      busProcessId: string;
      onProgress: (percent: number) => void;

      formData: any;
      reset: () => void;
    }) => startUpload(file, busProcessId, onProgress, formData, reset)
  );
};
