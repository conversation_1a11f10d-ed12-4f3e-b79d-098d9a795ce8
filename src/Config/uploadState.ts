// uploadState.ts
export type UploadPartStatus = {
  partNumber: number;
  etag: string | null;
  completed: boolean;
};

export type UploadState = {
  uploadId: string;
  key: string;
  id: number;
  parts: UploadPartStatus[];
  file: File;
};

const UPLOAD_KEY = 'video-upload-state';

export function saveUploadState(state: UploadState) {
  localStorage.setItem(UPLOAD_KEY, JSON.stringify(state));
}

export function getUploadState(): UploadState | null {
  const item = localStorage.getItem(UPLOAD_KEY);
  return item ? JSON.parse(item) : null;
}

export function clearUploadState() {
  localStorage.removeItem(UPLOAD_KEY);
}
