import {
  AdminIcon,
  EmailIcon,
  FileIcon,
  HouseIcon,
  KeyIcon,
  PersonIcon,
  SettingIcon,
  SubscriptionIcon,
  UsersIcon,
  CategoryIcon,
  QuestionsIcon,
  ReportIcon,
  BellIcon,
} from '@Icons';
import PATHS from './Path.Config';
import { t } from 'i18next';
import { MODULE_KEY } from '@Helpers/Constants';

export interface MenuItemType {
  key: string;
  title: string;
  Icon?: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  path?: string;
  bottom?: boolean;
  permissionKey?: string;
  children?: {
    key: string;
    title: string;
    Icon?: React.ComponentType<React.SVGProps<SVGSVGElement>>;
    permissionKey?: string;
    path?: string;
    activeHelper?: string[];
  }[];
  activeHelper?: string[];
}

const menuItems: MenuItemType[] = [
  {
    key: 'dashboard',
    title: t('pages.dashboard.title'),
    Icon: HouseIcon,
    path: PATHS.DASHBOARD,
  },
  {
    key: 'adminMng',
    title: t('pages.admins.title'),
    permissionKey: MODULE_KEY.ADMINS,
    activeHelper: [PATHS.ADMINMANAGEMENT, PATHS.ADD_SUB_ADMIN],
    Icon: AdminIcon,
    path: PATHS.ADMINMANAGEMENT,
  },
  {
    key: 'users',
    title: 'Users Management',
    Icon: UsersIcon,
    children: [
      {
        key: 'users',
        title: `Users`,
        permissionKey: MODULE_KEY.USERS,
        Icon: PersonIcon,
        path: PATHS.USERS,
        activeHelper: [PATHS.ADD_USERS],
      },
      {
        key: 'tradies',
        title: "Tradies",
        permissionKey: MODULE_KEY.USERS,
        Icon: CategoryIcon,
        path: PATHS.TRADIES,
      },
    ],
  },
  {
    key: 'tradiesHub',
    title: "Tradie's Hub",
    Icon: QuestionsIcon,
    path: '#',
    children: [
      {
        key: 'tradiesHub',
        title: `Tradie's Hub Posts`,
        Icon: QuestionsIcon,
        path: PATHS.TRADIE_HUB,
        permissionKey: MODULE_KEY.TRADIES_HUB,
        activeHelper: [
          PATHS.TRADIE_HUB,
          PATHS.TRADIE_HUB_POST_REPORT,
          PATHS.TRADIE_HUB_COMMENTS,
          PATHS.TRADIE_HUB_COMMENTS_REPORT,
          PATHS.TRADIE_HUB_REPLY,
          PATHS.TRADIE_HUB_REPLIES_REPORT,
        ],
      },
      {
        key: 'tradiesHub',
        title: `Tradie's Hub Categories`,
        Icon: CategoryIcon,
        path: PATHS.TRADIE_HUB_CATEGORY,
        permissionKey: MODULE_KEY.TRADIES_HUB,
        activeHelper: [PATHS.TRADIE_HUB_CATEGORY],
      },
      {
        key: 'tradiesHub',
        title: `Tradie's Hub Reports`,
        Icon: ReportIcon,
        path: PATHS.TRADIE_HUB_REPORTS,
        permissionKey: MODULE_KEY.TRADIES_HUB,
        activeHelper: [PATHS.TRADIE_HUB_REPORTS],
      },
    ],
  },
  {
    key: 'subscription',
    title: t('pages.subscriptions.title'),
    Icon: SubscriptionIcon,
    path: PATHS.SUBSCRIPTIONS,
  },
  {
    key: 'rolesPermission',
    title: t('pages.roleManagement.title'),
    Icon: KeyIcon,
    path: PATHS.ROLE_MANAGEMENT,
  },

  {
    key: 'contactRequest',
    title: t('pages.contactRequest'),
    permissionKey: MODULE_KEY.CONTACT_REQUEST,
    Icon: EmailIcon,
    path: PATHS.CONTACT_REQUESTS,
  },
  {
    key: 'notifications',
    title: `Notifications`,
    Icon: BellIcon,
    path: '#',
    children: [
      {
        key: 'emailTemplates',
        title: 'Email Templates',
        Icon: EmailIcon,
        path: PATHS.EMAIL_TEMPLATES,
      },
      {
        key: 'notificationTemplates',
        title: 'Notification Templates',
        Icon: BellIcon,
        path: PATHS.NOTIFICATIONS_TEMPLATES,
      },
    ],
  },

  {
    key: 'settings',
    title: 'Settings',
    Icon: SettingIcon,
    children: [
      {
        key: 'staticPages',
        title: t('pages.staticPages.title'),
        permissionKey: MODULE_KEY.STATIC_PAGES,
        Icon: FileIcon,
        path: PATHS.STATIC_PAGES,
      },
    ],
  },
  // {
  //   key: 'knowledgebase',
  //   title: t('pages.knowledgebase.title'),
  //   permissionKey: MODULE_KEY.KNOWLEDGEBASE,
  //   Icon: RoboIcon,
  //   path: PATHS.KNOWLEDGEBASE,
  // },
  // {
  //   key: 'account',
  //   title: 'Rex admin',
  //   Icon: UserCircleIcon,
  //   path: PATHS.PROFILE,
  //   bottom: true,
  //   activeHelper: [PATHS.PROFILE, PATHS.CHANGE_PASSWORD],
  // },
];
export default menuItems;
