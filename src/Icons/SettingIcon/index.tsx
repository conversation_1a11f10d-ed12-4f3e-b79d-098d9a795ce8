import React from 'react';

const SettingIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      viewBox="0 0 28 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.0001 8.00006C12.8134 8.00006 11.6533 8.35195 10.6666 9.01124C9.67994 9.67053 8.91091 10.6076 8.45678 11.704C8.00265 12.8003 7.88383 14.0067 8.11535 15.1706C8.34686 16.3345 8.9183 17.4036 9.75742 18.2427C10.5965 19.0818 11.6656 19.6533 12.8295 19.8848C13.9934 20.1163 15.1998 19.9975 16.2962 19.5433C17.3925 19.0892 18.3296 18.3202 18.9889 17.3335C19.6482 16.3468 20.0001 15.1867 20.0001 14.0001C19.9984 12.4093 19.3657 10.8841 18.2409 9.75924C17.116 8.63438 15.5908 8.00171 14.0001 8.00006ZM14.0001 18.0001C13.2089 18.0001 12.4356 17.7655 11.7778 17.3259C11.12 16.8864 10.6073 16.2617 10.3045 15.5308C10.0018 14.7999 9.92257 13.9956 10.0769 13.2197C10.2313 12.4438 10.6122 11.731 11.1716 11.1716C11.731 10.6122 12.4438 10.2313 13.2197 10.0769C13.9956 9.92257 14.7999 10.0018 15.5308 10.3045C16.2617 10.6073 16.8864 11.12 17.3259 11.7778C17.7655 12.4356 18.0001 13.2089 18.0001 14.0001C18.0001 15.0609 17.5786 16.0783 16.8285 16.8285C16.0783 17.5786 15.0609 18.0001 14.0001 18.0001ZM25.0001 14.2701C25.0051 14.0901 25.0051 13.9101 25.0001 13.7301L26.8651 11.4001C26.9628 11.2777 27.0305 11.1341 27.0627 10.9808C27.0948 10.8276 27.0905 10.6689 27.0501 10.5176C26.7443 9.36831 26.287 8.26487 25.6901 7.23631C25.6119 7.1017 25.5034 6.9872 25.3732 6.90192C25.243 6.81664 25.0947 6.76292 24.9401 6.74506L21.9751 6.41506C21.8517 6.28506 21.7267 6.16006 21.6001 6.04006L21.2501 3.06756C21.2321 2.91282 21.1782 2.76443 21.0926 2.63422C21.0071 2.50401 20.8924 2.39557 20.7576 2.31756C19.7286 1.72174 18.6253 1.26485 17.4763 0.958807C17.3249 0.918553 17.1662 0.914436 17.0129 0.946787C16.8596 0.979139 16.716 1.04705 16.5938 1.14506L14.2701 3.00006C14.0901 3.00006 13.9101 3.00006 13.7301 3.00006L11.4001 1.13881C11.2777 1.04102 11.1341 0.973329 10.9808 0.941196C10.8276 0.909063 10.6689 0.913382 10.5176 0.953807C9.36849 1.26009 8.26513 1.71741 7.23631 2.31381C7.1017 2.39196 6.9872 2.50046 6.90192 2.63067C6.81664 2.76087 6.76292 2.90919 6.74506 3.06381L6.41506 6.03381C6.28506 6.15797 6.16006 6.28297 6.04006 6.40881L3.06756 6.75006C2.91282 6.76806 2.76443 6.82196 2.63422 6.90747C2.50401 6.99298 2.39557 7.10772 2.31756 7.24256C1.72174 8.27151 1.26485 9.37486 0.958807 10.5238C0.918553 10.6752 0.914436 10.834 0.946787 10.9872C0.979139 11.1405 1.04705 11.2841 1.14506 11.4063L3.00006 13.7301C3.00006 13.9101 3.00006 14.0901 3.00006 14.2701L1.13881 16.6001C1.04102 16.7224 0.973329 16.866 0.941196 17.0193C0.909063 17.1726 0.913382 17.3312 0.953807 17.4826C1.25954 18.6318 1.71689 19.7352 2.31381 20.7638C2.39196 20.8984 2.50046 21.0129 2.63067 21.0982C2.76087 21.1835 2.90919 21.2372 3.06381 21.2551L6.02881 21.5851C6.15297 21.7151 6.27797 21.8401 6.40381 21.9601L6.75006 24.9326C6.76806 25.0873 6.82196 25.2357 6.90747 25.3659C6.99298 25.4961 7.10772 25.6045 7.24256 25.6826C8.27151 26.2784 9.37486 26.7353 10.5238 27.0413C10.6752 27.0816 10.834 27.0857 10.9872 27.0533C11.1405 27.021 11.2841 26.9531 11.4063 26.8551L13.7301 25.0001C13.9101 25.0051 14.0901 25.0051 14.2701 25.0001L16.6001 26.8651C16.7224 26.9628 16.866 27.0305 17.0193 27.0627C17.1726 27.0948 17.3312 27.0905 17.4826 27.0501C18.6318 26.7443 19.7352 26.287 20.7638 25.6901C20.8984 25.6119 21.0129 25.5034 21.0982 25.3732C21.1835 25.243 21.2372 25.0947 21.2551 24.9401L21.5851 21.9751C21.7151 21.8517 21.8401 21.7267 21.9601 21.6001L24.9326 21.2501C25.0873 21.2321 25.2357 21.1782 25.3659 21.0926C25.4961 21.0071 25.6045 20.8924 25.6826 20.7576C26.2784 19.7286 26.7353 18.6253 27.0413 17.4763C27.0816 17.3249 27.0857 17.1662 27.0533 17.0129C27.021 16.8596 26.9531 16.716 26.8551 16.5938L25.0001 14.2701ZM22.9876 13.4576C23.0088 13.8189 23.0088 14.1812 22.9876 14.5426C22.9727 14.79 23.0502 15.0341 23.2051 15.2276L24.9788 17.4438C24.7753 18.0906 24.5147 18.7181 24.2001 19.3188L21.3751 19.6388C21.129 19.6661 20.9019 19.7837 20.7376 19.9688C20.4969 20.2394 20.2407 20.4957 19.9701 20.7363C19.7849 20.9006 19.6674 21.1278 19.6401 21.3738L19.3263 24.1963C18.7257 24.5111 18.0982 24.7717 17.4513 24.9751L15.2338 23.2013C15.0564 23.0595 14.8359 22.9824 14.6088 22.9826H14.5488C14.1875 23.0038 13.8252 23.0038 13.4638 22.9826C13.2164 22.9677 12.9723 23.0452 12.7788 23.2001L10.5563 24.9751C9.90947 24.7715 9.28201 24.5109 8.68131 24.1963L8.36131 21.3751C8.334 21.129 8.21642 20.9019 8.03131 20.7376C7.76069 20.4969 7.50442 20.2407 7.26381 19.9701C7.09947 19.7849 6.87233 19.6674 6.62631 19.6401L3.80381 19.3251C3.48905 18.7244 3.22843 18.0969 3.02506 17.4501L4.79881 15.2326C4.95369 15.0391 5.0312 14.795 5.01631 14.5476C4.99506 14.1862 4.99506 13.8239 5.01631 13.4626C5.0312 13.2152 4.95369 12.971 4.79881 12.7776L3.02506 10.5563C3.22859 9.90947 3.4892 9.28201 3.80381 8.68131L6.62506 8.36131C6.87108 8.334 7.09822 8.21642 7.26256 8.03131C7.50317 7.76069 7.75944 7.50442 8.03006 7.26381C8.2159 7.09937 8.33397 6.8717 8.36131 6.62506L8.67506 3.80381C9.27569 3.48905 9.90317 3.22843 10.5501 3.02506L12.7676 4.79881C12.961 4.95369 13.2052 5.0312 13.4526 5.01631C13.8139 4.99506 14.1762 4.99506 14.5376 5.01631C14.785 5.0312 15.0291 4.95369 15.2226 4.79881L17.4438 3.02506C18.0906 3.22859 18.7181 3.4892 19.3188 3.80381L19.6388 6.62506C19.6661 6.87108 19.7837 7.09822 19.9688 7.26256C20.2394 7.50317 20.4957 7.75944 20.7363 8.03006C20.9006 8.21517 21.1278 8.33275 21.3738 8.36006L24.1963 8.67381C24.5111 9.27444 24.7717 9.90192 24.9751 10.5488L23.2013 12.7663C23.0449 12.9614 22.9673 13.2081 22.9838 13.4576H22.9876Z"
        fill={props.fill ?? '#343330'}
      />
    </svg>
  );
};

export default SettingIcon;
