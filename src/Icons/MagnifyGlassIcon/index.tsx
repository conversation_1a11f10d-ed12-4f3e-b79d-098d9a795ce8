import React from 'react';

const MagnifyGlass = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 32 32" fill="none">
      <path
        d="M26.7075 25.2923L20.4488 19.0348C22.2628 16.8569 23.1674 14.0635 22.9743 11.2357C22.7813 8.40789 21.5054 5.76337 19.4122 3.85226C17.319 1.94116 14.5696 0.910614 11.7359 0.975014C8.90225 1.03941 6.20249 2.1938 4.19827 4.19802C2.19404 6.20225 1.03966 8.90201 0.975258 11.7357C0.910859 14.5694 1.9414 17.3188 3.85251 19.412C5.76361 21.5052 8.40814 22.781 11.236 22.9741C14.0638 23.1672 16.8572 22.2626 19.035 20.4485L25.2925 26.7073C25.3855 26.8002 25.4958 26.8739 25.6171 26.9242C25.7385 26.9745 25.8686 27.0004 26 27.0004C26.1314 27.0004 26.2615 26.9745 26.3829 26.9242C26.5043 26.8739 26.6146 26.8002 26.7075 26.7073C26.8005 26.6144 26.8742 26.5041 26.9244 26.3827C26.9747 26.2613 27.0006 26.1312 27.0006 25.9998C27.0006 25.8684 26.9747 25.7383 26.9244 25.6169C26.8742 25.4955 26.8005 25.3852 26.7075 25.2923ZM3.00004 11.9998C3.00004 10.2198 3.52788 8.47971 4.51681 6.99966C5.50575 5.51962 6.91136 4.36607 8.55589 3.68488C10.2004 3.00369 12.01 2.82546 13.7559 3.17273C15.5017 3.52 17.1053 4.37716 18.364 5.63584C19.6227 6.89451 20.4798 8.49816 20.8271 10.244C21.1744 11.9898 20.9961 13.7994 20.315 15.4439C19.6338 17.0885 18.4802 18.4941 17.0002 19.483C15.5201 20.472 13.7801 20.9998 12 20.9998C9.6139 20.9972 7.32626 20.0481 5.639 18.3608C3.95175 16.6736 3.00269 14.3859 3.00004 11.9998Z"
        fill="#343330"
      />
    </svg>
  );
};

export default MagnifyGlass;
