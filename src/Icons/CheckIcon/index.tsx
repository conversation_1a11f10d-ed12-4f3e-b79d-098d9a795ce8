import React from 'react';

const CheckIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      viewBox="0 0 27 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M26.0612 3.06114L10.0612 19.0611C9.92184 19.201 9.75625 19.3119 9.57392 19.3876C9.3916 19.4633 9.19612 19.5023 8.9987 19.5023C8.80127 19.5023 8.60579 19.4633 8.42347 19.3876C8.24114 19.3119 8.07555 19.201 7.9362 19.0611L0.936196 12.0611C0.796666 11.9216 0.685985 11.756 0.610472 11.5737C0.53496 11.3914 0.496094 11.196 0.496094 10.9986C0.496094 10.8013 0.53496 10.6059 0.610472 10.4236C0.685985 10.2413 0.796666 10.0757 0.936196 9.93614C1.07573 9.79661 1.24137 9.68593 1.42367 9.61041C1.60598 9.5349 1.80137 9.49603 1.9987 9.49603C2.19602 9.49603 2.39141 9.5349 2.57372 9.61041C2.75602 9.68593 2.92167 9.79661 3.0612 9.93614L8.99995 15.8749L23.9387 0.938637C24.2205 0.656844 24.6027 0.498535 25.0012 0.498535C25.3997 0.498535 25.7819 0.656844 26.0637 0.938637C26.3455 1.22043 26.5038 1.60262 26.5038 2.00114C26.5038 2.39965 26.3455 2.78184 26.0637 3.06364L26.0612 3.06114Z"
        fill={props.fill ? props.fill : '#666666'}
      />
    </svg>
  );
};

export default CheckIcon;
