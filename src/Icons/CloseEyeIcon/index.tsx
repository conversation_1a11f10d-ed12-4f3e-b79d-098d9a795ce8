import React from 'react';

const CloseEyeIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 32 32" fill="none">
      <path
        d="M6.73999 4.32758C6.65217 4.22859 6.54558 4.14801 6.42639 4.09052C6.3072 4.03303 6.17778 3.99976 6.04564 3.99265C5.91351 3.98555 5.78127 4.00473 5.6566 4.04911C5.53193 4.09348 5.41731 4.16216 5.31938 4.25116C5.22144 4.34016 5.14215 4.44771 5.08609 4.56758C5.03003 4.68745 4.99832 4.81725 4.9928 4.94947C4.98727 5.08168 5.00804 5.21368 5.05391 5.33781C5.09978 5.46194 5.16982 5.57573 5.25999 5.67258L7.66499 8.31883C3.12499 11.1051 1.17249 15.4001 1.08624 15.5951C1.02938 15.723 1 15.8614 1 16.0013C1 16.1413 1.02938 16.2797 1.08624 16.4076C1.12999 16.5063 2.18874 18.8538 4.54249 21.2076C7.67874 24.3426 11.64 26.0001 16 26.0001C18.2408 26.0128 20.4589 25.5515 22.5087 24.6463L25.2587 27.6726C25.3466 27.7716 25.4531 27.8522 25.5723 27.9096C25.6915 27.9671 25.8209 28.0004 25.9531 28.0075C26.0852 28.0146 26.2175 27.9954 26.3421 27.9511C26.4668 27.9067 26.5814 27.838 26.6793 27.749C26.7773 27.66 26.8566 27.5525 26.9126 27.4326C26.9687 27.3127 27.0004 27.1829 27.0059 27.0507C27.0115 26.9185 26.9907 26.7865 26.9448 26.6624C26.899 26.5382 26.8289 26.4244 26.7387 26.3276L6.73999 4.32758ZM12.6562 13.8076L17.865 19.5388C17.0806 19.9515 16.1814 20.0919 15.3085 19.9381C14.4357 19.7843 13.6386 19.345 13.0425 18.6891C12.4464 18.0332 12.085 17.1979 12.0151 16.3144C11.9452 15.4308 12.1707 14.5491 12.6562 13.8076ZM16 24.0001C12.1525 24.0001 8.79124 22.6013 6.00874 19.8438C4.86663 18.7088 3.89526 17.4141 3.12499 16.0001C3.71124 14.9013 5.58249 11.8263 9.04374 9.82758L11.2937 12.2963C10.4227 13.412 9.97403 14.7996 10.0272 16.214C10.0803 17.6285 10.6317 18.9786 11.584 20.0257C12.5363 21.0729 13.8282 21.7496 15.2312 21.9364C16.6343 22.1232 18.0582 21.8079 19.2512 21.0463L21.0925 23.0713C19.4675 23.6948 17.7405 24.0097 16 24.0001ZM16.75 12.0713C16.4894 12.0216 16.2593 11.8704 16.1102 11.651C15.9611 11.4316 15.9053 11.1619 15.955 10.9013C16.0047 10.6408 16.1559 10.4106 16.3753 10.2615C16.5948 10.1124 16.8644 10.0566 17.125 10.1063C18.3995 10.3534 19.56 11.0059 20.4333 11.9664C21.3067 12.927 21.8462 14.1441 21.9712 15.4363C21.9959 15.7004 21.9147 15.9634 21.7455 16.1676C21.5762 16.3718 21.3328 16.5004 21.0687 16.5251C21.0375 16.5269 21.0062 16.5269 20.975 16.5251C20.725 16.5262 20.4838 16.4336 20.2987 16.2656C20.1136 16.0976 19.9981 15.8665 19.975 15.6176C19.8908 14.7581 19.5315 13.9487 18.9504 13.3098C18.3694 12.6708 17.5977 12.2365 16.75 12.0713ZM30.91 16.4076C30.8575 16.5251 29.5912 19.3288 26.74 21.8826C26.6426 21.9726 26.5282 22.0424 26.4036 22.0878C26.2789 22.1332 26.1465 22.1533 26.014 22.1471C25.8814 22.1408 25.7515 22.1082 25.6317 22.0512C25.5119 21.9942 25.4047 21.914 25.3162 21.8151C25.2277 21.7163 25.1598 21.6008 25.1163 21.4755C25.0729 21.3501 25.0549 21.2174 25.0633 21.085C25.0716 20.9526 25.1063 20.8232 25.1652 20.7043C25.2241 20.5855 25.306 20.4795 25.4062 20.3926C26.8051 19.1359 27.9801 17.6505 28.8812 16.0001C28.1093 14.5848 27.1358 13.2892 25.9912 12.1538C23.2087 9.39883 19.8475 8.00008 16 8.00008C15.1893 7.99909 14.3799 8.06473 13.58 8.19633C13.4499 8.21934 13.3166 8.21634 13.1876 8.18752C13.0587 8.15869 12.9368 8.10461 12.8289 8.02839C12.721 7.95217 12.6293 7.85533 12.559 7.74346C12.4887 7.63159 12.4413 7.50691 12.4196 7.37662C12.3978 7.24633 12.402 7.11301 12.432 6.98436C12.462 6.85572 12.5172 6.7343 12.5945 6.62711C12.6717 6.51992 12.7694 6.4291 12.8819 6.35988C12.9944 6.29066 13.1195 6.24442 13.25 6.22383C14.1589 6.07375 15.0787 5.99891 16 6.00008C20.36 6.00008 24.3212 7.65758 27.4575 10.7938C29.8112 13.1476 30.87 15.4963 30.9137 15.5951C30.9706 15.723 31 15.8614 31 16.0013C31 16.1413 30.9706 16.2797 30.9137 16.4076H30.91Z"
        fill={props.fill ?? '#343330'}
      />
    </svg>
  );
};

export default CloseEyeIcon;
