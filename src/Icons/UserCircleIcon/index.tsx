import React from 'react';

const UserCircleIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 26 27" fill="none">
      <path
        d="M13 0.5C10.4288 0.5 7.91543 1.26244 5.77759 2.6909C3.63975 4.11935 1.97351 6.14968 0.989572 8.52512C0.0056327 10.9006 -0.251811 13.5144 0.249797 16.0362C0.751405 18.5579 1.98953 20.8743 3.80762 22.6924C5.6257 24.5105 7.94208 25.7486 10.4638 26.2502C12.9856 26.7518 15.5995 26.4944 17.9749 25.5104C20.3503 24.5265 22.3807 22.8603 23.8091 20.7224C25.2376 18.5846 26 16.0712 26 13.5C25.9964 10.0533 24.6256 6.74882 22.1884 4.31163C19.7512 1.87445 16.4467 0.50364 13 0.5ZM6.26001 22.1875C6.98342 21.0561 7.98001 20.125 9.15791 19.4801C10.3358 18.8351 11.6571 18.4971 13 18.4971C14.3429 18.4971 15.6642 18.8351 16.8421 19.4801C18.02 20.125 19.0166 21.0561 19.74 22.1875C17.8129 23.6862 15.4413 24.4999 13 24.4999C10.5587 24.4999 8.18709 23.6862 6.26001 22.1875ZM9.00001 12.5C9.00001 11.7089 9.2346 10.9355 9.67413 10.2777C10.1137 9.61992 10.7384 9.10723 11.4693 8.80448C12.2002 8.50173 13.0044 8.42252 13.7804 8.57686C14.5563 8.7312 15.269 9.11216 15.8284 9.67157C16.3878 10.231 16.7688 10.9437 16.9231 11.7196C17.0775 12.4956 16.9983 13.2998 16.6955 14.0307C16.3928 14.7616 15.8801 15.3864 15.2223 15.8259C14.5645 16.2654 13.7911 16.5 13 16.5C11.9391 16.5 10.9217 16.0786 10.1716 15.3284C9.42143 14.5783 9.00001 13.5609 9.00001 12.5ZM21.22 20.8013C20.1047 19.1851 18.5365 17.9348 16.7125 17.2075C17.6923 16.4358 18.4072 15.378 18.7579 14.1811C19.1086 12.9843 19.0776 11.7079 18.6693 10.5294C18.2609 9.351 17.4955 8.32911 16.4794 7.60586C15.4634 6.88262 14.2472 6.49397 13 6.49397C11.7528 6.49397 10.5366 6.88262 9.52058 7.60586C8.50452 8.32911 7.73909 9.351 7.33074 10.5294C6.92238 11.7079 6.8914 12.9843 7.24209 14.1811C7.59279 15.378 8.30774 16.4358 9.28751 17.2075C7.46353 17.9348 5.89529 19.1851 4.78001 20.8013C3.37072 19.2165 2.4496 17.2581 2.12756 15.1619C1.80553 13.0657 2.09631 10.9211 2.9649 8.98637C3.83349 7.05163 5.24285 5.40922 7.02326 4.25692C8.80368 3.10462 10.8792 2.49156 13 2.49156C15.1208 2.49156 17.1963 3.10462 18.9768 4.25692C20.7572 5.40922 22.1665 7.05163 23.0351 8.98637C23.9037 10.9211 24.1945 13.0657 23.8724 15.1619C23.5504 17.2581 22.6293 19.2165 21.22 20.8013Z"
        fill="url(#paint0_linear_1462_536)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_1462_536"
          x1="13"
          y1="0.5"
          x2="13"
          y2="26.5"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={props.fill ?? '#FF8800'} />
          <stop offset="1" stopColor={props.fill ?? '#FFA033'} />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default UserCircleIcon;
