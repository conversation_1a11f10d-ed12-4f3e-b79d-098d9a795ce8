import React from 'react';

const BellIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 24 26" fill="none">
      <path
        d="M23.725 19.4925C23.0313 18.2975 22 14.9163 22 10.5C22 7.84784 20.9464 5.3043 19.0711 3.42893C17.1957 1.55357 14.6522 0.5 12 0.5C9.34785 0.5 6.80431 1.55357 4.92895 3.42893C3.05358 5.3043 2.00002 7.84784 2.00002 10.5C2.00002 14.9175 0.967516 18.2975 0.273766 19.4925C0.0966042 19.7963 0.00268396 20.1415 0.00147663 20.4931C0.000269303 20.8448 0.0918178 21.1906 0.266889 21.4956C0.441961 21.8006 0.694365 22.0541 0.998648 22.2304C1.30293 22.4068 1.64833 22.4997 2.00002 22.5H7.10127C7.33198 23.6289 7.94555 24.6436 8.83818 25.3722C9.73082 26.1009 10.8477 26.4989 12 26.4989C13.1523 26.4989 14.2692 26.1009 15.1618 25.3722C16.0545 24.6436 16.6681 23.6289 16.8988 22.5H22C22.3516 22.4995 22.6968 22.4064 23.0009 22.23C23.3051 22.0535 23.5573 21.8 23.7322 21.4951C23.9071 21.1901 23.9986 20.8444 23.9973 20.4928C23.996 20.1412 23.9021 19.7962 23.725 19.4925ZM12 24.5C11.3798 24.4998 10.7749 24.3074 10.2685 23.9492C9.76216 23.5911 9.37926 23.0848 9.17252 22.5H14.8275C14.6208 23.0848 14.2379 23.5911 13.7315 23.9492C13.2252 24.3074 12.6202 24.4998 12 24.5ZM2.00002 20.5C2.96252 18.845 4.00002 15.01 4.00002 10.5C4.00002 8.37827 4.84287 6.34344 6.34316 4.84315C7.84345 3.34285 9.87828 2.5 12 2.5C14.1217 2.5 16.1566 3.34285 17.6569 4.84315C19.1572 6.34344 20 8.37827 20 10.5C20 15.0063 21.035 18.8412 22 20.5H2.00002Z"
        fill="url(#paint0_linear_1462_528)"
      />

      <path
        d="M23.725 18.9925C23.0313 17.7975 22 14.4163 22 10C22 7.34784 20.9464 4.8043 19.0711 2.92893C17.1957 1.05357 14.6522 0 12 0C9.34785 0 6.80431 1.05357 4.92895 2.92893C3.05358 4.8043 2.00002 7.34784 2.00002 10C2.00002 14.4175 0.967516 17.7975 0.273766 18.9925C0.0966042 19.2963 0.00268396 19.6415 0.00147663 19.9931C0.000269303 20.3448 0.0918178 20.6906 0.266889 20.9956C0.441961 21.3006 0.694365 21.5541 0.998648 21.7304C1.30293 21.9068 1.64833 21.9997 2.00002 22H7.10127C7.33198 23.1289 7.94555 24.1436 8.83818 24.8722C9.73082 25.6009 10.8477 25.9989 12 25.9989C13.1523 25.9989 14.2692 25.6009 15.1618 24.8722C16.0545 24.1436 16.6681 23.1289 16.8988 22H22C22.3516 21.9995 22.6968 21.9064 23.0009 21.73C23.3051 21.5535 23.5573 21.3 23.7322 20.9951C23.9071 20.6901 23.9986 20.3444 23.9973 19.9928C23.996 19.6412 23.9021 19.2962 23.725 18.9925ZM12 24C11.3798 23.9998 10.7749 23.8074 10.2685 23.4492C9.76216 23.0911 9.37926 22.5848 9.17252 22H14.8275C14.6208 22.5848 14.2379 23.0911 13.7315 23.4492C13.2252 23.8074 12.6202 23.9998 12 24ZM2.00002 20C2.96252 18.345 4.00002 14.51 4.00002 10C4.00002 7.87827 4.84287 5.84344 6.34316 4.34315C7.84345 2.84285 9.87828 2 12 2C14.1217 2 16.1566 2.84285 17.6569 4.34315C19.1572 5.84344 20 7.87827 20 10C20 14.5063 21.035 18.3412 22 20H2.00002Z"
        fill={props.fill ? props.fill : '#666666'}
      />
    </svg>
  );
};

export default BellIcon;
