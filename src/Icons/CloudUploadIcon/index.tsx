import React from 'react';

const CloudUploadIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      viewBox="0 0 30 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M29.9999 10.9999C30.0062 13.3814 29.2336 15.6996 27.7999 17.6012C27.7209 17.7062 27.6221 17.7947 27.5089 17.8615C27.3958 17.9284 27.2705 17.9723 27.1404 17.9907C27.0103 18.0092 26.8778 18.0019 26.7505 17.9691C26.6233 17.9364 26.5037 17.8789 26.3986 17.7999C26.2936 17.721 26.2051 17.6221 26.1383 17.5089C26.0714 17.3958 26.0275 17.2706 26.0091 17.1405C25.9906 17.0103 25.9979 16.8779 26.0307 16.7506C26.0634 16.6233 26.1209 16.5037 26.1999 16.3987C27.3734 14.844 28.0056 12.9478 27.9999 10.9999C27.9999 8.61296 27.0517 6.32378 25.3638 4.63595C23.676 2.94813 21.3868 1.99991 18.9999 1.99991C16.6129 1.99991 14.3238 2.94813 12.6359 4.63595C10.9481 6.32378 9.99988 8.61296 9.99988 10.9999C9.99988 11.2651 9.89453 11.5195 9.70699 11.707C9.51945 11.8946 9.2651 11.9999 8.99988 11.9999C8.73467 11.9999 8.48031 11.8946 8.29278 11.707C8.10524 11.5195 7.99988 11.2651 7.99988 10.9999C7.9994 9.99076 8.13778 8.98634 8.41113 8.01491C8.27489 7.99991 8.13738 7.99991 7.99988 7.99991C6.40859 7.99991 4.88246 8.63205 3.75724 9.75727C2.63203 10.8825 1.99988 12.4086 1.99988 13.9999C1.99988 15.5912 2.63203 17.1173 3.75724 18.2426C4.88246 19.3678 6.40859 19.9999 7.99988 19.9999H10.9999C11.2651 19.9999 11.5195 20.1053 11.707 20.2928C11.8945 20.4803 11.9999 20.7347 11.9999 20.9999C11.9999 21.2651 11.8945 21.5195 11.707 21.707C11.5195 21.8946 11.2651 21.9999 10.9999 21.9999H7.99988C6.9001 22.0002 5.81208 21.7737 4.80377 21.3345C3.79546 20.8954 2.88853 20.2531 2.1396 19.4477C1.39068 18.6423 0.815854 17.6912 0.451032 16.6537C0.0862096 15.6162 -0.0607747 14.5146 0.0192589 13.4177C0.0992925 12.3209 0.404625 11.2523 0.916186 10.2787C1.42775 9.30516 2.13455 8.44754 2.99245 7.7594C3.85035 7.07127 4.84092 6.56741 5.90229 6.2793C6.96366 5.9912 8.07304 5.92502 9.16113 6.08491C10.2691 3.86886 12.0928 2.09176 14.3368 1.04145C16.5808 -0.00885046 19.1136 -0.27084 21.525 0.29792C23.9365 0.86668 26.0853 2.23288 27.6234 4.17522C29.1616 6.11757 29.9989 8.5223 29.9999 10.9999ZM18.7074 10.2924C18.6145 10.1994 18.5042 10.1257 18.3828 10.0754C18.2614 10.025 18.1313 9.99913 17.9999 9.99913C17.8685 9.99913 17.7383 10.025 17.6169 10.0754C17.4955 10.1257 17.3853 10.1994 17.2924 10.2924L13.2924 14.2924C13.1995 14.3853 13.1258 14.4956 13.0755 14.617C13.0252 14.7384 12.9993 14.8685 12.9993 14.9999C12.9993 15.1313 13.0252 15.2614 13.0755 15.3828C13.1258 15.5042 13.1995 15.6145 13.2924 15.7074C13.48 15.8951 13.7345 16.0005 13.9999 16.0005C14.1313 16.0005 14.2614 15.9746 14.3828 15.9243C14.5042 15.874 14.6145 15.8003 14.7074 15.7074L16.9999 13.4137V20.9999C16.9999 21.2651 17.1052 21.5195 17.2928 21.707C17.4803 21.8946 17.7347 21.9999 17.9999 21.9999C18.2651 21.9999 18.5195 21.8946 18.707 21.707C18.8945 21.5195 18.9999 21.2651 18.9999 20.9999V13.4137L21.2924 15.7074C21.3853 15.8003 21.4956 15.874 21.617 15.9243C21.7384 15.9746 21.8685 16.0005 21.9999 16.0005C22.1313 16.0005 22.2614 15.9746 22.3828 15.9243C22.5042 15.874 22.6145 15.8003 22.7074 15.7074C22.8003 15.6145 22.874 15.5042 22.9243 15.3828C22.9746 15.2614 23.0004 15.1313 23.0004 14.9999C23.0004 14.8685 22.9746 14.7384 22.9243 14.617C22.874 14.4956 22.8003 14.3853 22.7074 14.2924L18.7074 10.2924Z"
        fill={props.fill ? props.fill : '#666666'}
      />
    </svg>
  );
};

export default CloudUploadIcon;
