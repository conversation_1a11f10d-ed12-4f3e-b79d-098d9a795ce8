import React from 'react';

const Location2Icon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      viewBox="0 0 22 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20 26H13.8175C14.8561 25.0727 15.8355 24.0811 16.75 23.0312C20.1812 19.085 22 14.925 22 11C22 8.08262 20.8411 5.28473 18.7782 3.22183C16.7153 1.15893 13.9174 0 11 0C8.08262 0 5.28473 1.15893 3.22183 3.22183C1.15893 5.28473 0 8.08262 0 11C0 14.925 1.81375 19.085 5.25 23.0312C6.16451 24.0811 7.14393 25.0727 8.1825 26H2C1.73478 26 1.48043 26.1054 1.29289 26.2929C1.10536 26.4804 1 26.7348 1 27C1 27.2652 1.10536 27.5196 1.29289 27.7071C1.48043 27.8946 1.73478 28 2 28H20C20.2652 28 20.5196 27.8946 20.7071 27.7071C20.8946 27.5196 21 27.2652 21 27C21 26.7348 20.8946 26.4804 20.7071 26.2929C20.5196 26.1054 20.2652 26 20 26ZM2 11C2 8.61305 2.94821 6.32387 4.63604 4.63604C6.32387 2.94821 8.61305 2 11 2C13.3869 2 15.6761 2.94821 17.364 4.63604C19.0518 6.32387 20 8.61305 20 11C20 18.1537 13.0662 24.125 11 25.75C8.93375 24.125 2 18.1537 2 11ZM16 11C16 10.0111 15.7068 9.04439 15.1573 8.22215C14.6079 7.3999 13.827 6.75904 12.9134 6.3806C11.9998 6.00216 10.9945 5.90315 10.0245 6.09607C9.05464 6.289 8.16373 6.7652 7.46447 7.46447C6.7652 8.16373 6.289 9.05464 6.09607 10.0245C5.90315 10.9945 6.00216 11.9998 6.3806 12.9134C6.75904 13.827 7.3999 14.6079 8.22215 15.1573C9.04439 15.7068 10.0111 16 11 16C12.3261 16 13.5979 15.4732 14.5355 14.5355C15.4732 13.5979 16 12.3261 16 11ZM8 11C8 10.4067 8.17595 9.82664 8.50559 9.33329C8.83524 8.83994 9.30377 8.45542 9.85195 8.22836C10.4001 8.0013 11.0033 7.94189 11.5853 8.05764C12.1672 8.1734 12.7018 8.45912 13.1213 8.87868C13.5409 9.29824 13.8266 9.83279 13.9424 10.4147C14.0581 10.9967 13.9987 11.5999 13.7716 12.1481C13.5446 12.6962 13.1601 13.1648 12.6667 13.4944C12.1734 13.8241 11.5933 14 11 14C10.2044 14 9.44129 13.6839 8.87868 13.1213C8.31607 12.5587 8 11.7956 8 11Z"
        fill={props.fill ? props.fill : '#666666'}
      />
    </svg>
  );
};

export default Location2Icon;
