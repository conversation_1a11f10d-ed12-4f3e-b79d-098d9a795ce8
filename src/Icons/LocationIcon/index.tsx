import React from 'react';

const LocationIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      viewBox="0 0 22 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11 6C10.0111 6 9.04439 6.29325 8.22215 6.84265C7.3999 7.39206 6.75904 8.17295 6.3806 9.08658C6.00216 10.0002 5.90315 11.0055 6.09607 11.9755C6.289 12.9454 6.7652 13.8363 7.46447 14.5355C8.16373 15.2348 9.05464 15.711 10.0245 15.9039C10.9945 16.0969 11.9998 15.9978 12.9134 15.6194C13.827 15.241 14.6079 14.6001 15.1573 13.7779C15.7068 12.9556 16 11.9889 16 11C16 9.67392 15.4732 8.40215 14.5355 7.46447C13.5979 6.52678 12.3261 6 11 6ZM11 14C10.4067 14 9.82664 13.8241 9.33329 13.4944C8.83994 13.1648 8.45542 12.6962 8.22836 12.1481C8.0013 11.5999 7.94189 10.9967 8.05764 10.4147C8.1734 9.83279 8.45912 9.29824 8.87868 8.87868C9.29824 8.45912 9.83279 8.1734 10.4147 8.05764C10.9967 7.94189 11.5999 8.0013 12.1481 8.22836C12.6962 8.45542 13.1648 8.83994 13.4944 9.33329C13.8241 9.82664 14 10.4067 14 11C14 11.7956 13.6839 12.5587 13.1213 13.1213C12.5587 13.6839 11.7956 14 11 14ZM11 0C8.08363 0.00330842 5.28766 1.1633 3.22548 3.22548C1.1633 5.28766 0.00330842 8.08363 0 11C0 14.925 1.81375 19.085 5.25 23.0312C6.79403 24.8145 8.53182 26.4202 10.4312 27.8188C10.5994 27.9365 10.7997 27.9997 11.005 27.9997C11.2103 27.9997 11.4106 27.9365 11.5788 27.8188C13.4747 26.4196 15.2091 24.8139 16.75 23.0312C20.1812 19.085 22 14.925 22 11C21.9967 8.08363 20.8367 5.28766 18.7745 3.22548C16.7123 1.1633 13.9164 0.00330842 11 0ZM11 25.75C8.93375 24.125 2 18.1562 2 11C2 8.61305 2.94821 6.32387 4.63604 4.63604C6.32387 2.94821 8.61305 2 11 2C13.3869 2 15.6761 2.94821 17.364 4.63604C19.0518 6.32387 20 8.61305 20 11C20 18.1537 13.0662 24.125 11 25.75Z"
        fill={props.fill ? props.fill : '#666666'}
      />
    </svg>
  );
};

export default LocationIcon;
