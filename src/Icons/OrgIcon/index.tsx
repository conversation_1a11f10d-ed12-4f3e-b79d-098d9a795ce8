import React from 'react';

const OrgIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      viewBox="0 0 30 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M29 24H27V9.99996C27 9.46952 26.7893 8.96082 26.4142 8.58574C26.0391 8.21067 25.5304 7.99996 25 7.99996H17V1.99996C17.0002 1.63779 16.9021 1.28235 16.7161 0.971582C16.5301 0.660816 16.2633 0.406384 15.944 0.235445C15.6247 0.0645057 15.265 -0.0165253 14.9032 0.00100081C14.5415 0.018527 14.1913 0.133952 13.89 0.334957L3.89 6.99996C3.61567 7.18298 3.39086 7.431 3.23558 7.72193C3.0803 8.01287 2.99937 8.33768 3 8.66746V24H1C0.734784 24 0.48043 24.1053 0.292893 24.2929C0.105357 24.4804 0 24.7347 0 25C0 25.2652 0.105357 25.5195 0.292893 25.7071C0.48043 25.8946 0.734784 26 1 26H29C29.2652 26 29.5196 25.8946 29.7071 25.7071C29.8946 25.5195 30 25.2652 30 25C30 24.7347 29.8946 24.4804 29.7071 24.2929C29.5196 24.1053 29.2652 24 29 24ZM25 9.99996V24H17V9.99996H25ZM5 8.66746L15 1.99996V24H5V8.66746ZM13 12V14C13 14.2652 12.8946 14.5195 12.7071 14.7071C12.5196 14.8946 12.2652 15 12 15C11.7348 15 11.4804 14.8946 11.2929 14.7071C11.1054 14.5195 11 14.2652 11 14V12C11 11.7347 11.1054 11.4804 11.2929 11.2929C11.4804 11.1053 11.7348 11 12 11C12.2652 11 12.5196 11.1053 12.7071 11.2929C12.8946 11.4804 13 11.7347 13 12ZM9 12V14C9 14.2652 8.89464 14.5195 8.70711 14.7071C8.51957 14.8946 8.26522 15 8 15C7.73478 15 7.48043 14.8946 7.29289 14.7071C7.10536 14.5195 7 14.2652 7 14V12C7 11.7347 7.10536 11.4804 7.29289 11.2929C7.48043 11.1053 7.73478 11 8 11C8.26522 11 8.51957 11.1053 8.70711 11.2929C8.89464 11.4804 9 11.7347 9 12ZM9 19V21C9 21.2652 8.89464 21.5195 8.70711 21.7071C8.51957 21.8946 8.26522 22 8 22C7.73478 22 7.48043 21.8946 7.29289 21.7071C7.10536 21.5195 7 21.2652 7 21V19C7 18.7347 7.10536 18.4804 7.29289 18.2929C7.48043 18.1053 7.73478 18 8 18C8.26522 18 8.51957 18.1053 8.70711 18.2929C8.89464 18.4804 9 18.7347 9 19ZM13 19V21C13 21.2652 12.8946 21.5195 12.7071 21.7071C12.5196 21.8946 12.2652 22 12 22C11.7348 22 11.4804 21.8946 11.2929 21.7071C11.1054 21.5195 11 21.2652 11 21V19C11 18.7347 11.1054 18.4804 11.2929 18.2929C11.4804 18.1053 11.7348 18 12 18C12.2652 18 12.5196 18.1053 12.7071 18.2929C12.8946 18.4804 13 18.7347 13 19Z"
        fill={props.fill ? props.fill : '#666666'}
      />
    </svg>
  );
};

export default OrgIcon;
