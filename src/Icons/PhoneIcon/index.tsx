import React from 'react';

const PhoneIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      viewBox="0 0 25 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M23.7962 16.8075L17.9075 14.1687L17.8913 14.1612C17.5855 14.0305 17.2521 13.978 16.921 14.0086C16.5899 14.0391 16.2716 14.1517 15.995 14.3362C15.9624 14.3578 15.9311 14.3811 15.9013 14.4062L12.8587 17C10.9312 16.0637 8.94125 14.0887 8.005 12.1862L10.6025 9.09749C10.6275 9.06624 10.6513 9.03499 10.6738 9.00124C10.8543 8.72537 10.9638 8.40914 10.9926 8.08071C11.0214 7.75227 10.9685 7.42181 10.8388 7.11874V7.10374L8.1925 1.20499C8.02092 0.809072 7.72591 0.47926 7.35148 0.264789C6.97706 0.0503186 6.54332 -0.0373076 6.115 0.0149912C4.42121 0.237875 2.86647 1.0697 1.74117 2.35511C0.615859 3.64052 -0.00306237 5.2916 1.05129e-07 6.99999C1.05129e-07 16.925 8.075 25 18 25C19.7084 25.0031 21.3595 24.3841 22.6449 23.2588C23.9303 22.1335 24.7621 20.5788 24.985 18.885C25.0374 18.4568 24.95 18.0232 24.7357 17.6488C24.5215 17.2744 24.1919 16.9793 23.7962 16.8075ZM18 23C13.758 22.9954 9.69099 21.3082 6.69141 18.3086C3.69183 15.309 2.00463 11.242 2 6.99999C1.9953 5.77935 2.43506 4.59874 3.23717 3.67862C4.03928 2.75851 5.14888 2.16182 6.35875 1.99999C6.35826 2.00498 6.35826 2.01 6.35875 2.01499L8.98375 7.88999L6.4 10.9825C6.37378 11.0127 6.34995 11.0448 6.32875 11.0787C6.14064 11.3674 6.03029 11.6997 6.00839 12.0436C5.98649 12.3874 6.05379 12.7311 6.20375 13.0412C7.33625 15.3575 9.67 17.6737 12.0112 18.805C12.3237 18.9536 12.6694 19.0185 13.0144 18.9935C13.3595 18.9686 13.6922 18.8545 13.98 18.6625C14.0121 18.6409 14.043 18.6175 14.0725 18.5925L17.1113 16L22.9863 18.6312C22.9863 18.6312 22.9963 18.6312 23 18.6312C22.8401 19.8428 22.2443 20.9547 21.3241 21.7588C20.4038 22.5628 19.2221 23.0041 18 23Z"
        fill={props.fill ? props.fill : '#666666'}
      />
    </svg>
  );
};

export default PhoneIcon;
