import React from 'react';

const LicenceApproveIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 28 28" fill="none">
      <path
        d="M26.2325 10.8525C25.7612 10.36 25.2738 9.8525 25.09 9.40625C24.92 8.9975 24.91 8.32 24.9 7.66375C24.8813 6.44375 24.8612 5.06125 23.9 4.1C22.9387 3.13875 21.5562 3.11875 20.3363 3.1C19.68 3.09 19.0025 3.08 18.5938 2.91C18.1488 2.72625 17.64 2.23875 17.1475 1.7675C16.285 0.93875 15.305 0 14 0C12.695 0 11.7162 0.93875 10.8525 1.7675C10.36 2.23875 9.8525 2.72625 9.40625 2.91C9 3.08 8.32 3.09 7.66375 3.1C6.44375 3.11875 5.06125 3.13875 4.1 4.1C3.13875 5.06125 3.125 6.44375 3.1 7.66375C3.09 8.32 3.08 8.9975 2.91 9.40625C2.72625 9.85125 2.23875 10.36 1.7675 10.8525C0.93875 11.715 0 12.695 0 14C0 15.305 0.93875 16.2837 1.7675 17.1475C2.23875 17.64 2.72625 18.1475 2.91 18.5938C3.08 19.0025 3.09 19.68 3.1 20.3363C3.11875 21.5562 3.13875 22.9387 4.1 23.9C5.06125 24.8612 6.44375 24.8813 7.66375 24.9C8.32 24.91 8.9975 24.92 9.40625 25.09C9.85125 25.2738 10.36 25.7612 10.8525 26.2325C11.715 27.0613 12.695 28 14 28C15.305 28 16.2837 27.0613 17.1475 26.2325C17.64 25.7612 18.1475 25.2738 18.5938 25.09C19.0025 24.92 19.68 24.91 20.3363 24.9C21.5562 24.8813 22.9387 24.8612 23.9 23.9C24.8612 22.9387 24.8813 21.5562 24.9 20.3363C24.91 19.68 24.92 19.0025 25.09 18.5938C25.2738 18.1488 25.7612 17.64 26.2325 17.1475C27.0613 16.285 28 15.305 28 14C28 12.695 27.0613 11.7162 26.2325 10.8525ZM24.7887 15.7638C24.19 16.3888 23.57 17.035 23.2412 17.8288C22.9262 18.5913 22.9125 19.4625 22.9 20.3062C22.8875 21.1812 22.8738 22.0975 22.485 22.485C22.0963 22.8725 21.1862 22.8875 20.3062 22.9C19.4625 22.9125 18.5913 22.9262 17.8288 23.2412C17.035 23.57 16.3888 24.19 15.7638 24.7887C15.1388 25.3875 14.5 26 14 26C13.5 26 12.8562 25.385 12.2362 24.7887C11.6163 24.1925 10.965 23.57 10.1713 23.2412C9.40875 22.9262 8.5375 22.9125 7.69375 22.9C6.81875 22.8875 5.9025 22.8738 5.515 22.485C5.1275 22.0963 5.1125 21.1862 5.1 20.3062C5.0875 19.4625 5.07375 18.5913 4.75875 17.8288C4.43 17.035 3.81 16.3888 3.21125 15.7638C2.6125 15.1388 2 14.5 2 14C2 13.5 2.615 12.8562 3.21125 12.2362C3.8075 11.6163 4.43 10.965 4.75875 10.1713C5.07375 9.40875 5.0875 8.5375 5.1 7.69375C5.1125 6.81875 5.12625 5.9025 5.515 5.515C5.90375 5.1275 6.81375 5.1125 7.69375 5.1C8.5375 5.0875 9.40875 5.07375 10.1713 4.75875C10.965 4.43 11.6112 3.81 12.2362 3.21125C12.8612 2.6125 13.5 2 14 2C14.5 2 15.1438 2.615 15.7638 3.21125C16.3838 3.8075 17.035 4.43 17.8288 4.75875C18.5913 5.07375 19.4625 5.0875 20.3062 5.1C21.1812 5.1125 22.0975 5.12625 22.485 5.515C22.8725 5.90375 22.8875 6.81375 22.9 7.69375C22.9125 8.5375 22.9262 9.40875 23.2412 10.1713C23.57 10.965 24.19 11.6112 24.7887 12.2362C25.3875 12.8612 26 13.5 26 14C26 14.5 25.385 15.1438 24.7887 15.7638ZM15.5 20.5C15.5 20.7967 15.412 21.0867 15.2472 21.3334C15.0824 21.58 14.8481 21.7723 14.574 21.8858C14.2999 21.9993 13.9983 22.0291 13.7074 21.9712C13.4164 21.9133 13.1491 21.7704 12.9393 21.5607C12.7296 21.3509 12.5867 21.0836 12.5288 20.7926C12.4709 20.5017 12.5006 20.2001 12.6142 19.926C12.7277 19.6519 12.92 19.4176 13.1666 19.2528C13.4133 19.088 13.7033 19 14 19C14.3978 19 14.7794 19.158 15.0607 19.4393C15.342 19.7206 15.5 20.1022 15.5 20.5ZM19 11.5C19 13.6725 17.28 15.4913 15 15.91V16C15 16.2652 14.8946 16.5196 14.7071 16.7071C14.5196 16.8946 14.2652 17 14 17C13.7348 17 13.4804 16.8946 13.2929 16.7071C13.1054 16.5196 13 16.2652 13 16V15C13 14.7348 13.1054 14.4804 13.2929 14.2929C13.4804 14.1054 13.7348 14 14 14C15.6537 14 17 12.875 17 11.5C17 10.125 15.6537 9 14 9C12.3463 9 11 10.125 11 11.5V12C11 12.2652 10.8946 12.5196 10.7071 12.7071C10.5196 12.8946 10.2652 13 10 13C9.73478 13 9.48043 12.8946 9.29289 12.7071C9.10536 12.5196 9 12.2652 9 12V11.5C9 9.01875 11.2425 7 14 7C16.7575 7 19 9.01875 19 11.5Z"
        fill={props?.fill ?? '#333333'}
      />
    </svg>
  );
};

export default LicenceApproveIcon;
