import React from 'react';

// Define the type for the component props
interface UploadIconProps extends React.SVGProps<SVGSVGElement> {
  width?: string | number;
  height?: string | number;
  fill?: string;
}

const UploadIcon: React.FC<UploadIconProps> = (props) => {
  return (
    <svg {...props} viewBox="0 0 24 24" fill="#FF8800">
      <path
        d="M24 14.9998V21.9998C24 22.5302 23.7893 23.0389 23.4142 23.414C23.0391 23.7891 22.5304 23.9998 22 23.9998H2C1.46957 23.9998 0.960859 23.7891 0.585786 23.414C0.210714 23.0389 0 22.5302 0 21.9998V14.9998C0 14.7346 0.105357 14.4802 0.292893 14.2927C0.48043 14.1052 0.734784 13.9998 1 13.9998C1.26522 13.9998 1.51957 14.1052 1.70711 14.2927C1.89464 14.4802 2 14.7346 2 14.9998V21.9998H22V14.9998C22 14.7346 22.1054 14.4802 22.2929 14.2927C22.4804 14.1052 22.7348 13.9998 23 13.9998C23.2652 13.9998 23.5196 14.1052 23.7071 14.2927C23.8946 14.4802 24 14.7346 24 14.9998ZM7.7075 6.70731L11 3.41356V14.9998C11 15.265 11.1054 15.5194 11.2929 15.7069C11.4804 15.8945 11.7348 15.9998 12 15.9998C12.2652 15.9998 12.5196 15.8945 12.7071 15.7069C12.8946 15.5194 13 15.265 13 14.9998V3.41356L16.2925 6.70731C16.4801 6.89495 16.7346 7.00037 17 7.00037C17.2654 7.00037 17.5199 6.89495 17.7075 6.70731C17.8951 6.51967 18.0006 6.26517 18.0006 5.99981C18.0006 5.73445 17.8951 5.47995 17.7075 5.29231L12.7075 0.29231C12.6146 0.199334 12.5043 0.125575 12.3829 0.0752504C12.2615 0.0249261 12.1314 -0.000976562 12 -0.000976562C11.8686 -0.000976562 11.7385 0.0249261 11.6171 0.0752504C11.4957 0.125575 11.3854 0.199334 11.2925 0.29231L6.2925 5.29231C6.10486 5.47995 5.99944 5.73445 5.99944 5.99981C5.99944 6.26517 6.10486 6.51967 6.2925 6.70731C6.48014 6.89495 6.73464 7.00037 7 7.00037C7.26536 7.00037 7.51986 6.89495 7.7075 6.70731Z"
        fill="url(#paint0_linear_1399_1700)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_1399_1700"
          x1="12"
          y1="-0.000976563"
          x2="12"
          y2="23.9998"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FF8800" />
          <stop offset="1" stopColor="#FF8800" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default UploadIcon;
