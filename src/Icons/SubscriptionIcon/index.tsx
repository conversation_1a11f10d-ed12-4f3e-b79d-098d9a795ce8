import React from 'react';

const SubscriptionIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      viewBox="0 0 30 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15 4C14.0111 4 13.0444 4.29325 12.2221 4.84265C11.3999 5.39206 10.759 6.17295 10.3806 7.08658C10.0022 8.00021 9.90315 9.00555 10.0961 9.97545C10.289 10.9454 10.7652 11.8363 11.4645 12.5355C12.1637 13.2348 13.0546 13.711 14.0245 13.9039C14.9945 14.0969 15.9998 13.9978 16.9134 13.6194C17.827 13.241 18.6079 12.6001 19.1573 11.7779C19.7068 10.9556 20 9.98891 20 9C20 7.67392 19.4732 6.40215 18.5355 5.46447C17.5979 4.52678 16.3261 4 15 4ZM15 12C14.4067 12 13.8266 11.8241 13.3333 11.4944C12.8399 11.1648 12.4554 10.6962 12.2284 10.1481C12.0013 9.59987 11.9419 8.99667 12.0576 8.41473C12.1734 7.83279 12.4591 7.29824 12.8787 6.87868C13.2982 6.45912 13.8328 6.1734 14.4147 6.05764C14.9967 5.94189 15.5999 6.0013 16.1481 6.22836C16.6962 6.45542 17.1648 6.83994 17.4944 7.33329C17.8241 7.82664 18 8.40666 18 9C18 9.79565 17.6839 10.5587 17.1213 11.1213C16.5587 11.6839 15.7956 12 15 12ZM29 0H1C0.734784 0 0.48043 0.105357 0.292893 0.292893C0.105357 0.48043 0 0.734784 0 1V17C0 17.2652 0.105357 17.5196 0.292893 17.7071C0.48043 17.8946 0.734784 18 1 18H29C29.2652 18 29.5196 17.8946 29.7071 17.7071C29.8946 17.5196 30 17.2652 30 17V1C30 0.734784 29.8946 0.48043 29.7071 0.292893C29.5196 0.105357 29.2652 0 29 0ZM23.2062 16H6.79375C6.45801 14.8645 5.84351 13.8311 5.00623 12.9938C4.16895 12.1565 3.1355 11.542 2 11.2062V6.79375C3.1355 6.45801 4.16895 5.84351 5.00623 5.00623C5.84351 4.16895 6.45801 3.1355 6.79375 2H23.2062C23.542 3.1355 24.1565 4.16895 24.9938 5.00623C25.8311 5.84351 26.8645 6.45801 28 6.79375V11.2062C26.8645 11.542 25.8311 12.1565 24.9938 12.9938C24.1565 13.8311 23.542 14.8645 23.2062 16ZM28 4.67125C26.8005 4.15549 25.8445 3.19945 25.3288 2H28V4.67125ZM4.67125 2C4.15549 3.19945 3.19945 4.15549 2 4.67125V2H4.67125ZM2 13.3288C3.19945 13.8445 4.15549 14.8005 4.67125 16H2V13.3288ZM25.3288 16C25.8445 14.8005 26.8005 13.8445 28 13.3288V16H25.3288Z"
        fill={props.fill ? props.fill : '#666666'}
      />
    </svg>
  );
};

export default SubscriptionIcon;
