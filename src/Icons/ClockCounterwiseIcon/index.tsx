import React from 'react';

const ClockCounterWiseIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 25 24" fill="none">
      <path
        d="M14 5.99998V11.4337L18.515 14.1425C18.7424 14.2791 18.9063 14.5004 18.9705 14.7578C19.0347 15.0152 18.9941 15.2876 18.8575 15.515C18.7209 15.7424 18.4996 15.9062 18.2422 15.9705C17.9848 16.0347 17.7124 15.9941 17.485 15.8575L12.485 12.8575C12.337 12.7686 12.2146 12.6429 12.1296 12.4926C12.0446 12.3423 11.9999 12.1726 12 12V5.99998C12 5.73476 12.1054 5.48041 12.2929 5.29287C12.4804 5.10533 12.7348 4.99998 13 4.99998C13.2652 4.99998 13.5196 5.10533 13.7071 5.29287C13.8946 5.48041 14 5.73476 14 5.99998ZM13 -2.40211e-05C11.4225 -0.00395346 9.85987 0.305018 8.40257 0.90901C6.94527 1.513 5.62222 2.40002 4.51 3.51873C3.60125 4.43873 2.79375 5.32373 2 6.24998V3.99998C2 3.73476 1.89464 3.48041 1.70711 3.29287C1.51957 3.10533 1.26522 2.99998 1 2.99998C0.734784 2.99998 0.48043 3.10533 0.292893 3.29287C0.105357 3.48041 0 3.73476 0 3.99998V8.99998C0 9.26519 0.105357 9.51955 0.292893 9.70708C0.48043 9.89462 0.734784 9.99998 1 9.99998H6C6.26522 9.99998 6.51957 9.89462 6.70711 9.70708C6.89464 9.51955 7 9.26519 7 8.99998C7 8.73476 6.89464 8.48041 6.70711 8.29287C6.51957 8.10533 6.26522 7.99998 6 7.99998H3.125C4.01875 6.94748 4.90875 5.95623 5.92375 4.92873C7.31357 3.53892 9.08213 2.58949 11.0085 2.19907C12.9348 1.80866 14.9335 1.99457 16.7547 2.73358C18.576 3.47259 20.1391 4.73193 21.2487 6.35424C22.3584 7.97654 22.9653 9.88988 22.9938 11.8552C23.0222 13.8205 22.4708 15.7505 21.4086 17.4043C20.3463 19.058 18.8203 20.362 17.0212 21.1534C15.2221 21.9448 13.2296 22.1884 11.2928 21.8539C9.35598 21.5194 7.56069 20.6215 6.13125 19.2725C6.03571 19.1822 5.92333 19.1116 5.80052 19.0648C5.6777 19.0179 5.54686 18.9957 5.41547 18.9994C5.28407 19.0031 5.15469 19.0327 5.03472 19.0864C4.91475 19.1401 4.80653 19.2169 4.71625 19.3125C4.62597 19.408 4.55538 19.5204 4.50853 19.6432C4.46168 19.766 4.43948 19.8969 4.44319 20.0283C4.44691 20.1597 4.47647 20.289 4.53018 20.409C4.58389 20.529 4.66071 20.6372 4.75625 20.7275C6.18056 22.0716 7.91219 23.0466 9.8 23.5676C11.6878 24.0886 13.6744 24.1396 15.5865 23.7162C17.4986 23.2928 19.278 22.4079 20.7694 21.1386C22.2608 19.8694 23.4189 18.2545 24.1427 16.4347C24.8664 14.615 25.1338 12.6458 24.9215 10.699C24.7091 8.75212 24.0236 6.88684 22.9246 5.26589C21.8256 3.64495 20.3466 2.3176 18.6166 1.3997C16.8867 0.481807 14.9584 0.00125064 13 -2.40211e-05Z"
        fill={props.fill ?? '#343330'}
      />
    </svg>
  );
};

export default ClockCounterWiseIcon;
