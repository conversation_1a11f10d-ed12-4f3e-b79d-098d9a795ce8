import React from 'react';

const EmailIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 26 20" fill="none">
      <path
        d="M25 0H1C0.734784 0 0.48043 0.105357 0.292893 0.292893C0.105357 0.48043 0 0.734784 0 1V18C0 18.5304 0.210714 19.0391 0.585786 19.4142C0.960859 19.7893 1.46957 20 2 20H24C24.5304 20 25.0391 19.7893 25.4142 19.4142C25.7893 19.0391 26 18.5304 26 18V1C26 0.734784 25.8946 0.48043 25.7071 0.292893C25.5196 0.105357 25.2652 0 25 0ZM13 10.6437L3.57125 2H22.4287L13 10.6437ZM9.33875 10L2 16.7262V3.27375L9.33875 10ZM10.8188 11.3563L12.3188 12.7375C12.5032 12.9069 12.7446 13.0008 12.995 13.0008C13.2454 13.0008 13.4868 12.9069 13.6712 12.7375L15.1712 11.3563L22.4212 18H3.57125L10.8188 11.3563ZM16.6612 10L24 3.2725V16.7275L16.6612 10Z"
        fill={props.fill ?? '#343330'}
      />
    </svg>
  );
};

export default EmailIcon;
