import React from 'react';

const RoboIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 26 27" fill="none">
      <path
        d="M22 5H14V1C14 0.734784 13.8946 0.48043 13.7071 0.292893C13.5196 0.105357 13.2652 0 13 0C12.7348 0 12.4804 0.105357 12.2929 0.292893C12.1054 0.48043 12 0.734784 12 1V5H4C2.93913 5 1.92172 5.42143 1.17157 6.17157C0.421427 6.92172 0 7.93913 0 9V23C0 24.0609 0.421427 25.0783 1.17157 25.8284C1.92172 26.5786 2.93913 27 4 27H22C23.0609 27 24.0783 26.5786 24.8284 25.8284C25.5786 25.0783 26 24.0609 26 23V9C26 7.93913 25.5786 6.92172 24.8284 6.17157C24.0783 5.42143 23.0609 5 22 5ZM24 23C24 23.5304 23.7893 24.0391 23.4142 24.4142C23.0391 24.7893 22.5304 25 22 25H4C3.46957 25 2.96086 24.7893 2.58579 24.4142C2.21071 24.0391 2 23.5304 2 23V9C2 8.46957 2.21071 7.96086 2.58579 7.58579C2.96086 7.21071 3.46957 7 4 7H22C22.5304 7 23.0391 7.21071 23.4142 7.58579C23.7893 7.96086 24 8.46957 24 9V23ZM17.5 16H8.5C7.57174 16 6.6815 16.3687 6.02513 17.0251C5.36875 17.6815 5 18.5717 5 19.5C5 20.4283 5.36875 21.3185 6.02513 21.9749C6.6815 22.6313 7.57174 23 8.5 23H17.5C18.4283 23 19.3185 22.6313 19.9749 21.9749C20.6313 21.3185 21 20.4283 21 19.5C21 18.5717 20.6313 17.6815 19.9749 17.0251C19.3185 16.3687 18.4283 16 17.5 16ZM14 18V21H12V18H14ZM7 19.5C7 19.1022 7.15804 18.7206 7.43934 18.4393C7.72064 18.158 8.10218 18 8.5 18H10V21H8.5C8.10218 21 7.72064 20.842 7.43934 20.5607C7.15804 20.2794 7 19.8978 7 19.5ZM17.5 21H16V18H17.5C17.8978 18 18.2794 18.158 18.5607 18.4393C18.842 18.7206 19 19.1022 19 19.5C19 19.8978 18.842 20.2794 18.5607 20.5607C18.2794 20.842 17.8978 21 17.5 21ZM6 12.5C6 12.2033 6.08797 11.9133 6.2528 11.6666C6.41762 11.42 6.65189 11.2277 6.92597 11.1142C7.20006 11.0006 7.50166 10.9709 7.79264 11.0288C8.08361 11.0867 8.35088 11.2296 8.56066 11.4393C8.77044 11.6491 8.9133 11.9164 8.97118 12.2074C9.02906 12.4983 8.99935 12.7999 8.88582 13.074C8.77229 13.3481 8.58003 13.5824 8.33335 13.7472C8.08668 13.912 7.79667 14 7.5 14C7.10218 14 6.72064 13.842 6.43934 13.5607C6.15804 13.2794 6 12.8978 6 12.5ZM17 12.5C17 12.2033 17.088 11.9133 17.2528 11.6666C17.4176 11.42 17.6519 11.2277 17.926 11.1142C18.2001 11.0006 18.5017 10.9709 18.7926 11.0288C19.0836 11.0867 19.3509 11.2296 19.5607 11.4393C19.7704 11.6491 19.9133 11.9164 19.9712 12.2074C20.0291 12.4983 19.9993 12.7999 19.8858 13.074C19.7723 13.3481 19.58 13.5824 19.3334 13.7472C19.0867 13.912 18.7967 14 18.5 14C18.1022 14 17.7206 13.842 17.4393 13.5607C17.158 13.2794 17 12.8978 17 12.5Z"
        fill={props.fill ?? '#343330'}
      />
    </svg>
  );
};

export default RoboIcon;
