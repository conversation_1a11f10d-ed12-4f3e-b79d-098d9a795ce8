import React from 'react';

const DeleteAccIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      viewBox="0 0 26 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M18 4.00001C18 3.7348 18.1054 3.48044 18.2929 3.29291C18.4805 3.10537 18.7348 3.00001 19 3.00001H25C25.2652 3.00001 25.5196 3.10537 25.7071 3.29291C25.8947 3.48044 26 3.7348 26 4.00001C26 4.26523 25.8947 4.51958 25.7071 4.70712C25.5196 4.89466 25.2652 5.00001 25 5.00001H19C18.7348 5.00001 18.4805 4.89466 18.2929 4.70712C18.1054 4.51958 18 4.26523 18 4.00001ZM25.82 10.835C26.2769 13.5507 25.8589 16.3413 24.6263 18.804C23.3938 21.2666 21.4105 23.2738 18.9628 24.5358C16.5151 25.7978 13.7297 26.2492 11.0087 25.8249C8.28773 25.4006 5.77199 24.1226 3.82472 22.1753C1.87744 20.2281 0.599406 17.7123 0.175128 14.9913C-0.249149 12.2704 0.202289 9.48494 1.46426 7.03726C2.72624 4.58958 4.73344 2.60628 7.19608 1.37373C9.65871 0.141183 12.4493 -0.276842 15.165 0.180014C15.4246 0.22592 15.6556 0.372467 15.8077 0.587778C15.9598 0.803088 16.0207 1.06974 15.9772 1.32974C15.9337 1.58974 15.7893 1.82204 15.5754 1.97613C15.3615 2.13021 15.0954 2.1936 14.835 2.15251C13.2577 1.88717 11.6415 1.96873 10.0989 2.39152C8.55627 2.81431 7.12432 3.56817 5.90268 4.60064C4.68104 5.63311 3.69906 6.91938 3.02508 8.36994C2.3511 9.82051 2.0013 11.4005 2.00003 13C1.99766 15.6928 2.98734 18.292 4.78003 20.3013C5.89532 18.6851 7.46356 17.4348 9.28753 16.7075C8.30776 15.9358 7.59282 14.878 7.24212 13.6811C6.89143 12.4843 6.92241 11.2079 7.33077 10.0294C7.73912 8.85101 8.50455 7.82912 9.52061 7.10588C10.5367 6.38263 11.7529 5.99399 13 5.99399C14.2472 5.99399 15.4634 6.38263 16.4795 7.10588C17.4955 7.82912 18.2609 8.85101 18.6693 10.0294C19.0777 11.2079 19.1086 12.4843 18.7579 13.6811C18.4072 14.878 17.6923 15.9358 16.7125 16.7075C18.5365 17.4348 20.1047 18.6851 21.22 20.3013C23.0127 18.292 24.0024 15.6928 24 13C24.0001 12.3852 23.9491 11.7714 23.8475 11.165C23.8245 11.0349 23.8275 10.9016 23.8564 10.7727C23.8852 10.6437 23.9393 10.5218 24.0155 10.4139C24.0917 10.306 24.1885 10.2143 24.3004 10.144C24.4123 10.0738 24.537 10.0264 24.6672 10.0046C24.7975 9.98278 24.9309 9.98701 25.0595 10.017C25.1881 10.0471 25.3096 10.1023 25.4168 10.1795C25.5239 10.2567 25.6148 10.3544 25.684 10.4669C25.7532 10.5794 25.7994 10.7045 25.82 10.835ZM13 16C13.7912 16 14.5645 15.7654 15.2223 15.3259C15.8801 14.8864 16.3928 14.2617 16.6956 13.5307C16.9983 12.7998 17.0775 11.9956 16.9232 11.2197C16.7688 10.4437 16.3879 9.731 15.8285 9.17159C15.269 8.61218 14.5563 8.23121 13.7804 8.07687C13.0045 7.92253 12.2002 8.00175 11.4693 8.3045C10.7384 8.60725 10.1137 9.11994 9.67416 9.77773C9.23463 10.4355 9.00003 11.2089 9.00003 12C9.00003 13.0609 9.42146 14.0783 10.1716 14.8284C10.9218 15.5786 11.9392 16 13 16ZM13 24C15.4417 24.0025 17.8142 23.1885 19.74 21.6875C19.0166 20.5561 18.02 19.625 16.8421 18.9801C15.6642 18.3351 14.3429 17.9971 13 17.9971C11.6571 17.9971 10.3358 18.3351 9.15794 18.9801C7.98004 19.625 6.98345 20.5561 6.26003 21.6875C8.1859 23.1885 10.5583 24.0025 13 24Z"
        fill={props.fill ? props.fill : '#666666'}
      />
    </svg>
  );
};

export default DeleteAccIcon;
