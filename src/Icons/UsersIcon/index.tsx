import React from 'react';

const UsersIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      viewBox="0 0 32 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.6563 13.7401C15.9966 12.8479 17.0141 11.548 17.5584 10.0327C18.1028 8.51739 18.145 6.86715 17.6789 5.32599C17.2129 3.78483 16.2632 2.43458 14.9703 1.4749C13.6775 0.515221 12.1102 -0.00292969 10.5001 -0.00292969C8.88997 -0.00292969 7.32261 0.515221 6.02978 1.4749C4.73694 2.43458 3.78726 3.78483 3.32118 5.32599C2.85509 6.86715 2.89734 8.51739 3.44168 10.0327C3.98603 11.548 5.00356 12.8479 6.34381 13.7401C3.91943 14.6337 1.84894 16.2872 0.441312 18.4539C0.367332 18.5638 0.315945 18.6874 0.290139 18.8174C0.264332 18.9474 0.264621 19.0812 0.290989 19.2111C0.317356 19.3409 0.369276 19.4643 0.44373 19.5739C0.518185 19.6835 0.613688 19.7773 0.72469 19.8497C0.835692 19.9221 0.959977 19.9717 1.09032 19.9956C1.22067 20.0196 1.35447 20.0174 1.48396 19.9892C1.61344 19.9609 1.73603 19.9073 1.84458 19.8312C1.95314 19.7552 2.04551 19.6584 2.11631 19.5464C3.0243 18.1498 4.26676 17.0023 5.73086 16.2078C7.19496 15.4134 8.83432 14.9973 10.5001 14.9973C12.1658 14.9973 13.8052 15.4134 15.2693 16.2078C16.7334 17.0023 17.9758 18.1498 18.8838 19.5464C19.0305 19.7644 19.257 19.9159 19.5145 19.9681C19.772 20.0204 20.0397 19.9692 20.2598 19.8257C20.4799 19.6822 20.6346 19.4578 20.6906 19.2011C20.7465 18.9444 20.6992 18.676 20.5588 18.4539C19.1512 16.2872 17.0807 14.6337 14.6563 13.7401ZM5.00006 7.50011C5.00006 6.41232 5.32263 5.34895 5.92698 4.44448C6.53133 3.54001 7.39031 2.83506 8.3953 2.41878C9.4003 2.00249 10.5062 1.89358 11.5731 2.10579C12.64 2.31801 13.62 2.84184 14.3891 3.61103C15.1583 4.38021 15.6822 5.36022 15.8944 6.42712C16.1066 7.49401 15.9977 8.59988 15.5814 9.60487C15.1651 10.6099 14.4602 11.4688 13.5557 12.0732C12.6512 12.6775 11.5879 13.0001 10.5001 13.0001C9.04188 12.9985 7.64389 12.4185 6.6128 11.3874C5.58171 10.3563 5.00172 8.9583 5.00006 7.50011ZM31.2676 19.8376C31.0454 19.9825 30.7749 20.0331 30.5154 19.9785C30.2559 19.9239 30.0287 19.7685 29.8838 19.5464C28.9769 18.149 27.7346 17.0009 26.2702 16.2068C24.8058 15.4127 23.1659 14.9979 21.5001 15.0001C21.2348 15.0001 20.9805 14.8948 20.793 14.7072C20.6054 14.5197 20.5001 14.2653 20.5001 14.0001C20.5001 13.7349 20.6054 13.4805 20.793 13.293C20.9805 13.1055 21.2348 13.0001 21.5001 13.0001C22.31 12.9993 23.1098 12.8197 23.8423 12.474C24.5748 12.1283 25.2219 11.6251 25.7373 11.0003C26.2528 10.3756 26.6239 9.64465 26.8242 8.85983C27.0244 8.07501 27.0488 7.25565 26.8957 6.4603C26.7426 5.66494 26.4157 4.91323 25.9383 4.25886C25.461 3.60449 24.845 3.06362 24.1344 2.6749C23.4239 2.28619 22.6362 2.05921 21.8277 2.0102C21.0192 1.96118 20.2099 2.09134 19.4576 2.39136C19.3349 2.44439 19.2028 2.47229 19.0692 2.47342C18.9356 2.47455 18.8031 2.44889 18.6795 2.39794C18.556 2.34699 18.4439 2.2718 18.3499 2.1768C18.2559 2.0818 18.1819 1.96893 18.1323 1.84485C18.0827 1.72077 18.0584 1.588 18.0609 1.45439C18.0635 1.32077 18.0928 1.18903 18.1471 1.06693C18.2015 0.944832 18.2797 0.834863 18.3772 0.743519C18.4748 0.652176 18.5897 0.581312 18.7151 0.535113C20.4369 -0.151573 22.352 -0.176284 24.091 0.465747C25.83 1.10778 27.2696 2.37106 28.1321 4.01188C28.9946 5.65271 29.2189 7.55482 28.7617 9.35126C28.3045 11.1477 27.1982 12.7112 25.6563 13.7401C28.0807 14.6337 30.1512 16.2872 31.5588 18.4539C31.7037 18.676 31.7543 18.9466 31.6997 19.2061C31.6451 19.4655 31.4897 19.6927 31.2676 19.8376Z"
        fill={props.fill ? props.fill : '#666666'}
      />
    </svg>
  );
};

export default UsersIcon;
