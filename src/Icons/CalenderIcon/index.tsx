import React from 'react';

const CalenderIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      viewBox="0 0 24 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M22 2H19V1C19 0.734784 18.8946 0.48043 18.7071 0.292893C18.5196 0.105357 18.2652 0 18 0C17.7348 0 17.4804 0.105357 17.2929 0.292893C17.1054 0.48043 17 0.734784 17 1V2H7V1C7 0.734784 6.89464 0.48043 6.70711 0.292893C6.51957 0.105357 6.26522 0 6 0C5.73478 0 5.48043 0.105357 5.29289 0.292893C5.10536 0.48043 5 0.734784 5 1V2H2C1.46957 2 0.960859 2.21071 0.585786 2.58579C0.210714 2.96086 0 3.46957 0 4V24C0 24.5304 0.210714 25.0391 0.585786 25.4142C0.960859 25.7893 1.46957 26 2 26H22C22.5304 26 23.0391 25.7893 23.4142 25.4142C23.7893 25.0391 24 24.5304 24 24V4C24 3.46957 23.7893 2.96086 23.4142 2.58579C23.0391 2.21071 22.5304 2 22 2ZM5 4V5C5 5.26522 5.10536 5.51957 5.29289 5.70711C5.48043 5.89464 5.73478 6 6 6C6.26522 6 6.51957 5.89464 6.70711 5.70711C6.89464 5.51957 7 5.26522 7 5V4H17V5C17 5.26522 17.1054 5.51957 17.2929 5.70711C17.4804 5.89464 17.7348 6 18 6C18.2652 6 18.5196 5.89464 18.7071 5.70711C18.8946 5.51957 19 5.26522 19 5V4H22V8H2V4H5ZM22 24H2V10H22V24ZM10 13V21C10 21.2652 9.89464 21.5196 9.70711 21.7071C9.51957 21.8946 9.26522 22 9 22C8.73478 22 8.48043 21.8946 8.29289 21.7071C8.10536 21.5196 8 21.2652 8 21V14.6175L7.4475 14.895C7.21013 15.0137 6.93534 15.0332 6.68357 14.9493C6.4318 14.8654 6.22368 14.6849 6.105 14.4475C5.98632 14.2101 5.96679 13.9353 6.05071 13.6836C6.13463 13.4318 6.31513 13.2237 6.5525 13.105L8.5525 12.105C8.70502 12.0287 8.87451 11.9926 9.04489 12.0003C9.21526 12.0079 9.38084 12.059 9.5259 12.1487C9.67096 12.2384 9.79066 12.3637 9.87364 12.5127C9.95663 12.6617 10.0001 12.8295 10 13ZM17.395 16.8062L15 20H17C17.2652 20 17.5196 20.1054 17.7071 20.2929C17.8946 20.4804 18 20.7348 18 21C18 21.2652 17.8946 21.5196 17.7071 21.7071C17.5196 21.8946 17.2652 22 17 22H13C12.8143 22 12.6322 21.9483 12.4743 21.8507C12.3163 21.753 12.1886 21.6133 12.1056 21.4472C12.0225 21.2811 11.9874 21.0952 12.004 20.9102C12.0207 20.7252 12.0886 20.5486 12.2 20.4L15.7975 15.6038C15.8793 15.4948 15.938 15.3703 15.97 15.2379C16.002 15.1054 16.0066 14.9678 15.9835 14.8336C15.9604 14.6993 15.9101 14.5712 15.8357 14.457C15.7612 14.3429 15.6643 14.2452 15.5508 14.1699C15.4372 14.0945 15.3095 14.0432 15.1754 14.019C15.0413 13.9949 14.9037 13.9983 14.771 14.0292C14.6383 14.0602 14.5134 14.1179 14.4038 14.1988C14.2942 14.2798 14.2023 14.3823 14.1338 14.5C14.07 14.6174 13.9834 14.7208 13.8791 14.8041C13.7748 14.8875 13.6548 14.9491 13.5263 14.9853C13.3977 15.0215 13.2633 15.0316 13.1308 15.0151C12.9983 14.9985 12.8704 14.9555 12.7548 14.8887C12.6391 14.8219 12.5381 14.7327 12.4575 14.6262C12.3769 14.5197 12.3185 14.3981 12.2857 14.2687C12.2528 14.1392 12.2462 14.0045 12.2663 13.8725C12.2864 13.7405 12.3327 13.6138 12.4025 13.5C12.7328 12.9284 13.2425 12.4817 13.8525 12.2291C14.4625 11.9766 15.1388 11.9323 15.7765 12.1032C16.4142 12.2741 16.9778 12.6506 17.3798 13.1743C17.7818 13.6981 17.9998 14.3398 18 15C18.0021 15.6522 17.7895 16.2869 17.395 16.8062Z"
        fill={props.fill ? props.fill : '#666666'}
      />
    </svg>
  );
};

export default CalenderIcon;
