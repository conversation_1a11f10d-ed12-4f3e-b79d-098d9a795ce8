import React from 'react';

const SimpleTickIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 32 32" fill="none">
      <path
        opacity="0.2"
        d="M29.7074 10.6575L13.6574 26.7075C13.5645 26.8005 13.4543 26.8743 13.3329 26.9246C13.2115 26.9749 13.0813 27.0008 12.9499 27.0008C12.8185 27.0008 12.6884 26.9749 12.567 26.9246C12.4456 26.8743 12.3353 26.8005 12.2424 26.7075L3.29242 17.7075C3.10502 17.52 2.99976 17.2658 2.99976 17.0007C2.99976 16.7355 3.10502 16.4813 3.29242 16.2938L6.29242 13.2938C6.38529 13.2008 6.49558 13.127 6.61698 13.0767C6.73837 13.0264 6.8685 13.0005 6.99992 13.0005C7.13133 13.0005 7.26146 13.0264 7.38286 13.0767C7.50426 13.127 7.61454 13.2008 7.70742 13.2938L12.2924 17.7088C12.3853 17.8018 12.4956 17.8755 12.617 17.9258C12.7384 17.9762 12.8685 18.0021 12.9999 18.0021C13.1313 18.0021 13.2615 17.9762 13.3829 17.9258C13.5043 17.8755 13.6145 17.8018 13.7074 17.7088L25.2924 6.29377C25.3853 6.2008 25.4956 6.12704 25.617 6.07671C25.7384 6.02639 25.8685 6.00049 25.9999 6.00049C26.1313 6.00049 26.2615 6.02639 26.3829 6.07671C26.5043 6.12704 26.6145 6.2008 26.7074 6.29377L29.7074 9.24377C29.8948 9.43129 30.0001 9.68555 30.0001 9.95065C30.0001 10.2158 29.8948 10.47 29.7074 10.6575Z"
        fill="#343330"
      />
      <path
        d="M30.4099 8.53003L27.4099 5.58503C27.0351 5.21176 26.5277 5.0022 25.9987 5.0022C25.4697 5.0022 24.9623 5.21176 24.5874 5.58503L12.9999 17L12.9862 16.9863L8.40619 12.5775C8.03043 12.2056 7.5227 11.9977 6.99402 11.9991C6.46534 12.0005 5.95873 12.2111 5.58494 12.585L2.58494 15.585C2.21054 15.96 2.00024 16.4683 2.00024 16.9982C2.00024 17.5281 2.21054 18.0363 2.58494 18.4113L11.5374 27.4113C11.7232 27.597 11.9437 27.7444 12.1863 27.8449C12.429 27.9455 12.6891 27.9972 12.9518 27.9972C13.2145 27.9972 13.4746 27.9455 13.7173 27.8449C13.96 27.7444 14.1805 27.597 14.3662 27.4113L30.4162 11.3638C30.6023 11.1776 30.7498 10.9564 30.8503 10.713C30.9507 10.4697 31.0021 10.2089 31.0015 9.9456C31.0009 9.68232 30.9484 9.42175 30.8469 9.17883C30.7453 8.93591 30.5969 8.71543 30.4099 8.53003ZM12.9524 26L3.99994 17L6.99994 14L7.01369 14.0138L11.5937 18.4225C11.9682 18.7935 12.474 19.0016 13.0012 19.0016C13.5283 19.0016 14.0342 18.7935 14.4087 18.4225L26.0074 7.00003L28.9999 9.95003L12.9524 26Z"
        fill="#343330"
      />
    </svg>
  );
};

export default SimpleTickIcon;
