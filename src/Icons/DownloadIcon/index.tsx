import React from 'react';

const DownloadIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg width="32" height="32" {...props} viewBox="0 0 32 32" fill="none">
      <path
        d="M28.5 19V26C28.5 26.663 28.2366 27.2989 27.7678 27.7678C27.2989 28.2366 26.663 28.5 26 28.5H6C5.33696 28.5 4.70107 28.2366 4.23223 27.7678C3.76339 27.2989 3.5 26.663 3.5 26V19C3.5 18.6022 3.65804 18.2206 3.93934 17.9393C4.22064 17.658 4.60218 17.5 5 17.5C5.39782 17.5 5.77936 17.658 6.06066 17.9393C6.34196 18.2206 6.5 18.6022 6.5 19V25.5H25.5V19C25.5 18.6022 25.658 18.2206 25.9393 17.9393C26.2206 17.658 26.6022 17.5 27 17.5C27.3978 17.5 27.7794 17.658 28.0607 17.9393C28.342 18.2206 28.5 18.6022 28.5 19ZM14.9388 20.0613C15.0781 20.2011 15.2437 20.312 15.426 20.3878C15.6083 20.4635 15.8038 20.5024 16.0012 20.5024C16.1987 20.5024 16.3941 20.4635 16.5765 20.3878C16.7588 20.312 16.9244 20.2011 17.0637 20.0613L22.0637 15.0612C22.3455 14.7795 22.5039 14.3973 22.5039 13.9987C22.5039 13.6002 22.3455 13.218 22.0637 12.9362C21.782 12.6545 21.3998 12.4961 21.0012 12.4961C20.6027 12.4961 20.2205 12.6545 19.9387 12.9362L17.5 15.375V5C17.5 4.60218 17.342 4.22064 17.0607 3.93934C16.7794 3.65804 16.3978 3.5 16 3.5C15.6022 3.5 15.2206 3.65804 14.9393 3.93934C14.658 4.22064 14.5 4.60218 14.5 5V15.375L12.0612 12.9388C11.7795 12.657 11.3973 12.4986 10.9987 12.4986C10.6002 12.4986 10.218 12.657 9.93625 12.9388C9.65446 13.2205 9.49615 13.6027 9.49615 14.0013C9.49615 14.3998 9.65446 14.782 9.93625 15.0638L14.9388 20.0613Z"
        fill={props.fill ? props.fill : '#343330'}
      />
    </svg>
  );
};

export default DownloadIcon;
