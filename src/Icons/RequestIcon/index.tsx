import React from 'react';

const RequestIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.00001 4.00006C7.99963 3.25201 7.7895 2.51906 7.39347 1.88444C6.99745 1.24982 6.43141 0.738966 5.75962 0.409897C5.08784 0.0808275 4.33725 -0.053271 3.59308 0.0228304C2.84891 0.0989319 2.14099 0.382183 1.54972 0.840417C0.958454 1.29865 0.507528 1.9135 0.248155 2.61515C-0.0112185 3.31679 -0.0686444 4.0771 0.0823995 4.80975C0.233443 5.54239 0.586904 6.21799 1.10264 6.75984C1.61837 7.30168 2.27571 7.68805 3.00001 7.87506V16.1251C2.05735 16.3685 1.23582 16.9473 0.689408 17.7531C0.142996 18.5588 -0.0907835 19.5362 0.031891 20.5021C0.154565 21.4679 0.625271 22.3558 1.35578 22.9994C2.08628 23.643 3.02644 23.998 4.00001 23.998C4.97358 23.998 5.91374 23.643 6.64424 22.9994C7.37475 22.3558 7.84545 21.4679 7.96813 20.5021C8.0908 19.5362 7.85702 18.5588 7.31061 17.7531C6.7642 16.9473 5.94267 16.3685 5.00001 16.1251V7.87506C5.85833 7.65212 6.61846 7.15077 7.16134 6.44956C7.70421 5.74834 7.99917 4.88686 8.00001 4.00006ZM6.00001 20.0001C6.00001 20.3956 5.88271 20.7823 5.66295 21.1112C5.44319 21.4401 5.13083 21.6964 4.76538 21.8478C4.39992 21.9992 3.99779 22.0388 3.60983 21.9616C3.22187 21.8845 2.8655 21.694 2.5858 21.4143C2.30609 21.1346 2.11561 20.7782 2.03844 20.3902C1.96127 20.0023 2.00087 19.6001 2.15225 19.2347C2.30363 18.8692 2.55997 18.5569 2.88887 18.3371C3.21777 18.1174 3.60445 18.0001 4.00001 18.0001C4.53044 18.0001 5.03915 18.2108 5.41422 18.5858C5.7893 18.9609 6.00001 19.4696 6.00001 20.0001ZM4.00001 6.00006C3.60445 6.00006 3.21777 5.88276 2.88887 5.663C2.55997 5.44324 2.30363 5.13088 2.15225 4.76543C2.00087 4.39997 1.96127 3.99784 2.03844 3.60988C2.11561 3.22192 2.30609 2.86555 2.5858 2.58585C2.8655 2.30614 3.22187 2.11566 3.60983 2.03849C3.99779 1.96132 4.39992 2.00093 4.76538 2.1523C5.13083 2.30368 5.44319 2.56002 5.66295 2.88892C5.88271 3.21782 6.00001 3.6045 6.00001 4.00006C6.00001 4.53049 5.7893 5.0392 5.41422 5.41427C5.03915 5.78935 4.53044 6.00006 4.00001 6.00006ZM21 16.1251V11.4851C21.0023 10.5654 20.8224 9.6545 20.4705 8.80486C20.1186 7.95522 19.6018 7.18376 18.95 6.53506L15.4138 3.00006H19C19.2652 3.00006 19.5196 2.8947 19.7071 2.70717C19.8947 2.51963 20 2.26528 20 2.00006C20 1.73484 19.8947 1.48049 19.7071 1.29295C19.5196 1.10542 19.2652 1.00006 19 1.00006H13C12.7348 1.00006 12.4804 1.10542 12.2929 1.29295C12.1054 1.48049 12 1.73484 12 2.00006V8.00006C12 8.26528 12.1054 8.51963 12.2929 8.70717C12.4804 8.8947 12.7348 9.00006 13 9.00006C13.2652 9.00006 13.5196 8.8947 13.7071 8.70717C13.8947 8.51963 14 8.26528 14 8.00006V4.41381L17.535 7.95006C18.0009 8.41305 18.3704 8.96391 18.6218 9.57072C18.8733 10.1775 19.0019 10.8282 19 11.4851V16.1251C18.0574 16.3685 17.2358 16.9473 16.6894 17.7531C16.143 18.5588 15.9092 19.5362 16.0319 20.5021C16.1546 21.4679 16.6253 22.3558 17.3558 22.9994C18.0863 23.643 19.0264 23.998 20 23.998C20.9736 23.998 21.9137 23.643 22.6442 22.9994C23.3747 22.3558 23.8455 21.4679 23.9681 20.5021C24.0908 19.5362 23.857 18.5588 23.3106 17.7531C22.7642 16.9473 21.9427 16.3685 21 16.1251ZM20 22.0001C19.6044 22.0001 19.2178 21.8828 18.8889 21.663C18.56 21.4432 18.3036 21.1309 18.1523 20.7654C18.0009 20.4 17.9613 19.9978 18.0384 19.6099C18.1156 19.2219 18.3061 18.8656 18.5858 18.5858C18.8655 18.3061 19.2219 18.1157 19.6098 18.0385C19.9978 17.9613 20.3999 18.0009 20.7654 18.1523C21.1308 18.3037 21.4432 18.56 21.6629 18.8889C21.8827 19.2178 22 19.6045 22 20.0001C22 20.5305 21.7893 21.0392 21.4142 21.4143C21.0392 21.7893 20.5304 22.0001 20 22.0001Z"
        fill={props.fill ? props.fill : '#666666'}
      />
    </svg>
  );
};

export default RequestIcon;
