import React from 'react';

const VideoFileIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      viewBox="0 0 23 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.5287 16.1513C14.3847 16.0616 14.2202 16.0101 14.0507 16.0016C13.8812 15.993 13.7123 16.0278 13.56 16.1025L10.9075 17.4013C10.7801 16.9951 10.5264 16.6402 10.1833 16.3882C9.84022 16.1361 9.42569 16.0002 9 16H2C1.46957 16 0.960859 16.2107 0.585786 16.5858C0.210714 16.9609 0 17.4696 0 18V23C0 23.5304 0.210714 24.0391 0.585786 24.4142C0.960859 24.7893 1.46957 25 2 25H9C9.44013 24.9995 9.86782 24.8538 10.2168 24.5856C10.5657 24.3173 10.8164 23.9415 10.93 23.5163L13.535 24.8913C13.6879 24.9715 13.8589 25.0109 14.0315 25.0055C14.204 25 14.3723 24.95 14.5198 24.8603C14.6673 24.7705 14.789 24.6441 14.8732 24.4934C14.9573 24.3426 15.001 24.1727 15 24V17C15 16.8302 14.9567 16.6631 14.8743 16.5146C14.7918 16.3661 14.6729 16.2411 14.5287 16.1513ZM9 23H2V18H9V21.875C9 21.8913 9 21.9062 9 21.9225V23ZM13 22.345L11 21.2925V19.5825L13 18.6038V22.345ZM22.7075 7.2925L15.7075 0.2925C15.6146 0.199666 15.5042 0.126052 15.3829 0.0758639C15.2615 0.0256758 15.1314 -0.000102986 15 3.09198e-07H3C2.46957 3.09198e-07 1.96086 0.210714 1.58579 0.585787C1.21071 0.960859 1 1.46957 1 2V13C1 13.2652 1.10536 13.5196 1.29289 13.7071C1.48043 13.8946 1.73478 14 2 14C2.26522 14 2.51957 13.8946 2.70711 13.7071C2.89464 13.5196 3 13.2652 3 13V2H14V8C14 8.26522 14.1054 8.51957 14.2929 8.70711C14.4804 8.89464 14.7348 9 15 9H21V24H18C17.7348 24 17.4804 24.1054 17.2929 24.2929C17.1054 24.4804 17 24.7348 17 25C17 25.2652 17.1054 25.5196 17.2929 25.7071C17.4804 25.8946 17.7348 26 18 26H21C21.5304 26 22.0391 25.7893 22.4142 25.4142C22.7893 25.0391 23 24.5304 23 24V8C23.0001 7.86864 22.9743 7.73855 22.9241 7.61715C22.8739 7.49576 22.8003 7.38544 22.7075 7.2925ZM16 3.41375L19.5863 7H16V3.41375Z"
        fill={props.fill ? props.fill : '#666666'}
      />
    </svg>
  );
};

export default VideoFileIcon;
