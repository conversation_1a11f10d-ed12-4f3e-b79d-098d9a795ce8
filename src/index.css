/* @import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap'); */
@import 'tailwindcss';

@theme {
  --color-primary-100: #FF8800;
  --color-primary-0: #FFA033;
  --shadow-primary: 0px 0px 14px rgba(0, 0, 0, 0.15);
  --color-b-primary: #e0def7;
  --color-secondary: #666666;
  --color-primary-light: #ebfffb;
  --color-error-0: #f2584d;
  --color-error-100: #ffd8e0;
  --color-error-500: #ff0000;
}

body {
  font-family: 'Roboto', sans-serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
}

@layer base {
  .reset-styles h1,
  .reset-styles h2,
  .reset-styles h3,
  .reset-styles h4,
  .reset-styles h5,
  .reset-styles h6,
  .reset-styles ul,
  .reset-styles ol,
  .reset-styles li,
  .reset-styles p,
  .reset-styles blockquote,
  .reset-styles pre,
  .reset-styles code,
  .reset-styles table,
  .reset-styles th,
  .reset-styles td,
  .reset-styles button,
  .reset-styles input,
  .reset-styles textarea,
  .reset-styles select,
  .reset-styles label,
  .reset-styles a {
    all: revert !important;
  }
}

@layer utilities {
  /* Chrome, Safari and Opera */
  .scrollbar-hidden::-webkit-scrollbar {
    display: none;
  }

  .scrollbar-hidden {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }
}

.custom-scrollbar::-webkit-scrollbar {
  height: 2px;
  width: 4px;
}
.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #FF8800;
  border-radius: 10px;
}
.custom-scrollbar::-webkit-scrollbar-track {
  background-color: transparent;
}

.custom-clamp {
  display: -webkit-box;
  -webkit-line-clamp: 3; /* Limits text to 3 lines */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 5.3em; /* Adjust based on font size */
  line-height: 1.5em; /* Controls line spacing */

  /* Standard Property (Fallback) */
  display: box;
  line-clamp: 3;
  box-orient: vertical;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0px 1000px white inset !important;
  box-shadow: 0 0 0px 1000px white inset !important;
  transition: background-color 5000s ease-in-out 0s;
}

.css-l8st5q-control,
.css-1iqnnhh-control,
.css-1cz0gmn-control,
.css-z07fgl-control {
  padding-block: 6px !important;
  padding-left: 12px !important;
}

.react-datepicker-wrapper {
  width: 100%;
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type='number'] {
  -moz-appearance: textfield;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.infinite-loader {
  height: 100%;
  width: 50%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: shimmer 1.2s infinite linear;
  position: absolute;
  top: 0;
  left: 0;
}

.css-19bb58m {
  margin: 0px !important;
  padding-bottom: 0px !important;
  padding-top: 0px !important;
}

.css-hlgwow {
  padding: 0px !important;
}

.css-l8st5q-control,
.css-1iqnnhh-control,
.css-1cz0gmn-control,
.css-z07fgl-control {
  padding-block: 8px !important;
  padding-left: 11px !important;
}

.css-ofcfbk-control,
.css-l8st5q-control {
  padding-block: 3px !important;
  padding-left: 11px !important;
}

.react-select__control,
.react-select__control--is-disabled {
  font-size: var(--text-sm);
}
