import { RootState } from '@Redux/store';
import { useSelector } from 'react-redux';

const useHasAccess = (
  featureName: string,
  actions: ('read' | 'write' | 'update' | 'delete')[]
) => {
  const permissions = useSelector(
    (state: RootState) => state.PermissionControl?.data?.features ?? []
  );

  // Find the feature in permissions
  const feature = permissions?.find(
    (feature: { name: string }) => feature?.name === featureName
  );

  // Map over actions array and return an object with true/false values
  return actions.reduce(
    (acc, action) => {
      acc[action] = feature?.access?.[action] ?? false;
      return acc;
    },
    {} as Record<'read' | 'write' | 'update' | 'delete', boolean>
  );
};

export default useHasAccess;
