import { useLocation, useSearchParams } from 'react-router';

const usePathnameAndQuery = () => {
  const location = useLocation(); // Get current URL details
  const [searchParams] = useSearchParams(); // Get query parameters

  const pathname = location.pathname; // Extract the pathname
  const query = Object.fromEntries(searchParams.entries()); // Convert query params to an object

  return { pathname, query };
};

export default usePathnameAndQuery;
