import { useEffect, useCallback, useRef } from 'react';

const useShortcutKeys = (
  keys: string[],
  callback: () => void,
  options: { preventDefault?: boolean } = {}
) => {
  // Store normalized keys (lowercase)
  const requiredKeysRef = useRef(new Set(keys.map((k) => k.toLowerCase())));
  // Store currently pressed keys
  const pressedKeysRef = useRef(new Set<string>());
  // Flag to track if we're in the middle of detecting our specific combination
  const isOurCombinationRef = useRef(false);

  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      const key = event.key.toLowerCase();

      // Record the key press
      if (requiredKeysRef.current.has(key)) {
        pressedKeysRef.current.add(key);
      }

      // Special handling for modifier keys
      if (requiredKeysRef.current.has('control') && event.ctrlKey) {
        pressedKeysRef.current.add('control');
      }
      if (requiredKeysRef.current.has('shift') && event.shiftKey) {
        pressedKeysRef.current.add('shift');
      }
      if (requiredKeysRef.current.has('alt') && event.altKey) {
        pressedKeysRef.current.add('alt');
      }
      if (requiredKeysRef.current.has('meta') && event.metaKey) {
        pressedKeysRef.current.add('meta');
      }

      // Check if our combination is being formed
      const hasAnyRequiredKey = [...requiredKeysRef.current].some((k) =>
        pressedKeysRef.current.has(k)
      );

      // Update our tracking flag
      isOurCombinationRef.current = hasAnyRequiredKey;

      // Check if all keys in our combination are pressed
      const allRequiredKeysPressed = [...requiredKeysRef.current].every((k) =>
        pressedKeysRef.current.has(k)
      );

      // CRITICAL: Only prevent default and execute callback if EXACTLY our combination is pressed
      if (
        allRequiredKeysPressed &&
        pressedKeysRef.current.size === requiredKeysRef.current.size &&
        isOurCombinationRef.current
      ) {
        // Only if we match our exact combination AND preventDefault was requested
        if (options.preventDefault) {
          event.preventDefault();
          event.stopPropagation();
        }

        // Execute the callback
        callback();

        // Reset after executing to prevent multiple firings
        pressedKeysRef.current.clear();
        isOurCombinationRef.current = false;
      }
    },
    [callback, options.preventDefault]
  );

  const handleKeyUp = useCallback((event: KeyboardEvent) => {
    const key = event.key.toLowerCase();

    // Remove the key from tracking
    pressedKeysRef.current.delete(key);

    // Handle modifier keys
    if (!event.ctrlKey) pressedKeysRef.current.delete('control');
    if (!event.shiftKey) pressedKeysRef.current.delete('shift');
    if (!event.altKey) pressedKeysRef.current.delete('alt');
    if (!event.metaKey) pressedKeysRef.current.delete('meta');

    // Reset our combination tracking if no keys from our combination are pressed
    const hasAnyRequiredKey = [...requiredKeysRef.current].some((k) =>
      pressedKeysRef.current.has(k)
    );

    if (!hasAnyRequiredKey) {
      isOurCombinationRef.current = false;
      pressedKeysRef.current.clear();
    }
  }, []);

  // For handling blur events (user switches tabs/windows)
  const handleBlur = useCallback(() => {
    // Reset everything when user changes focus
    pressedKeysRef.current.clear();
    isOurCombinationRef.current = false;
  }, []);

  useEffect(() => {
    // Register events in normal bubbling phase (not capture) to ensure we don't interfere with default browser behavior
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);
    window.addEventListener('blur', handleBlur);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
      window.removeEventListener('blur', handleBlur);
    };
  }, [handleKeyDown, handleKeyUp, handleBlur]);

  // Return debugging utilities if needed
  return {
    debug: () => {},
  };
};

export default useShortcutKeys;
