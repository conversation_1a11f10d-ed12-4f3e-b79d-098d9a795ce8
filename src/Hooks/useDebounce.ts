import { useEffect, useState } from 'react';

/**
 * Custom hook to debounce a value.
 * @param value - The value to debounce.
 * @param delay - Delay in milliseconds (default: 500ms).
 * @returns The debounced value.
 */
export const useDebounce = <T>(value: T, delay: number = 500): T => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler); // Cleanup previous timeout
    };
  }, [value, delay]);

  return debouncedValue;
};
