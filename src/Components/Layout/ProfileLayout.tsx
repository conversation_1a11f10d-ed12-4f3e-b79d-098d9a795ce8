import { AccessibleImage, Button, Modal } from '@Components/UI';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import PATHS from '@Config/Path.Config';
import queryClient from '@Helpers/QueryClient';
import RedCrossImage from '@Assets/Images/RedCross.png';
import { AdminIcon, DeleteAccIcon, KeyIcon, LogoutIcon } from '@Icons';
import {
  useDeleteSubAdminAccount,
  useGetAdminProfile,
} from '@Query/Hooks/useAdmin';
import { useLogout } from '@Query/Hooks/useAuth';
import clsx from 'clsx';
import { FC, ReactNode, SVGProps, useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router';

interface SidebarItemProps {
  title: string;
  Icon: FC<SVGProps<SVGSVGElement>>;
  isActive: boolean;
  onClick?: () => void;
  clickAble?: boolean;
}

const ProfileSidebarItem: FC<SidebarItemProps> = ({
  title,
  Icon,
  isActive,
  onClick,
  clickAble = false, // Default to false
}) => (
  <div
    className={clsx(
      'flex items-center gap-2 border-b border-[#F1F1F1] p-4 cursor-pointer transition-all duration-300',
      isActive ? 'bg-[#ffe3c5]' : 'hover:bg-[#F9F9F9]'
    )}
    onClick={clickAble ? onClick : undefined} // Prevent unnecessary clicks
  >
    <div
      className={clsx(
        'bg-[#F1F1F1] h-10 w-10 rounded-full flex items-center justify-center transition-all duration-300',
        isActive && 'bg-gradient-to-b from-[#FF8800] to-[#FFA033]'
      )}
    >
      <Icon height={18} width={18} fill={isActive ? '#FFFFFF' : '#333333'} />
    </div>
    <h4
      className={clsx(
        'text-[#333333] text-base font-semibold transition-all duration-300',
        isActive && 'text-[#FF8800]'
      )}
    >
      {title}
    </h4>
  </div>
);

interface ProfileLayoutProps {
  children: ReactNode;
}

const ProfileLayout: FC<ProfileLayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [openLogoutModal, setOpenLogoutModal] = useState<boolean>(false);
  const [openDeleteAccountModal, setOpenDeleteAccountModal] =
    useState<boolean>(false);

  const { addToast } = useToast();
  const {
    mutate: logout,
    isLoading,
    isError,
    error,
    isSuccess,
    data,
  } = useLogout();

  const {
    mutate: deleteAccount,
    data: deleteAccountData,
    isError: isDeleteAccountError,
    error: deleteAccountError,
    isSuccess: deleteAccountSuccess,
    isLoading: isDeleteAccountLoading,
  } = useDeleteSubAdminAccount();

  const {
    data: viewProfileData,
    isError: isErrorInViewProfile,
    error: errorOfViewProfile,
    isSuccess: isSuccessViewProfile,
    // isLoading: viewProfileIsLoading,
  } = useGetAdminProfile();

  const handleLogout = () => setOpenLogoutModal(true);

  const [sidebarList, setSidebarList] = useState([
    {
      key: 'myProfile',
      title: 'My Profile',
      Icon: AdminIcon,
      path: PATHS.PROFILE,
      clickAble: true,
    },
    {
      key: 'changePassword',
      title: 'Change Password',
      Icon: KeyIcon,
      path: PATHS.CHANGE_PASSWORD,
      clickAble: true,
    },
    {
      key: 'logout',
      title: 'Logout',
      Icon: LogoutIcon,
      path: null, // No path for logout
      clickAble: true,
      action: handleLogout,
    },
  ]);

  useEffect(() => {
    if (isError) {
      addToast('error', error as string);
    }
  }, [isError, error]);

  useEffect(() => {
    if (isErrorInViewProfile) {
      addToast('error', errorOfViewProfile as string);
    }
  }, [isSuccessViewProfile, errorOfViewProfile]);

  useEffect(() => {
    if (isSuccess) {
      addToast('success', data?.data?.message);
      setOpenLogoutModal(false);
      localStorage.removeItem('authaccess');
      queryClient.clear();
      navigate(PATHS.LOGIN, { replace: true });
    }
  }, [isSuccess]);

  useEffect(() => {
    if (isDeleteAccountError) {
      addToast('error', deleteAccountError as string);
    }
    if (deleteAccountSuccess) {
      setOpenDeleteAccountModal(false);
      addToast('success', deleteAccountData?.data?.message);
      localStorage.removeItem('authaccess');
      navigate(PATHS.LOGIN, { replace: true });
    }
  }, [
    isDeleteAccountError,
    deleteAccountError,
    deleteAccountSuccess,
    deleteAccountData,
  ]);

  // useEffect(() => {
  //   if (!(viewProfileData?.data?.data?.role === 1)) {
  //     setSidebarList(
  //       sidebarList.concat({
  //         key: 'deleteAccount',
  //         title: 'Delete Account',
  //         Icon: DeleteAccIcon,
  //         path: null, // No path for logout
  //         clickAble: true,
  //         action: () => setOpenDeleteAccountModal(true),
  //       })
  //     );
  //   }
  // }, [viewProfileData]);

  return (
    <div className="flex gap-8 p-10">
      {/* Sidebar */}
      <div className="w-[420px] h-[80vh] shadow-md rounded-md overflow-hidden bg-white">
        {sidebarList.map(({ key, title, Icon, path, clickAble, action }) => (
          <ProfileSidebarItem
            key={key}
            title={title}
            Icon={Icon}
            isActive={path === location.pathname}
            onClick={() => (path ? navigate(path) : action?.())}
            clickAble={clickAble}
          />
        ))}
      </div>

      {/* Content Section */}
      <div className="w-full border border-[#F1F1F1] rounded-md bg-white">
        {children}
      </div>
      <Modal
        hideCloseIcon
        children={
          <div className="flex flex-col items-center gap-[36px] justify-center">
            <div className="bg-[#ffe3c5] h-20 w-20 flex justify-center items-center rounded-full">
              <LogoutIcon width={25} height={25} fill="#FF8800" />
            </div>
            <div>
              <h4 className="text-black text-2xl font-bold text-center">
                Are you sure want to logout this account?
              </h4>
            </div>
            <div className="flex w-full gap-6">
              <Button text="Yes" loading={isLoading} onClick={() => logout()} />
              <Button
                text="No"
                variant="outline"
                disabled={isLoading}
                onClick={() => setOpenLogoutModal(false)}
              />
            </div>
          </div>
        }
        isOpen={openLogoutModal}
        onClose={() => setOpenLogoutModal(false)}
      />

      <Modal
        isOpen={openDeleteAccountModal}
        hideCloseIcon
        onClose={() => setOpenDeleteAccountModal(false)}
        children={
          <div className="flex flex-col items-center gap-[36px] justify-center">
            {/* <div> */}
            {/* <CloseIcon width={25} height={25} fill="#FF3B30" /> */}
            <AccessibleImage
              alt="redcross"
              className="h-16 w-16"
              src={RedCrossImage}
              width={10}
              height={10}
            />
            {/* </div> */}
            <div>
              <h4 className="text-black text-2xl font-bold text-center">
                Are you sure want to delete this account?
              </h4>
            </div>
            <div className="h-[50px] w-full flex justify-center gap-5">
              <Button
                text="Cancel"
                variant="outline"
                onClick={() => setOpenDeleteAccountModal(false)}
              />
              <Button
                onClick={deleteAccount}
                text="Delete Account"
                variant="other"
                loading={isDeleteAccountLoading}
                disabled={isDeleteAccountLoading}
                className=" border border-error-0 text-error-0 bg-transparent hover:border-error-0"
              />
            </div>
          </div>
        }
      />
    </div>
  );
};

export default ProfileLayout;
