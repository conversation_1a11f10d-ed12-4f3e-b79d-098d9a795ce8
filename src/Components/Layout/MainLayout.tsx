import Sidebar from '@Components/Common/Sidebar';
import PATHS from '@Config/Path.Config';
import { AdminIcon } from '@Icons';
import clsx from 'clsx';

import { ReactNode } from 'react';
import { useNavigate } from 'react-router';

interface MainLayoutProps {
  heading: string;
  children: ReactNode;
  breadcrumbs: ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({
  heading,
  breadcrumbs,
  children,
}: MainLayoutProps) => {
  const navigate = useNavigate();

  return (
    <div className="flex">
      <Sidebar />
      <div className="w-full h-dvh flex flex-col overflow-hidden">
        <div className="flex flex-col justify-between sticky top-0 bg-white z-20">
          <div
            className={clsx(
              'flex justify-between items-center gap-1.5 p-3.5 shadow'
            )}
          >
            <div>
              <h4 className="text-xl font-semibold">{heading}</h4>
            </div>
            <div>
              <div
                className="bg-primary-100 h-10 w-10 rounded-full flex items-center justify-center cursor-pointer shadow-md"
                onClick={() => navigate(PATHS.PROFILE)}
              >
                <AdminIcon height={18} width={18} fill="#fff" />
              </div>
            </div>
          </div>
        </div>
        <div className="flex flex-col overflow-auto custom-scrollbar">
          {children}
        </div>
      </div>
    </div>
  );
};

export default MainLayout;
