import clsx from 'clsx';
import React, { ReactNode } from 'react';

interface ContentLayoutProps {
  children?: ReactNode;
  title?: string | ReactNode;
  subtitle?: string;
  headerRightSideChildren?: ReactNode;
  headerLeftSideChildren?: ReactNode;
  removeHeader?: boolean;
  childrenMainClass?: string;
  customHeadStyle?: string;
  rightSideStyle?: string;
  leftSideStyle?: string;
}

const ContentLayout: React.FC<ContentLayoutProps> = ({
  children,
  subtitle,
  title,
  headerRightSideChildren,
  headerLeftSideChildren,
  removeHeader = false,
  childrenMainClass = '',
  customHeadStyle = 'p-6 px-10 pb-0',
  rightSideStyle,
  leftSideStyle,
}: ContentLayoutProps) => {
  return (
    <div>
      <div className={clsx('p-6 flex flex-col gap-4', childrenMainClass)}>
        {children}
      </div>
    </div>
  );
};
export default ContentLayout;
