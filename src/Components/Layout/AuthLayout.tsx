import React, { ReactNode } from 'react';
import Logo from '@Assets/Images/Logo2.png';
import AccessibleImage from '@Components/UI/AccessibleImage';
import { t } from 'i18next';

interface AuthLayoutProps {
  title: string;
  subTitle?: string;
  children?: ReactNode;
}

const AuthLayout: React.FC<AuthLayoutProps> = ({
  title,
  children,
  subTitle,
}: AuthLayoutProps) => {
  const currentYear = new Date().getFullYear();
  return (
    <div className="flex flex-col h-screen w-screen items-center pt-24 relative overflow-y-auto">
      <div className="flex h-[120px] w-[192px] justify-center">
        <AccessibleImage src={Logo} alt={'Rex'} className="w-20 h-20" />
      </div>
      <div className="text-black font-bold text-4xl">{title}</div>
      <div className="text-black font-normal text-lg pt-2">{subTitle}</div>
      <div className="w-full sm:w-[500px] flex flex-col pb-20 pt-4">
        {children}
      </div>
      <div className="fixed w-full text-[] text-center text-[10px] bottom-0 py-2 bg-white">
        {t('copyrightText1')} {currentYear} {t('copyrightText2')}
      </div>
    </div>
  );
};

export default AuthLayout;
