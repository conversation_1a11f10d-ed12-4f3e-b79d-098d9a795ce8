import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeftIcon } from '@Icons';
import clsx from 'clsx';

const ActionBarLayout = (props: {
  children: React.ReactNode;
  actionbarChildren?: React.ReactNode;
  customStyleForMain?: string;
}) => {
  const { actionbarChildren, children, customStyleForMain } = props;

  return (
    <div
      className={clsx(
        'bg-white p-5 rounded-2xl border border-[#f0f0f0] h-[80vh]',
        customStyleForMain
      )}
    >
      <div
        className={clsx(
          'w-full flex items-center justify-between gap-5',
          actionbarChildren ? 'mb-5' : ''
        )}
      >
        {actionbarChildren}
      </div>
      {children}
    </div>
  );
};

export default ActionBarLayout;
