import React from 'react';

export interface CustomRadioProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
}

const CustomRadio: React.FC<CustomRadioProps> = ({ label, ...props }) => {
  return (
    <label className="select-none flex items-center space-x-2 cursor-pointer">
      {/* Hidden Default Radio */}
      <input {...props} type="radio" className="hidden" />

      {/* Custom Radio */}
      <div
        className={`w-5 h-5 border-2 rounded-full flex items-center justify-center transition-all ${
          props.disabled
            ? 'border-gray-400 bg-gray-200 cursor-not-allowed'
            : props.checked
              ? 'bg-primary-100 border-primary-100'
              : 'border-[#666666]'
        }`}
      >
        {props.checked && <div className="w-2.5 h-2.5 bg-white rounded-full" />}
      </div>

      {/* Label Text */}
      {label && <span className="text-gray-700">{label}</span>}
    </label>
  );
};

export default CustomRadio;
