import { InfoIcon, ErrorIcon, Loader } from '@Icons';
import clsx from 'clsx';
import React from 'react';
import { ControllerRenderProps } from 'react-hook-form';
import Tooltip from '../Tooltip';

interface InputFieldProps extends React.InputHTMLAttributes<HTMLInputElement> {
  type?: string;
  label?: string | boolean;
  placeholder?: string;
  variant?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  field?: ControllerRenderProps<any, any>; // Proper typing for react-hook-form field
  errorMessage?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  infoMsg?: string;
  containerClassName?: string;
  inputContainerClassName?: string;
  inputClassName?: string;
  labelColor?: string;
  disabled?: boolean;
  loading?: boolean;
  showErrorIcon?: boolean;
  rows?: number;
}

const InputField: React.FC<InputFieldProps> = ({
  type = 'text',
  label,
  placeholder,
  field,
  errorMessage,
  leftIcon,
  rightIcon,
  infoMsg = '',
  containerClassName = '',
  inputContainerClassName = 'max-h-fit',
  inputClassName = '',
  labelColor = 'text-gray-700',
  disabled = false,
  showErrorIcon = true,
  loading = false,
  ...rest
}) => {
  return (
    <div
      className={clsx(
        'flex flex-col gap-1.5 w-full relative select-none',
        containerClassName
      )}
    >
      {/* Label & Info Icon */}
      {(label || infoMsg) && (
        <div className="flex items-center">
          {label && (
            <label
              className={clsx('text-sm font-medium text-left', labelColor)}
            >
              {label}
            </label>
          )}
          {!!infoMsg && (
            <div className="relative group flex cursor-pointer pl-1.5">
              <Tooltip content={infoMsg}>
                <InfoIcon height={16} width={16} />
              </Tooltip>
              {/* <div className="absolute left-0 mb-1 bottom-full p-2 rounded-xl min-w-max border border-[#E4E7EC] bg-white text-secondary-color-text text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
                {}
              </div> */}
            </div>
          )}
        </div>
      )}

      {/* Input Container */}
      <div
        className={clsx(
          'flex items-center flex-1 py-2 px-[11px] border rounded-[10px] border-[#E0DEF7] focus-within:ring-1 focus-within:ring-teal-100 focus-within:shadow-[0px_0px_14px_rgba(0,0,0,0.15)]',
          inputContainerClassName,
          disabled
            ? 'bg-[#e5e7eb] cursor-not-allowed focus-within:border-[#E0DEF7] hover:border-[#E0DEF7]'
            : 'bg-white hover:border-primary-100 focus-within:border-primary-100',
          errorMessage &&
            'border-red-500 focus-within:border-red-500 focus-within:shadow-red-200 shadow-red-200 hover:border-red-500'
        )}
      >
        {leftIcon && <div className="flex pr-2">{leftIcon}</div>}

        <input
          {...field} // Pass react-hook-form field props
          {...rest} // Pass additional props (ensures `disabled` and `type` override correctly)
          type={type}
          placeholder={placeholder}
          disabled={disabled}
          className={clsx(
            'w-full flex-1 border-none outline-none appearance-none disabled:bg-[#e5e7eb] disabled:cursor-not-allowed text-sm',
            inputClassName
          )}
        />

        {(rightIcon || loading) && (
          <div className="flex items-center">
            {loading ? (
              <Loader height={20} width={20} fill="#fff" />
            ) : (
              rightIcon
            )}
          </div>
        )}
        {errorMessage && !rightIcon && showErrorIcon && <ErrorIcon />}
      </div>

      {/* Error Message */}
      {errorMessage && (
        <p className="text-red-500 text-xs break-words">{errorMessage}</p>
      )}
    </div>
  );
};

export default InputField;
