import React, { useRef, useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import clsx from 'clsx';
import { useController, Control } from 'react-hook-form';
import { ThumbnailIcon, UserCircleIcon } from '@Icons';

type ProfileImageUploaderProps = {
  name: string;
  errorMessage?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  control: Control<any>;
  defaultImage?: string | File | undefined;
  maindivClassName?: string;
  imageclassName?: string;
  changePlaceholderImage?: boolean;
  rootDivStyle?: string;
};

const ProfileImageUploader: React.FC<ProfileImageUploaderProps> = ({
  name,
  control,
  errorMessage,
  defaultImage,
  maindivClassName = '',
  imageclassName = '',
  rootDivStyle = '',
  changePlaceholderImage = false,
}) => {
  const {
    field: { value, onChange, ref },
  } = useController({
    name,
    control,
  });

  const [preview, setPreview] = useState<string | null>(
    defaultImage ? (defaultImage as string) : ''
  );
  const [isDragging, setIsDragging] = useState(false);
  const inputRef = useRef<HTMLInputElement | null>(null);

  useEffect(() => {
    if (value instanceof File) {
      const objectUrl = URL.createObjectURL(value);
      setPreview(objectUrl);
      return () => URL.revokeObjectURL(objectUrl); // cleanup
    } else if (value === null) {
      setPreview(null);
    }
  }, [value]);

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    const file = e.dataTransfer.files?.[0];
    if (file && file.type.startsWith('image/')) {
      onChange(file);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      onChange(file);
    }
  };

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering the parent div's onClick
    onChange(null);
    // Reset the file input value so the same file can be selected again
    if (inputRef.current) {
      inputRef.current.value = '';
    }
  };

  const handleClick = () => {
    inputRef.current?.click();
  };

  useEffect(() => {
    if (defaultImage) {
      setPreview(defaultImage as string);
    }
  }, [defaultImage]);

  return (
    <div className={clsx('flex flex-col items-center space-y-4', rootDivStyle)}>
      <motion.div
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onDragLeave={handleDragLeave}
        className={clsx(
          'flex flex-col items-center justify-center text-sm text-gray-500 text-center  border border-dashed w-30 h-30 group bg-gray-100 rounded-full border-gray-300 transition-all relative overflow-hidden cursor-pointer',
          isDragging && 'border-blue-400 bg-blue-50',
          maindivClassName
        )}
        initial={{ scale: 1 }}
        animate={{ scale: isDragging ? 1.05 : 1 }}
        transition={{ type: 'spring', stiffness: 300, damping: 20 }}
        onClick={handleClick}
      >
        <input
          ref={(e) => {
            inputRef.current = e;
            if (ref) ref(e);
          }}
          type="file"
          className="sr-only"
          accept="image/*"
          onChange={handleFileChange}
        />
        {preview ? (
          <img
            src={preview ?? (defaultImage as string)}
            alt="Profile"
            className={clsx(
              'object-cover w-full h-full rounded-full',
              imageclassName
            )}
          />
        ) : (
          <div className="flex flex-col items-center">
            <div className="w-8 h-8 rounded-full">
              {changePlaceholderImage ? (
                <ThumbnailIcon fill="#FF8800" />
              ) : (
                <UserCircleIcon fill="#FF8800" />
              )}
            </div>
          </div>
        )}
      </motion.div>
      {errorMessage && (
        <p className="text-red-500 text-sm break-words">{errorMessage}</p>
      )}

      <AnimatePresence>
        {preview && (
          <motion.button
            className="text-sm text-red-500 hover:text-red-700 underline"
            onClick={handleRemove}
            initial={{ opacity: 0, y: -5 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -5 }}
            transition={{ duration: 0.2 }}
            type="button"
          >
            Remove Image
          </motion.button>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ProfileImageUploader;
