import React, { Component, ErrorInfo, ReactNode } from 'react';
import { motion } from 'framer-motion';
import AccessibleImage from '../AccessibleImage';
import Logo from '@Assets/Images/Logo.png';

const productionFlag = import.meta.env.VITE_PRODUCTION_FLAG === 'true';

interface ErrorBoundaryProps {
  children: ReactNode;
}

interface ErrorBoundaryState {
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { error: null, errorInfo: null };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({ error, errorInfo });
  }

  render() {
    const { error, errorInfo } = this.state;

    if (productionFlag) {
      return (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5, ease: 'easeOut' }}
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100vh',
            textAlign: 'center',
            backgroundColor: '#ffe5e5',
            color: '#d32f2f',
            fontSize: '18px',
            fontWeight: 'bold',
            borderRadius: '8px',
            padding: '20px',
            boxShadow: '0px 4px 6px rgba(0,0,0,0.1)',
          }}
        >
          <AccessibleImage
            src={Logo}
            alt="logo"
            className={`w-[50px] h-[30px]`}
          />
          <p>Something went wrong. Please refresh the page!</p>
        </motion.div>
      );
    }

    if (errorInfo) {
      return (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.5, ease: 'easeInOut' }}
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'left',
            justifyContent: 'left',
            height: '100vh',
            backgroundColor: '#f8f9fa',
            color: '#333',
            borderRadius: '12px',
            padding: '30px',
            boxShadow: '0px 4px 10px rgba(0,0,0,0.1)',
          }}
        >
          <AccessibleImage
            src={Logo}
            alt="logo"
            className={`w-[50px] h-[30px]`}
          />
          <motion.h3
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            style={{
              fontSize: '24px',
              fontWeight: 'bold',
              color: '#d32f2f',
              textAlign: 'left',
            }}
          >
            {error?.message || 'Oh no, an error occurred!'}
          </motion.h3>
          <motion.pre
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            style={{
              backgroundColor: '#fff',
              border: '1px solid #ddd',
              borderRadius: '8px',
              padding: '15px',
              fontSize: '14px',
              overflow: 'auto',
              maxWidth: '100%',
              marginTop: '10px',
              whiteSpace: 'pre-wrap',
            }}
          >
            {error?.stack ||
              'No stack trace available. Check console for details.'}
          </motion.pre>
        </motion.div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
