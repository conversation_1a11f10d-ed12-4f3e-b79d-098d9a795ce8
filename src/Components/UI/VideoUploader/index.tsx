import React, { useRef, useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import clsx from 'clsx';
import { useController, Control } from 'react-hook-form';
import { VideoIcon } from '@Icons'; // Replace with your actual video icon

type VideoUploaderProps = {
  name: string;
  errorMessage?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  control: Control<any>;
  defaultVideo?: string | File | undefined;
  hideRemove?: boolean;
};

const VideoUploader: React.FC<VideoUploaderProps> = ({
  name,
  control,
  errorMessage,
  defaultVideo,
  hideRemove = false,
}) => {
  const {
    field: { value, onChange, ref },
  } = useController({
    name,
    control,
  });

  const [preview, setPreview] = useState<string | null>(
    defaultVideo ? (defaultVideo as string) : ''
  );
  const [isDragging, setIsDragging] = useState(false);
  const inputRef = useRef<HTMLInputElement | null>(null);

  useEffect(() => {
    if (value instanceof File) {
      const objectUrl = URL.createObjectURL(value);
      setPreview(objectUrl);
      return () => URL.revokeObjectURL(objectUrl);
    } else {
      setPreview(null);
    }
  }, [value]);

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    const file = e.dataTransfer.files?.[0];
    if (file && file.type.startsWith('video/')) {
      onChange(file);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type.startsWith('video/')) {
      onChange(file);
    }
  };

  const handleRemove = () => {
    onChange(null);
  };

  const handleClick = () => {
    if (!hideRemove) inputRef.current?.click();
  };

  useEffect(() => {
    if (defaultVideo) {
      setPreview(defaultVideo as string);
    }
  }, [defaultVideo]);

  return (
    <div className="flex flex-col items-center space-y-4">
      <motion.div
        onDragEnter={handleDragEnter}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onDragLeave={handleDragLeave}
        className={clsx(
          'flex flex-col items-center justify-center text-sm text-gray-500 text-center border border-dashed w-full h-96 group bg-gray-100 rounded-xl border-gray-300 transition-all relative overflow-hidden cursor-pointer',
          isDragging && 'border-blue-400 bg-blue-50'
        )}
        initial={{ scale: 1 }}
        animate={{ scale: isDragging ? 1.05 : 1 }}
        transition={{ type: 'spring', stiffness: 300, damping: 20 }}
        onClick={handleClick}
      >
        <input
          ref={(e) => {
            inputRef.current = e;
            if (ref) ref(e);
          }}
          type="file"
          className="sr-only"
          accept="video/*"
          onChange={handleFileChange}
        />
        {preview ? (
          <video
            src={preview}
            controls
            className="w-full h-full object-cover rounded-xl"
          />
        ) : (
          <div className="flex flex-col items-center">
            <div className="w-8 h-8">
              <VideoIcon />
            </div>
            {!hideRemove && <p className="text-xs mt-2">Upload Video</p>}
          </div>
        )}
      </motion.div>

      {errorMessage && (
        <p className="text-red-500 text-sm break-words">{errorMessage}</p>
      )}

      <AnimatePresence>
        {preview && !hideRemove && (
          <motion.button
            type="button"
            className="text-sm text-red-500 hover:text-red-700 underline"
            onClick={handleRemove}
            initial={{ opacity: 0, y: -5 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -5 }}
            transition={{ duration: 0.2 }}
          >
            Remove Video
          </motion.button>
        )}
      </AnimatePresence>
    </div>
  );
};

export default VideoUploader;
