/* eslint-disable @typescript-eslint/no-explicit-any */
import clsx from 'clsx';
import React from 'react';
import Select, { components, OptionProps } from 'react-select';

// Define types for the CustomOption component props
interface CustomOptionProps extends OptionProps<any, any> {
  isSelected: boolean;
}

// Custom Option component to add a tick mark for the selected option
const CustomOption: React.FC<CustomOptionProps> = (props) => {
  return (
    <components.Option {...props}>
      <div className="flex items-center justify-between">
        <span>{props.label}</span>
        {/* {props.isSelected && <CircleTickIcon height={20} width={20} />} */}
      </div>
    </components.Option>
  );
};

// Define the type for the props of InputSelect component
interface InputSelectProps {
  options: Array<{ label: string; value: string }>;
  label: string | boolean;
  field?: {
    onBlur: () => void;
    onChange: (value: any) => void;
    value: any;
  };
  errorMessage?: string;
  disabled?: boolean;
  containerClassName?: string;
  onChange?: (value: any) => void;
  paddingBlock?: string;
  [key: string]: unknown;
}

const InputSelect: React.FC<InputSelectProps> = ({
  options,
  label,
  field,
  errorMessage,
  disabled = false,
  containerClassName = 'pt-2',
  onChange,
  paddingBlock = '10px',
  ...rest
}) => {
  const customStyles = {
    control: (provided: any, state: any) => ({
      ...provided,
      borderRadius: '10px',
      paddingBlock,
      paddingLeft: '4px',
      cursor: disabled ? 'not-allowed' : 'pointer',
      backgroundColor: disabled ? '#e5e7eb' : '#fff',
      borderColor:
        state.isFocused && !errorMessage
          ? '#FF8800'
          : errorMessage
            ? '#ef4444'
            : '#E0DEF7',

      boxShadow: state.isFocused ? '0px 0px 14px rgba(0,0,0,0.15)' : 'none',

      '&:hover': {
        borderColor: errorMessage ? '#ef4444' : '#FF8800',
        backgroundColor: disabled ? '#e5e7eb' : '#fff',
        cursor: disabled ? 'not-allowed' : 'pointer',
      },
    }),
    singleValue: (provided: any) => ({
      ...provided,
      color: disabled ? 'grey' : '#000',
    }),
    option: (provided: any, state: any) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? '#ffe3c5'
        : state.isFocused
          ? '#f3f4f6'
          : null,
      color: state.isSelected ? '#FF8800' : '#000',
      '&:hover': {
        backgroundColor: '#f3f4f6',
      },
    }),
    indicatorSeparator: () => ({
      display: 'none', // Remove separator
    }),
    dropdownIndicator: (provided: any) => ({
      ...provided,
      padding: '0px 4px 0px 1px', // Remove left padding from arrow icon
    }),
    menuList: (provided: any) => ({
      ...provided,
      maxHeight: '200px', // Adjust height if needed
      overflowY: 'auto', // Keep scrollbar visible
      scrollbarWidth: 'thin', // Standard property for Firefox
      '&::-webkit-scrollbar': {
        width: '6px', // Reduce scrollbar width for Chrome, Safari
      },
      '&::-webkit-scrollbar-thumb': {
        background: '#ccc', // Scroll thumb color
        borderRadius: '10px',
      },
      '&::-webkit-scrollbar-track': {
        background: 'transparent', // Hide scrollbar track
      },
    }),
    menuPortal: (base: any) => ({ ...base, zIndex: 9999 }),
    menu: (base: any) => ({ ...base, position: 'absolute' }),
  };

  return (
    <div
      className={clsx(
        'flex flex-col gap-1.5 w-full select-none',
        containerClassName
      )}
    >
      <label className="block text-sm font-medium text-gray-700">{label}</label>
      <Select
        options={options}
        styles={customStyles}
        className="w-full"
        classNamePrefix="react-select"
        components={{ Option: CustomOption }} // Use the custom Option component
        isDisabled={disabled}
        menuPosition="fixed"
        menuPortalTarget={document.body}
        {...field}
        {...rest}
        onChange={(val) => {
          if (onChange) {
            onChange(val);
          } else {
            field && field.onChange(val);
          }
        }}
      />
      {errorMessage && (
        <p className="text-red-500 text-sm break-words">{errorMessage}</p>
      )}
    </div>
  );
};

export default InputSelect;
