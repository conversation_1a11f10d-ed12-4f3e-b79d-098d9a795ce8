import React, { ReactNode } from 'react';
import clsx from 'clsx';
import { CloseIcon } from '@Icons';
import { motion, AnimatePresence } from 'framer-motion';
import Button from '../Button';
import { t } from 'i18next';
import Divider from '../Divider';

interface ModalProps {
  isOpen: boolean;
  onClose?: () => void;
  children: ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'dxl' | 'none';
  header?: string | ReactNode;
  headerWrapperClassName?: string;
  hideCloseIcon?: boolean;
  footerButton?: boolean;
  onCancel?: () => void;
  onSave?: () => void;
  cancelLoading?: boolean;
  saveLoading?: boolean;
  renameSave?: any;
}

const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  children,
  className = '',
  size = 'md',
  header = true,
  headerWrapperClassName,
  hideCloseIcon,
  footerButton,
  onCancel,
  onSave,
  cancelLoading,
  saveLoading,
  renameSave,
}) => {
  const sizeClasses: Record<
    'sm' | 'md' | 'lg' | 'xl' | 'dxl' | 'none',
    string
  > = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    dxl: 'max-w-2xl',
    none: '',
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center overflow-hidden outline-none focus:outline-none max-h-screen"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
        >
          {/* Overlay */}
          <motion.div
            className="fixed inset-0 bg-black opacity-50"
            onClick={onClose}
            initial={{ opacity: 0 }}
            animate={{ opacity: 0.5 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
          ></motion.div>

          {/* Modal Container */}
          <motion.div
            className={clsx(
              'relative w-full mx-auto flex flex-col overflow-hidden bg-white rounded-lg shadow-xl transform transition-all max-h-[95vh]',
              sizeClasses[size],
              className
            )}
            initial={false}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.98 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
          >
            {/* Modal Header */}
            {header && (
              <div
                className={clsx(
                  'flex items-center justify-between p-[33px]',
                  hideCloseIcon ? 'pb-0' : 'pb-[33px]',
                  headerWrapperClassName
                )}
              >
                <h3 className="text-lg font-semibold text-gray-900">
                  {header}
                </h3>
                {!hideCloseIcon && (
                  <button
                    type="button"
                    className="absolute right-7 text-gray-400 bg-transparent hover:scale-110 cursor-pointer rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center"
                    onClick={onClose}
                  >
                    <CloseIcon height={14} width={14} stroke="currentColor" />
                    <span className="sr-only">Close modal</span>
                  </button>
                )}
              </div>
            )}

            {/* Modal Body */}
            <div className="relative overflow-auto custom-scrollbar">
              <div className="p-[33px] pt-0">{children}</div>
              {footerButton && (
                <>
                  <Divider />
                  <div className="w-full flex justify-end">
                    <div className="pb-[13px] px-[33px] mt-4 flex gap-5  w-md">
                      <Button
                        text={t('cancel')}
                        type="reset"
                        variant="outline"
                        onClick={onCancel}
                        disabled={cancelLoading}
                      />

                      <Button
                        text={renameSave ?? t('save')}
                        type="submit"
                        onClick={onSave}
                        disabled={saveLoading}
                        loading={saveLoading}
                      />
                    </div>
                  </div>
                </>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default Modal;
