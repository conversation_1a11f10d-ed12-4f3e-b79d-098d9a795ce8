import { motion } from 'framer-motion';
type ProgressBarProps = {
  progress: number;
  isCustomProgrssTitle?: string;
};

const LinearProgressbar = ({
  progress,
  isCustomProgrssTitle,
}: ProgressBarProps) => {
  const isInfinite = progress <= 0;

  return (
    <div className="space-y-1">
      <label className="text-sm font-medium text-left text-gray-800">
        {isCustomProgrssTitle
          ? isCustomProgrssTitle
          : isInfinite
            ? 'Initializing upload...'
            : `Uploading ${progress}%`}
      </label>

      <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden relative shadow-inner mt-2">
        {isInfinite ? (
          <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
            <div className="infinite-loader" />
          </div>
        ) : (
          <div
            className="h-full bg-gradient-to-l from-primary-100 to-[#FFA033] rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          />
        )}
      </div>
    </div>
  );
};

const ProgressBar = ({
  progress,
  variant,
  progressTitle = '',
  isCustomProgrssTitle,
}: {
  progress: number;
  variant: 'circular' | 'linear';
  progressTitle?: string;
  isCustomProgrssTitle?: string;
}) => {
  return (
    <>
      {variant === 'circular' && (
        <CircularProgressBar
          progress={progress}
          progressTitle={progressTitle}
        />
      )}{' '}
      {variant === 'linear' && (
        <LinearProgressbar
          progress={progress}
          isCustomProgrssTitle={isCustomProgrssTitle}
        />
      )}{' '}
    </>
  );
};

type CircularProgressBarProps = {
  progress: number; // 0–100, or -1 for infinite
  size?: number; // default size in px
  strokeWidth?: number;
  progressTitle?: string;
};

const CircularProgressBar = ({
  progress,
  size = 70,
  strokeWidth = 8,
  progressTitle,
}: CircularProgressBarProps) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const isInfinite = progress < 0;

  const strokeDashoffset = isInfinite
    ? circumference
    : circumference - (progress / 100) * circumference;

  return (
    <div
      className="relative flex items-center justify-center"
      style={{ width: size, height: size }}
    >
      <svg width={size} height={size}>
        <circle
          className="text-gray-300"
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="transparent"
          r={radius}
          cx={size / 2}
          cy={size / 2}
        />
        <motion.circle
          className="text-teal-400"
          stroke="currentColor"
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          fill="transparent"
          r={radius}
          cx={size / 2}
          cy={size / 2}
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
          animate={
            isInfinite
              ? {
                  rotate: 360,
                  strokeDashoffset: [circumference, circumference * 0.75],
                }
              : { strokeDashoffset }
          }
          initial={false}
          transition={
            isInfinite
              ? {
                  repeat: Infinity,
                  ease: 'linear',
                  duration: 1.2,
                }
              : {
                  ease: 'easeInOut',
                  duration: 0.4,
                }
          }
          style={{
            transformOrigin: 'center',
          }}
        />
      </svg>

      {!isInfinite && (
        <div className="absolute text-sm font-semibold text-gray-700">
          {progressTitle}
        </div>
      )}
    </div>
  );
};

export default ProgressBar;
