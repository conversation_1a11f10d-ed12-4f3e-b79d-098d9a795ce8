import React from 'react';
import clsx from 'clsx';
import { Loader } from '@Icons';

export interface ButtonProps {
  className?: string;
  onClick?: () => void;
  text: React.ReactNode;
  type?: 'button' | 'submit' | 'reset';
  loading?: boolean;
  disabled?: boolean;
  variant?: 'primary' | 'outline' | 'other';
  width?: string;
  style?: React.CSSProperties;
}

const Button: React.FC<ButtonProps> = ({
  className = '',
  onClick,
  text,
  type = 'button',
  loading = false,
  disabled = false,
  variant = 'primary',
  width,
  style,
}) => {
  return (
    <button
      className={clsx(
        `px-[15px] py-2 rounded-lg transition-all duration-200 text-sm font-medium hover:shadow-[0px_0px_14px_rgba(0,0,0,0.15)]`,
        {
          'bg-gradient-to-tr from-primary-100 to-[#FFA033] text-white':
            variant === 'primary',
          'border-1 border-[#FFA033] text-gray-700 bg-transparent hover:border-primary-100':
            variant === 'outline',
          'opacity-80 cursor-not-allowed': loading || disabled,
          'cursor-pointer': !(loading || disabled), // Ensures pointer only when NOT disabled or loading
        },
        width ? width : 'w-full',
        className
      )}
      onClick={onClick}
      type={type}
      disabled={loading || disabled}
      style={style}
    >
      {!loading ? text : <Loader height={20} width={20} fill="#fff" />}
    </button>
  );
};

export default Button;
