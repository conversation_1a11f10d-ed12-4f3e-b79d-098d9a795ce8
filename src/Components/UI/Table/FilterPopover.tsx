import { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FilterIcon } from '@Icons';
import { Column } from '@tanstack/react-table';
import { createPopper } from '@popperjs/core';
import clsx from 'clsx';
import Button from '../Button';

type FilterProps<TData> = {
  column: Column<TData, unknown>;
  options: { label: string; value: string }[];
  onFilterChange?: (value: string) => void; // Callback to parent
};

export default function FilterPopover<TData>({
  column,
  options,
  onFilterChange,
}: FilterProps<TData>) {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const popoverRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isOpen && buttonRef.current && popoverRef.current) {
      createPopper(buttonRef.current, popoverRef.current, {
        placement: 'bottom-end', // Ensures proper positioning
        modifiers: [
          { name: 'preventOverflow', options: { boundary: 'viewport' } },
          { name: 'offset', options: { offset: [0, 8] } }, // Adds spacing
        ],
      });
    }

    // Close when clicking outside
    function handleClickOutside(event: MouseEvent) {
      if (
        popoverRef.current &&
        !popoverRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleFilterChange = (value: string) => {
    column.setFilterValue(value);
    onFilterChange?.(value);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      {/* Filter Button */}
      <button
        ref={buttonRef}
        onClick={() => setIsOpen((prev) => !prev)}
        className="focus:outline-none"
      >
        <FilterIcon
          height={16}
          width={16}
          fill={column.getIsFiltered() ? '#FF8800' : '#9CA3AF'}
          className="cursor-pointer text-gray-600 hover:text-black transition"
        />
      </button>

      {/* Popover */}
      {isOpen && (
        <motion.div
          ref={popoverRef}
          initial={{ opacity: 0, y: -8 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -8 }}
          className="absolute bg-white shadow-lg rounded-lg border border-gray-300 z-50 w-50 max-h-45 overflow-y-auto"
          style={{ zIndex: 1000 }}
        >
          {/* Filter Options */}
          <div className="flex flex-col space-y-2 pt-3">
            {options.map((option) => (
              <label
                key={option.value}
                className="select-none flex items-start space-x-2 px-3 py-1 cursor-pointer text-gray-700 hover:text-black"
              >
                <input
                  type="radio"
                  name={`filter-${column.id}`}
                  value={option.value}
                  checked={column.getFilterValue() === option.value}
                  onChange={() => handleFilterChange(option.value)}
                  className="hidden"
                />
                <div
                  className={clsx(
                    'w-4 h-4 rounded-full border-2 flex-shrink-0 flex items-center justify-center transition-all',
                    column.getFilterValue() === option.value
                      ? 'border-[#FF8800] bg-[#FF8800]'
                      : 'bg-white'
                  )}
                >
                  {column.getFilterValue() === option.value && (
                    <div className="w-2 h-2 bg-white rounded-full" />
                  )}
                </div>

                <span className="break-words max-w-[160px] text-sm leading-snug">
                  {option.label}
                </span>
              </label>
            ))}
          </div>

          {/* Clear Filter */}
          <div className="p-3">
            <Button
              text="Reset"
              variant="outline"
              className="border border-gray-400 h-10"
              onClick={() => handleFilterChange('')}
            />
          </div>
        </motion.div>
      )}
    </div>
  );
}
