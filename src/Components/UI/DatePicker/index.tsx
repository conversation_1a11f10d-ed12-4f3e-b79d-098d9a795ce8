import React from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import clsx from 'clsx';
import { Controller } from 'react-hook-form';
// Assuming these icons are available in your project
import { InfoIcon, ErrorIcon, Loader } from '@Icons';
import Tooltip from '../Tooltip';

interface CustomDatePickerProps {
  // Form control props
  control?: any;
  name?: string;

  // Direct onChange props
  value?: Date | null | { startDate: Date | null; endDate: Date | null };
  onChange?: (
    date: Date | null | { startDate: Date | null; endDate: Date | null }
  ) => void;

  // Common props
  isSingleMode: boolean;
  label?: string | boolean;
  placeholder?: string;
  errorMessage?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  infoMsg?: string;
  containerClassName?: string;
  inputContainerClassName?: string;
  inputClassName?: string;
  labelColor?: string;
  disabled?: boolean;
  loading?: boolean;
  showErrorIcon?: boolean;
  minDate?: Date;
}

const CustomDatePicker: React.FC<CustomDatePickerProps> = ({
  // Form control props
  control,
  name,

  // Direct onChange props
  value,
  onChange,

  // Common props
  isSingleMode,
  label,
  placeholder,
  errorMessage,
  leftIcon,
  rightIcon,
  infoMsg = '',
  containerClassName = 'pt-2',
  inputContainerClassName = 'max-h-[46px]',
  inputClassName = '',
  labelColor = 'text-gray-700',
  disabled = false,
  loading = false,
  showErrorIcon = true,
  minDate,
}) => {
  // Determine if we're using form control or direct onChange
  const isFormControlled = control && name;

  const renderDatePickerContent = (fieldValue: any, handleChange: any) => {
    if (isSingleMode) {
      return (
        <DatePicker
          selected={fieldValue ? new Date(fieldValue) : null}
          onChange={(date) => handleChange(date)}
          showPopperArrow={false}
          dateFormat="dd/MM/yyyy"
          disabled={disabled}
          placeholderText={placeholder || 'Select date'}
          minDate={minDate}
          showYearDropdown
          scrollableYearDropdown
          showMonthDropdown
          todayButton="Today"
          className={clsx(
            'w-full flex-1 p-1.5 border-none outline-none appearance-none disabled:bg-[#e5e7eb] disabled:cursor-not-allowed text-base',
            inputClassName
          )}
        />
      );
    } else {
      return (
        <DatePicker
          selected={
            fieldValue?.startDate ? new Date(fieldValue.startDate) : null
          }
          onChange={(dates) => {
            // Fix for date range selection
            // When using selectsRange, the dates param is [startDate, endDate]
            if (Array.isArray(dates)) {
              const [start, end] = dates;
              handleChange({
                startDate: start,
                endDate: end,
              });
            } else {
              // Fallback handling if dates are not returned as expected
              handleChange(dates);
            }
          }}
          startDate={
            fieldValue?.startDate ? new Date(fieldValue.startDate) : null
          }
          endDate={fieldValue?.endDate ? new Date(fieldValue.endDate) : null}
          showYearDropdown
          scrollableYearDropdown
          showMonthDropdown
          todayButton="Today"
          selectsRange
          showPopperArrow={false}
          dateFormat="dd/MM/yyyy"
          disabled={disabled}
          placeholderText={placeholder || 'Select date range'}
          minDate={minDate}
          className={clsx(
            'w-full flex-1 p-1.5 border-none outline-none appearance-none disabled:bg-[#e5e7eb] disabled:cursor-not-allowed text-base',
            inputClassName
          )}
        />
      );
    }
  };

  return (
    <div
      className={clsx(
        'flex flex-col gap-1.5 w-full relative select-none',
        containerClassName
      )}
    >
      {/* Label & Info Icon */}
      {(label || infoMsg) && (
        <div className="flex items-center">
          {label && (
            <label
              className={clsx('text-sm font-medium text-left', labelColor)}
            >
              {label}
            </label>
          )}
          {!!infoMsg && (
            <div className="relative group flex cursor-pointer pl-1.5">
              <Tooltip content={infoMsg}>
                <InfoIcon height={16} width={16} />
              </Tooltip>
            </div>
          )}
        </div>
      )}

      {isFormControlled ? (
        // Form-controlled mode
        <Controller
          name={name || ''}
          control={control}
          render={({ field }) => (
            <div
              className={clsx(
                'flex items-center flex-1  px-4 border rounded-[10px] border-[#E0DEF7] focus-within:ring-1 focus-within:ring-teal-100 focus-within:shadow-[0px_0px_14px_rgba(0,0,0,0.15)]',
                inputContainerClassName,
                disabled
                  ? 'bg-[#e5e7eb] cursor-not-allowed focus-within:border-[#E0DEF7] hover:border-[#E0DEF7]'
                  : 'bg-white hover:border-primary-100 focus-within:border-primary-100',
                errorMessage &&
                  'border-red-500 focus-within:border-red-500 focus-within:shadow-red-200 shadow-red-200 hover:border-red-500'
              )}
            >
              {leftIcon && <div className="flex pr-2">{leftIcon}</div>}
              {renderDatePickerContent(field.value, field.onChange)}
              {(rightIcon || loading) && (
                <div className="flex items-center">
                  {loading ? (
                    <Loader height={20} width={20} fill="#fff" />
                  ) : (
                    rightIcon
                  )}
                </div>
              )}
              {errorMessage && !rightIcon && showErrorIcon && <ErrorIcon />}
            </div>
          )}
        />
      ) : (
        // Direct onChange mode
        <div
          className={clsx(
            'flex items-center flex-1 py-6 px-4 border rounded-[10px] border-[#E0DEF7] focus-within:ring-1 focus-within:ring-teal-100 focus-within:shadow-[0px_0px_14px_rgba(0,0,0,0.15)]',
            inputContainerClassName,
            disabled
              ? 'bg-[#e5e7eb] cursor-not-allowed focus-within:border-[#E0DEF7] hover:border-[#E0DEF7]'
              : 'bg-white hover:border-primary-100 focus-within:border-primary-100',
            errorMessage &&
              'border-red-500 focus-within:border-red-500 focus-within:shadow-red-200 shadow-red-200 hover:border-red-500'
          )}
        >
          {leftIcon && <div className="flex pr-2">{leftIcon}</div>}
          {renderDatePickerContent(value, onChange || (() => {}))}
          {(rightIcon || loading) && (
            <div className="flex items-center">
              {loading ? (
                <Loader height={20} width={20} fill="#fff" />
              ) : (
                rightIcon
              )}
            </div>
          )}
          {errorMessage && !rightIcon && showErrorIcon && <ErrorIcon />}
        </div>
      )}

      {/* Error Message */}
      {errorMessage && (
        <p className="text-red-500 text-sm break-words">{errorMessage}</p>
      )}
    </div>
  );
};

export default CustomDatePicker;
