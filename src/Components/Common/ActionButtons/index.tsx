import { Switch } from '@Components/UI';
import { SwitchProps } from '@Components/UI/Switch';
import {
  ClockCounterwiseIcon,
  DeleteIcon,
  IgnoreIcon,
  EditIcon,
  EyeIcon,
  ReportIcon,
} from '@Icons';
import clsx from 'clsx';
import { MouseEventHandler, useEffect } from 'react';

interface ActionButtonsProps {
  deleteIcon?: boolean;
  ignoreIcon?: boolean;
  reportIcon?: boolean;
  deleteIconDisabled?: boolean;
  ignoreIconDisabled?: boolean;
  editIcon?: boolean;
  editIconDisabled?: boolean;
  viewIcon?: boolean;
  isSwitch?: boolean;
  isPurchaseHistory?: boolean;
  switchProps?: SwitchProps;
  onDelete?: MouseEventHandler<SVGSVGElement>;
  onIgnore?: MouseEventHandler<SVGSVGElement>;
  onEdit?: MouseEventHandler<SVGSVGElement>;
  onView?: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><SVGSVGElement>;
  onReport?: <PERSON><PERSON><PERSON><PERSON>and<PERSON><SVGSVGElement>;
  onPurchaseHistoryView?: <PERSON><PERSON><PERSON><PERSON>and<PERSON><SVGSVGElement>;
  loadingView?: boolean;
  loadingPurchaseHistory?: boolean;
  loadingEdit?: boolean;
  reportCount?: number;
  viewTooltip?: string;
  editTooltip?: string;
  deleteTooltip?: string;
  ignoreTooltip?: string;
  reportTooltip?: string;
  historyTooltip?: string;
}

const ActionButtons = ({
  deleteIcon,
  ignoreIcon,
  reportIcon,
  editIcon,
  viewIcon,
  onDelete,
  onIgnore,
  onEdit,
  onView,
  onReport,
  isSwitch,
  switchProps,
  isPurchaseHistory,
  editIconDisabled,
  deleteIconDisabled,
  ignoreIconDisabled,
  onPurchaseHistoryView,
  loadingView,
  loadingEdit,
  loadingPurchaseHistory,
  reportCount,
  viewTooltip = 'View',
  editTooltip = 'Edit',
  deleteTooltip = 'Delete',
  ignoreTooltip = 'Ignore',
  reportTooltip = 'Report',
  historyTooltip = 'Purchase History',
}: ActionButtonsProps) => {
  useEffect(() => {
    const updateMousePosition = (e: MouseEvent) => {
      document.documentElement.style.setProperty('--mouse-x', `${e.clientX}px`);
      document.documentElement.style.setProperty('--mouse-y', `${e.clientY}px`);
    };

    window.addEventListener('mousemove', updateMousePosition);

    return () => {
      window.removeEventListener('mousemove', updateMousePosition);
    };
  }, []);
  return (
    <div className="flex gap-4">
      {isSwitch && <Switch {...switchProps} />}

      {viewIcon && (
        <div className="relative group">
          <EyeIcon
            height={20}
            width={20}
            className={`cursor-pointer ${loadingView ? 'opacity-50' : ''}`}
            onClick={onView && !loadingView ? onView : () => null}
          />
          <div
            className="fixed transform -translate-x-1/2 hidden group-hover:block bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-nowrap z-[9999] pointer-events-none"
            style={{
              top: 'calc(var(--mouse-y, 0) - 30px)',
              left: 'var(--mouse-x, 0)',
            }}
          >
            {viewTooltip}
          </div>
        </div>
      )}

      {reportIcon && (
        <div className="relative group cursor-pointer">
          <ReportIcon
            height={20}
            width={20}
            className={`${loadingView ? 'opacity-50' : ''}`}
            onClick={onReport && !loadingView ? onReport : () => null}
          />
          {reportCount !== undefined && reportCount > 0 && (
            <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              {reportCount}
            </span>
          )}
          <div
            className="fixed transform -translate-x-1/2 hidden group-hover:block bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-nowrap z-[9999] pointer-events-none"
            style={{
              top: 'calc(var(--mouse-y, 0) - 30px)',
              left: 'var(--mouse-x, 0)',
            }}
          >
            {reportTooltip}
          </div>
        </div>
      )}

      {editIcon && (
        <div className="relative group">
          <EditIcon
            height={16}
            width={16}
            className={clsx(
              loadingEdit ? 'opacity-60' : '',
              editIconDisabled
                ? 'cursor-not-allowed opacity-60'
                : 'cursor-pointer'
            )}
            onClick={editIconDisabled ? undefined : onEdit}
          />
          <div
            className="fixed transform -translate-x-1/2 hidden group-hover:block bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-nowrap z-[9999] pointer-events-none"
            style={{
              top: 'calc(var(--mouse-y, 0) - 30px)',
              left: 'var(--mouse-x, 0)',
            }}
          >
            {editTooltip}
          </div>
        </div>
      )}

      {ignoreIcon && (
        <div className="relative group">
          <IgnoreIcon
            height={18}
            width={18}
            className={
              ignoreIconDisabled
                ? 'cursor-not-allowed opacity-60 '
                : 'cursor-pointer'
            }
            onClick={ignoreIconDisabled ? undefined : onIgnore}
          />
          <div
            className="fixed transform -translate-x-1/2 hidden group-hover:block bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-nowrap z-[9999] pointer-events-none"
            style={{
              top: 'calc(var(--mouse-y, 0) - 30px)',
              left: 'var(--mouse-x, 0)',
            }}
          >
            {ignoreTooltip}
          </div>
        </div>
      )}

      {deleteIcon && (
        <div className="relative group">
          <DeleteIcon
            height={18}
            width={18}
            className={
              deleteIconDisabled
                ? 'cursor-not-allowed opacity-60 '
                : 'cursor-pointer'
            }
            onClick={deleteIconDisabled ? undefined : onDelete}
          />
          <div
            className="fixed transform -translate-x-1/2 hidden group-hover:block bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-nowrap z-[9999] pointer-events-none"
            style={{
              top: 'calc(var(--mouse-y, 0) - 30px)',
              left: 'var(--mouse-x, 0)',
            }}
          >
            {deleteTooltip}
          </div>
        </div>
      )}

      {isPurchaseHistory && (
        <div className="relative group">
          <ClockCounterwiseIcon
            height={18}
            width={18}
            className={`cursor-pointer ${loadingPurchaseHistory ? 'opacity-50' : ''}`}
            onClick={
              onPurchaseHistoryView && !loadingPurchaseHistory
                ? onPurchaseHistoryView
                : () => null
            }
          />
          <div
            className="fixed transform -translate-x-1/2 hidden group-hover:block bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-nowrap z-[9999] pointer-events-none"
            style={{
              top: 'calc(var(--mouse-y, 0) - 30px)',
              left: 'var(--mouse-x, 0)',
            }}
          >
            {historyTooltip}
          </div>
        </div>
      )}
    </div>
  );
};

export default ActionButtons;
