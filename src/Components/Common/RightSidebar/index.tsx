import { Divider } from '@Components/UI';
import { CloseIcon } from '@Icons';
import { motion } from 'framer-motion';
import { ReactNode } from 'react';

interface RightSidebarProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  children: ReactNode;
  title?: string;
  subTitle?: string;
  onClose?: () => void;
}

const RightSidebar: React.FC<RightSidebarProps> = ({
  open,
  setOpen,
  children,
  title,
  subTitle,
  onClose,
}) => {
  return (
    <>
      {/* Overlay (Click to Close) */}
      {open && (
        <motion.div
          className="fixed inset-0 bg-black bg-opacity-30 backdrop-blur-md z-40"
          initial={false}
          animate={{ opacity: 0.5 }}
          exit={{ opacity: 0 }}
          onClick={() => setOpen(false)}
        />
      )}

      {/* Sidebar Panel */}
      <motion.div
        className="fixed top-0 right-0 h-full w-[500px] bg-white shadow-xl  z-50 flex flex-col"
        initial={{ x: '100%' }}
        animate={{ x: open ? '0%' : '100%' }}
        transition={{ type: 'spring', stiffness: 200, damping: 25 }}
      >
        {/* Close Button */}
        <button
          onClick={() => {
            setOpen(false);
            if (onClose) onClose();
          }}
          className="absolute top-4 right-4 cursor-pointer"
          aria-label="Close Sidebar"
        >
          <CloseIcon height={18} width={18} />
        </button>

        {/* Sidebar Content */}
        <div className="mt-1 p-6">
          <h1 className="text-3xl">{title}</h1>
          <small>{subTitle}</small>
        </div>
        <Divider />
        <div className="p-6 overflow-y-auto custom-scrollbar">{children}</div>
      </motion.div>
    </>
  );
};

export default RightSidebar;
