import React, { ReactNode, useEffect, useRef, useState } from 'react';
import { CaretRightIcon, LogoutIcon } from '@Icons';
import clsx from 'clsx';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router';

interface MenuItemProps {
  keyName: string;
  title: string | ReactNode;
  Icon?: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  showCaretIcon?: boolean;
  isCollapsed?: boolean;
  onMenuClick?: () => void;
  pathname: string;
  children?: boolean;
  handleLogout?: React.MouseEventHandler<HTMLButtonElement>;
  childData?: {
    key: string;
    title: string;
    Icon?: React.ComponentType<React.SVGProps<SVGSVGElement>>;
    path?: string;
    activeHelper?: string[];
  }[];
  activeHelper?: string[];
}

const MenuItem: React.FC<MenuItemProps> = ({
  keyName,
  title,
  Icon,
  isCollapsed = false,
  onMenuClick,
  pathname,
  children = false,
  showCaretIcon,
  childData = [],
  handleLogout,
  activeHelper,
}) => {
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(false);
  const hasMounted = useRef(false); // Track initial mount

  useEffect(() => {
    if (!hasMounted.current) {
      if (
        childData.some(
          (child) =>
            location.pathname === child.path ||
            (child.activeHelper?.includes(location.pathname) ?? false)
        )
      ) {
        setIsOpen(true);
      }
      hasMounted.current = true;
    }
  }, [location.pathname, childData]);

  const isActive =
    location.pathname === pathname ||
    (activeHelper?.includes(location.pathname) ?? false) ||
    childData.some((child) => location.pathname === child.path);

  const isChildActive = (child: (typeof childData)[number]) =>
    location.pathname === child.path ||
    (child.activeHelper?.includes(location.pathname) ?? false);

  const menuClickHandler = () => {
    if (onMenuClick) onMenuClick();

    setIsOpen((prev) => !prev);
  };

  return (
    <li key={keyName} className="w-full">
      <button
        onClick={menuClickHandler}
        className={clsx(
          'relative w-full flex items-center p-4 pl-7 space-x-3 text-left hover:bg-[#F4F4FB] text-[#333333] cursor-pointer',
          !children &&
            isActive &&
            'bg-gradient-to-l from-primary-100 to-[#FFA033] text-white shadow-lg'
        )}
      >
        {Icon && (
          <Icon
            height={16}
            width={16}
            fill={!children && isActive ? '#fff' : '#FF8800'}
          />
        )}
        {!isCollapsed && (
          <span className="text-base font-medium max-w-[190px] truncate whitespace-nowrap overflow-hidden">
            {title}
          </span>
        )}

        {!isCollapsed && children && childData.length > 0 && (
          <CaretRightIcon
            height={16}
            width={16}
            className={clsx(
              'absolute right-5 transition-transform',
              isOpen ? 'rotate-90' : 'rotate-0'
            )}
            fill={!children && isActive ? '#fff' : '#FF8800'}
          />
        )}
        {!isCollapsed && showCaretIcon && (
          <button onClick={handleLogout}>
            <LogoutIcon
              height={16}
              width={16}
              fill={isActive ? '#fff' : '#FF8800'}
              className="cursor-pointer absolute right-8 top-6"
            />
          </button>
        )}
      </button>

      {children && childData.length > 0 && (
        <motion.div
          initial={false}
          animate={{ height: isOpen ? 'auto' : 0, opacity: isOpen ? 1 : 0 }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
          className="overflow-hidden"
        >
          {childData.map((child) => (
            <button
              key={child.key}
              onClick={() => navigate(child.path ?? '#')}
              className={clsx(
                'relative w-full flex items-center p-4 pl-11 space-x-3 text-left hover:bg-[#F4F4FB] text-[#333333] cursor-pointer',
                isChildActive(child) &&
                  'bg-gradient-to-l from-primary-100 to-[#FFA033] text-white shadow-lg'
              )}
            >
              {child.Icon && (
                <child.Icon
                  height={16}
                  width={16}
                  fill={isChildActive(child) ? '#fff' : '#FF8800'}
                />
              )}
              {!isCollapsed && (
                <span className="text-base font-medium">{child.title}</span>
              )}
            </button>
          ))}
        </motion.div>
      )}
    </li>
  );
};

export default MenuItem;
