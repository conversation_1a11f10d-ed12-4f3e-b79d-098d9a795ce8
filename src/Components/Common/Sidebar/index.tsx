import Logo from '@Assets/Images/Logo2.png';
import Robo<PERSON>ogo from '@Assets/Images/Logo2.png';
import { useEffect, useState } from 'react';
import clsx from 'clsx';
import AccessibleImage from '../../UI/AccessibleImage';
import MenuItem from '../MenuItem';
import { useNavigate } from 'react-router';
import { Button, Modal } from '@Components/UI';
import { useLogout } from '@Query/Hooks/useAuth';
import menuItems, { MenuItemType } from '@Config/MenuItems.config';
import { Loader, LogoutIcon } from '@Icons';
import { useSelector } from 'react-redux';
import { RootState } from '@Redux/store';
import { fetchPermissions } from '@Redux/SystemControl/PermissionControl';
import { useAppDispatch } from '@Hooks/useAppDispatch';
import { useGetAdminProfile } from '@Query/Hooks/useAdmin';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import queryClient from '@Helpers/QueryClient';
import CacheKeys from '@Helpers/CacheKeys';
import PATHS from '@Config/Path.Config';

const Sidebar: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { addToast } = useToast();

  const {
    mutate: logout,
    data: logoutData,
    isLoading: logoutIsLoading,
    isSuccess: logoutSuccess,
    isError: logoutIsError,
    error: logoutError,
  } = useLogout();

  const [openLogoutModal, setOpenLogoutModal] = useState<boolean>(false);
  const sidebarStorage = JSON.parse(localStorage.getItem('sidebar') || 'false');
  const [isCollapsed, setIsCollapsed] = useState(sidebarStorage);

  const toggleSidebar = () => {
    const newState = !isCollapsed;
    setIsCollapsed(newState);
    localStorage.setItem('sidebar', String(newState));
  };

  const { data: viewProfileData, isLoading: viewProfileLoading } =
    useGetAdminProfile();

  const roleName = viewProfileData?.data?.data?.role;

  const { data: permissions } = useSelector(
    (state: RootState) => state.PermissionControl
  );

  useEffect(() => {
    if (roleName) {
      dispatch(fetchPermissions(roleName));
    }
  }, [roleName, dispatch]);

  useEffect(() => {
    if (logoutSuccess) {
      addToast('success', logoutData?.data?.message);
      setOpenLogoutModal(false);
      localStorage.removeItem('authaccess');

      queryClient.removeQueries(CacheKeys.profileData);

      navigate(PATHS.LOGIN, { replace: true });
    }
    if (logoutIsError) {
      addToast('error', logoutError as string);
    }
  }, [logoutError, logoutIsError, logoutSuccess, logoutData]);

  type Permission = {
    name: string;
    access: {
      read: boolean;
      write: boolean;
      update: boolean;
      delete: boolean;
    };
  };

  const filterMenuItems = (
    items: MenuItemType[],
    permissions?: { features: Permission[] }
  ): MenuItemType[] => {
    return items
      .map((item) => {
        if (item.children) {
          // Recursively filter children
          const filteredChildren = filterMenuItems(item.children, permissions);

          // Check if this item has explicit permissions
          const hasPermission =
            !item.permissionKey ||
            (permissions?.features ?? []).some(
              (feature) =>
                feature.name === item.permissionKey && feature.access.read
            );

          // Keep the item only if it has permission AND has valid children
          if (hasPermission && filteredChildren.length > 0) {
            return {
              ...item,
              children: filteredChildren, // Keep valid children
            };
          }
          return null; // Remove the parent if it has no valid children
        } else {
          // Check permission for items without children
          if (
            !item.permissionKey ||
            (permissions?.features ?? []).some(
              (feature) =>
                feature.name === item.permissionKey && feature.access.read
            )
          ) {
            return item;
          }
        }
        return null; // Exclude the menu item if it has no permission
      })
      .filter((item): item is MenuItemType => item !== null); // Remove null entries
  };

  const filteredMenuItems: MenuItemType[] = filterMenuItems(
    roleName === 1
      ? menuItems
      : menuItems.filter((i) => i.path !== PATHS.ROLE_MANAGEMENT),
    permissions
  );

  return (
    <div
      className={clsx(
        'h-screen flex flex-col text-white shadow-lg transition-all',
        isCollapsed ? 'w-[86px]' : 'w-[390px]'
      )}
    >
      <div
        className={clsx(
          'flex z-10 items-center h-[69px] fixed top-0 border-b border-b-[#E0DEF7]',
          isCollapsed ? 'w-[86px]' : 'w-[390px]',
          isCollapsed ? 'bg-transparent' : 'bg-white'
        )}
      >
        <div
          className={clsx(
            'cursor-pointer flex items-center',
            isCollapsed ? 'ml-5' : 'ml-6'
          )}
          onClick={toggleSidebar}
        >
          {isCollapsed ? (
            <AccessibleImage
              src={RoboLogo}
              alt="logo"
              className="w-[30px] h-[20px] block object-contain"
            />
          ) : (
            <AccessibleImage
              src={Logo}
              alt="logo"
              className="w-[50px] h-[40px] block object-contain"
            />
          )}
        </div>
      </div>

      <nav className="flex-grow overflow-y-auto custom-scrollbar mt-17">
        <ul>
          {filteredMenuItems
            .filter((i) => !i.bottom)
            .map(({ key, title, Icon, path, children, activeHelper }) => (
              <MenuItem
                key={key}
                keyName={key}
                title={title}
                Icon={Icon}
                isCollapsed={isCollapsed}
                onMenuClick={() => {
                  navigate(path ?? '#');
                  dispatch(fetchPermissions(roleName));
                }}
                pathname={path ?? '#'}
                childData={children ?? []}
                children={Boolean(children?.length)}
                activeHelper={activeHelper}
              />
            ))}
        </ul>
      </nav>

      <div>
        <ul>
          {filteredMenuItems
            .filter((i) => i.bottom)
            .map(({ key, title, Icon, path, children, activeHelper }) => (
              <MenuItem
                key={key}
                keyName={key}
                title={
                  key === 'account' ? (
                    viewProfileLoading ? (
                      <Loader height={20} width={20} fill="#fff" />
                    ) : (
                      `${viewProfileData?.data?.data?.first_name} ${viewProfileData?.data?.data?.last_name}`
                    )
                  ) : (
                    title
                  )
                }
                Icon={Icon}
                showCaretIcon={key === 'account'}
                handleLogout={(e) => {
                  e.stopPropagation();
                  setOpenLogoutModal(true);
                }}
                isCollapsed={isCollapsed}
                onMenuClick={() => navigate(path ?? '#')}
                pathname={path ?? '#'}
                childData={children ?? []}
                children={Boolean(children?.length)}
                activeHelper={activeHelper}
              />
            ))}
        </ul>
      </div>

      <Modal
        hideCloseIcon
        isOpen={openLogoutModal}
        onClose={() => setOpenLogoutModal(false)}
      >
        <div className="flex flex-col items-center gap-9 justify-center">
          <div className="bg-[#ffe3c5] h-20 w-20 flex justify-center items-center rounded-full">
            <LogoutIcon width={25} height={25} fill="#FF8800" />
          </div>
          <h4 className="text-black text-2xl font-bold text-center">
            Are you sure you want to logout?
          </h4>
          <div className="flex w-full gap-6">
            <Button
              text="Yes"
              loading={logoutIsLoading}
              onClick={() => logout()}
            />
            <Button
              text="No"
              variant="outline"
              disabled={logoutIsLoading}
              onClick={() => setOpenLogoutModal(false)}
            />
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default Sidebar;
