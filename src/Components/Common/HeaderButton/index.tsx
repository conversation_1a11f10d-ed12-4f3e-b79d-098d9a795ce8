import React, { ButtonHTMLAttributes, ReactNode } from 'react';
import clsx from 'clsx';
import { Loader } from '@Icons';

// Define interface for component props
interface HeaderButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  text: string;
  icon?: ReactNode;
  isLoading?: boolean;
  extraClasses?: string;
}

const HeaderButton: React.FC<HeaderButtonProps> = ({
  text,
  icon,
  isLoading = false,
  extraClasses = '',
  ...listener
}) => {
  return (
    <button
      className={clsx(
        'py-2.5 px-4 rounded-lg border border-[#D0D5DD] shadow-custom-light flex gap-2 items-center justify-center h-[40px] min-w-[105px]',
        extraClasses
      )}
      {...listener}
    >
      {isLoading ? (
        <Loader height={20} width={20} fill="#293175" />
      ) : (
        <>
          {icon}
          <span className="text-[rgb(52,64,84)] font-semibold text-[14px] leading-5">
            {text}
          </span>
        </>
      )}
    </button>
  );
};

export default HeaderButton;
