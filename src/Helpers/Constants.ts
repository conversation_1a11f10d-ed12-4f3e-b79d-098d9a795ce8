export const API_PATHS = {
  //COUNTS
  LOGIN: 'cms/auth/login/',
  LOGOUT: 'cms/logout/',
  SEND_FORGOT_PASSWORD_EMAIL: 'cms/auth/reset-password-link/',
  RESET_PASSWORD: 'cms/auth/reset-password/',
  NEW_REFRESH_TOKEN: 'cms/auth/refresh-token/',

  //SUBADMIN
  ADDSUBADMIN: 'cms/user/',
  SUBADMIN_LIST: 'cms/users/',
  CREATE_USER: 'cms/rex/user/create/',
  EDIT_USER: 'cms/rex/users/:id/',
  TOGGLE_USER_STATUS: 'cms/rex/user/:id/toggle-status/',
  DELETE_USER: 'cms/rex/user/:id/delete/',
  SAMPLE_CSV: 'cms/sample-user-csv/',
  USER_LIST: 'cms/rex/users/list/',
  PURCHASE_HISTORY: 'cms/rex/users/:id/purchase-history/',
  EXPORT_CSV: 'cms/rex/users/export-csv/',
  IMPORT_CSV: 'cms/rex/users/bulk-create/',
  COUNTRIES_LIST: 'address/countries/',
  STATES_LIST: 'address/countries/',
  TRADTE_LIST: 'users/trades/',
  ROLE_LIST: 'cms/roles/',
  OWN_ACC_DELETE: 'cms/delete/',
  DELETE_SUBADMIN: 'cms/user/',
  ADMIN_PROFILE: 'cms/user/profile/view/',
  UPDATE_ADMIN_PROFILE: 'cms/user/profile/update/',

  // KNOWLEDGEBASE
  KNOWLEDGEBASE_UPLOAD_API: 'rag/upload-doc-knowledgebase',

  //STATICPAGE & CONTACT US
  TRADE_LIST: 'cms/trades/list/',
  TRADE_TOGGLE_STATUS: 'cms/trades/toggle-status/:id/',
  ADD_TRADE: 'cms/trades/create/',
  EDIT_TRADE: 'cms/trades/update/:id',

  //STATICPAGE & CONTACT US
  STATIC_PAGE: 'cms/static-page/',
  CONTACT_US: 'cms/contact-us/',

  //ROLE & PERMISSION
  ROLE_FEATURE_MAPPING: 'cms/role-features-mapping/',
  FEATURES: 'cms/features/',
  ROLE_BY_ID: 'cms/role/',
  USER_COUNT_BY_ROLE: 'cms/role/count/',
  DELETE_ROLE: 'cms/role/delete/',

  //LICENCE APPROVAL
  LICENCE_APPROVAL_REQUESTS: 'cms/license/requests/',
  LICENCE_APPROVAL_ACTIONS: 'cms/license/request/',

  //FORMS AND DOCUMENTS
  FETCH_FORM_LIST: 'cms/forms-documents/',
  CREATE_FORM: 'cms/forms-documents/create/',
  UPDATE_FORM: 'cms/forms-documents/update/:id/',
  DELETE_FORM: 'cms/forms-documents/delete/:id/',
  TOGGLE_FORM_STATUS: 'cms/forms-documents/toggle-active/:id/',

  //TRADIES HUB
  FETCH_TRADIES_HUB_CATEGORIES_LIST: 'cms/builder-hub/categories/',
  TOGGLE_TRADIES_HUB_CATEGORY: 'cms/builder-hub/categories/:id/status/',
  TRADIES_HUB_CATEGORY_DELETE: 'cms/builder-hub/categories/:id/delete/',
  TRADIES_HUB_CATEGORY_CREATE: 'cms/builder-hub/categories/create/',
  TRADIES_HUB_CATEGORY_UPDATE: 'cms/builder-hub/categories/:id/update/',
  TOGGLE_TRADIES_HUB_CONTENT: 'cms/builder-hub/content/toggle-status/',
  TRADIES_HUB_POST_LIST: 'cms/builder-hub/questions/',
  TRADIES_HUB_DELETE: 'cms/builder-hub/content/delete/',
  TRADIES_HUB_COMMENT_LIST: 'cms/builder-hub/questions/:id/',
  TRADIES_HUB_REPLY_LIST: 'cms/builder-hub/comments/:id/',
  TRADIES_HUB_REPORT_LIST: 'cms/builder-hub/report/list/',
  TRADIES_HUB_POST_REPORT_LIST: 'cms/builder-hub/posts/:id/reports/',
  TRADIES_HUB_COMMENT_REPORT_LIST: 'cms/builder-hub/reports/comments/:id/',
  TRADIES_HUB_REPLY_REPORT_LIST: 'cms/builder-hub/reports/reply/:id/',
  TRADIES_HUB_REPORT_DELETE: 'cms/builder-hub/reports/:id/action/',

  //SUBSCRIPTION
  SUBCRIPTION_LIST: 'payment/product/list/',
  SUBCRIPTION_EDIT: 'payment/subscription/features/edit/',


  //TRANSACTION HISTORY
  VIDEO_TRANSACTION_HISTORY: 'cms/training-education/transactions/',

  // ANALYTICS
  USER_ANALYTICS: 'cms/analytics_data/user-analytics/?time_range=',
  STATE_WISE_UPLOAD_DOCS:
    'cms/analytics_data/state-wise-upload-document-analytics/?time_range=',
  STATE_WISE_QUESTIONS_ASKED:
    'cms/analytics_data/state-wise-questions-asked-analytics/?time_range=',
  CATEGORY_WISE_QUESTIONS_ASKED:
    'cms/analytics_data/category-wise-questions-asked-analytics/?time_range=',
  CATEGORY_WISE_ISSUES:
    'cms/analytics_data/category-wise-issue-counts-analytics/?time_range=',

  RECTIFICATION_ANALYTICS:
    'cms/analytics_data/rectification-wise-issue-counts-analytics/?time_range=',

  LIST_ISSUES: 'cms/analytics_data/list-issues-category-wise/?category_id=',
  VIEW_ISSUES: 'cms/analytics_data/issue-solution-detail/?question_id=',
  LIST_CHATS: 'cms/analytics_data/list-chat-category-wise/?category_id=',
  VIEW_CHATS: 'cms/analytics_data/chat-solution-detail/?',
  REC_ISSUES:
    'cms/analytics_data/list-issues-rectification-type-wise/?rectification_type=',
  //rectification_type=immediate&page=1&search=The Lounge wall

  NEW_NOTIFICATION_TEMPLATE: 'config/notification-templates/',
  GET_NOTIFICATION_TEMPLATE: 'config/notification-templates/',
  EDIT_NOTIFICATION_TEMPLATE: 'config/notification-templates/',
  DELETE_NOTIFICATION_TEMPLATE: 'config/notification-templates/<template_id>/',
  GET_NOTIFICATION_TEMPLATE_DETAIL:
    'config/notification-templates/<int:template_id>/',

  GET_NOTIFICATION_TYPE: 'config/list-notification-type/',

  SEND_NOTIFICATION: 'config/push-notification/',
};

export const MODULE_KEY = {
  ADMINS: 'admin-management',
  KNOWLEDGEBASE: 'knowledge-base',
  USERS: 'user-management',
  CONTACT_REQUEST: 'contact-request',
  LICENCE_APPROVAL: 'licence-approval',
  STATIC_PAGES: 'static-pages',
  SUBSCRIPTION: 'subscription-management',
  NOTIFICATION: 'notification-templates',
  TRADIES_HUB: 'tradie-hub',
  TRAINING_EDUCATION: 'training-education',
};