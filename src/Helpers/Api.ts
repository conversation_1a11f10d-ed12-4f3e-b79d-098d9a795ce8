import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { API_PATHS } from './Constants';
import queryClient from './QueryClient';

// Define types for the data parameter in methods
interface ApiData {
  params?: Record<string, unknown>;
  data?: Record<string, unknown> | FormData;
  headers?: Record<string, string>;
  isMultipart?: boolean;
  skipAuth?: boolean;
  skipErrorHandling?: boolean;
}

// Enum for HTTP methods
enum MethodSubType {
  GET = 'get',
  POST = 'post',
  PUT = 'put',
  DELETE = 'delete',
  PATCH = 'patch',
}

interface ErrorResponse {
  is_logout: boolean;
  message: string;
  code?: number; // Optional error code
  details?: string; // Optional additional details
}

type Method = MethodSubType;

const BASEURL = import.meta.env.VITE_APP_API_URL; // Ensure this is set correctly in your env.

class Api {
  private isLoggedIn: boolean;
  private authToken?: string;
  private baseURL: string;

  constructor() {
    this.baseURL = BASEURL || '';
    this.isLoggedIn = this.getAuthenticationInfo();
  }

  private getAuthenticationInfo(): boolean {
    const isLoggedIn = localStorage.getItem('authaccess');
    if (isLoggedIn) {
      this.authToken = localStorage.getItem('authaccess') || undefined;
      return true;
    }
    return false;
  }

  public get(url: string, data?: ApiData): Promise<AxiosResponse> {
    return this.api(MethodSubType.GET, url, data);
  }

  public post(url: string, data?: ApiData): Promise<AxiosResponse> {
    return this.api(MethodSubType.POST, url, data);
  }

  public put(url: string, data?: ApiData): Promise<AxiosResponse> {
    return this.api(MethodSubType.PUT, url, data);
  }

  public patch(url: string, data?: ApiData): Promise<AxiosResponse> {
    return this.api(MethodSubType.PATCH, url, data);
  }

  public delete(url: string, data?: ApiData): Promise<AxiosResponse> {
    return this.api(MethodSubType.DELETE, url, data);
  }

  public async refetchRefreshToken(): Promise<string | null> {
    try {
      const response = await axios.post(BASEURL + API_PATHS.NEW_REFRESH_TOKEN);

      // Destructure and validate response structure
      const accessToken = response?.data?.data?.access_token;
      if (!accessToken) {
        throw new Error(
          response?.data?.message || 'Invalid response structure'
        );
      }
      localStorage.setItem('authaccess', accessToken);
      window.location.reload();
      return accessToken;
    } catch (error) {
      if (
        error instanceof AxiosError &&
        error.response?.status === 403 &&
        error?.response?.data?.is_logout
      ) {
        // Handle 403 error specifically
        window.location.href = '/login';
      }
      // General error handling
      console.error('Error fetching refresh token:', error);
      // Redirect to login if an error occurs
      window.location.href = '/login';

      return null;
    }
  }

  private api(
    method: Method,
    url: string,
    data?: ApiData
  ): Promise<AxiosResponse> {
    const axiosConfig: AxiosRequestConfig = {
      method,
      url: `${this.baseURL}${url}`,
      headers: this.setHeaders(data),
      withCredentials: true,
      timeout: 300000,
    };

    // Ensure request body (`data.data`) is correctly assigned for POST, PUT, DELETE
    if (
      method === MethodSubType.POST ||
      method === MethodSubType.PUT ||
      method === MethodSubType.DELETE ||
      method === MethodSubType.PATCH
    ) {
      axiosConfig.data = data?.data || {}; // Ensure body is always passed, even if empty
    } else if (data?.params) {
      axiosConfig.params = data.params; // Only set params for GET requests
    }

    return axios(axiosConfig)
      .then((response: AxiosResponse) => {
        if (
          response.status === 200 ||
          response.status === 201 ||
          response.status === 207
        ) {
          return response;
        }
        throw new Error(`Unexpected status code: ${response.status}`);
      })
      .catch(async (error: AxiosError<ErrorResponse>) => {
        const errorMessage =
          error.response?.data?.message ||
          'An unexpected error occurred. Please try again';
        if (error.response?.status === 401) {
          localStorage.removeItem('authaccess');

          // // Attempt to refresh token
          const newToken = await this.refetchRefreshToken();
          if (newToken) {
            axiosConfig.headers = {
              ...axiosConfig.headers,
              Authorization: `Bearer ${newToken}`,
            };
            return axios(axiosConfig);
          } else {
            queryClient.clear();
            window.location.href = '/login';
            return Promise.reject(
              'Authentication failed, redirecting to login.'
            );
          }
        }

        if (!data?.skipErrorHandling) {
          console.error(errorMessage);
        }

        return Promise.reject(errorMessage);
      });
  }

  private setHeaders(data?: ApiData): Record<string, string> {
    const headers: Record<string, string> = {
      'Accept-Language': 'en',
      'Content-Type': 'application/json',
    };

    // Check if the request is multipart
    if (data?.isMultipart) {
      headers['Content-Type'] = 'multipart/form-data';
    }

    if (data?.headers) {
      Object.assign(headers, data.headers);
    }

    // Always fetch the latest token from localStorage
    const token = localStorage.getItem('authaccess');

    if (token && !data?.skipAuth) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
  }
}

export default Api;
