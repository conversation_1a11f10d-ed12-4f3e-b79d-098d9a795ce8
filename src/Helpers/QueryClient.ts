import { QueryClient } from 'react-query';

// Create a custom queryClient instance with optimized settings
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Cache data for 5 minutes (adjustable as per your use case)
      staleTime: 1000 * 60 * 5, // 5 minutes
      cacheTime: 1000 * 60 * 60 * 24, // Cache for 1 day before eviction
      retry: 2, // Retry failed queries 2 times
      retryDelay: (attemptIndex: number) =>
        Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff retry delay
      refetchOnWindowFocus: true, // Refetch when the window is focused (common for fresh data)
      refetchOnReconnect: true, // Refetch on reconnect (good for background data sync)
      refetchInterval: false, // No background polling unless you set it
      suspense: false, // Set to true if you prefer React Suspense for data fetching
    },
  },
});

export default queryClient;
