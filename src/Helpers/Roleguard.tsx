import { Navigate, Outlet } from 'react-router';

interface RoleGuardProps {
  redirectPath?: string;
  isPublicRoute?: boolean;
}

export const RoleGuard: React.FC<RoleGuardProps> = ({
  redirectPath = '/login',
  isPublicRoute = false,
}) => {
  const accessToken = localStorage.getItem('authaccess');

  // If user is logged in, prevent access to public routes (e.g., login)
  if (isPublicRoute && accessToken) {
    return <Navigate to="/" replace />;
  }

  // If user is NOT logged in, protect private routes
  if (!isPublicRoute && !accessToken) {
    return <Navigate to={redirectPath} replace />;
  }

  return <Outlet />;
};
