import { defineConfig } from 'vite';
import tailwindcss from '@tailwindcss/vite';
import react from '@vitejs/plugin-react-swc';
import path from 'path';

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss()],
  // server: {
  //   allowedHosts: ['7abf-203-88-135-90.ngrok-free.app'], // Allow your ngrok domain
  //   host: true, // Ensure the server is accessible externally
  // },
  resolve: {
    alias: {
      '@Components': path.resolve(__dirname, 'src/Components'),
      '@LanguageProvider': path.resolve(__dirname, 'src/LanguageProvider'),
      '@Assets': path.resolve(__dirname, 'src/Assets'),
      '@Helpers': path.resolve(__dirname, 'src/Helpers'),
      '@Hooks': path.resolve(__dirname, 'src/Hooks'),
      '@Query': path.resolve(__dirname, 'src/Query'),
      '@Redux': path.resolve(__dirname, 'src/Redux'),
      '@Routes': path.resolve(__dirname, 'src/Routes'),
      '@Pages': path.resolve(__dirname, 'src/Pages'),
      '@Icons': path.resolve(__dirname, 'src/Icons'),
      '@Config': path.resolve(__dirname, 'src/Config'),
    },
  },
});
